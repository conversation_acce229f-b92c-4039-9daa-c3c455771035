// AI-generated code: Bot chat widget controller
import { Controller } from "@hotwired/stimulus"

export default class extends Controller {
  static targets = ["widget", "messages", "input", "sendButton", "minimizeButton"]
  static values = { conversationId: String }

  connect() {
    this.isMinimized = true
    this.conversationIdValue = null
    this.setupEventListeners()
  }

  setupEventListeners() {
    // Handle Enter key in input
    this.inputTarget.addEventListener('keypress', (e) => {
      if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault()
        this.sendMessage()
      }
    })
  }

  toggleWidget() {
    this.isMinimized = !this.isMinimized
    
    if (this.isMinimized) {
      this.widgetTarget.classList.add('translate-y-full')
      this.minimizeButtonTarget.innerHTML = `
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.013 8.013 0 01-7-4c0-4.418 3.582-8 8-8s8 3.582 8 8z"></path>
        </svg>
      `
    } else {
      this.widgetTarget.classList.remove('translate-y-full')
      this.minimizeButtonTarget.innerHTML = `
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      `
      
      // Focus input when opening
      setTimeout(() => {
        this.inputTarget.focus()
      }, 100)
      
      // Load initial greeting if no conversation exists
      if (!this.conversationIdValue && this.messagesTarget.children.length === 0) {
        this.loadInitialGreeting()
      }
    }
  }

  async loadInitialGreeting() {
    this.addBotMessage("Hi! I'm Ofie Assistant. I'm here to help you with any questions about our rental platform. How can I assist you today?")
  }

  async sendMessage() {
    const message = this.inputTarget.value.trim()
    if (!message) return

    // Add user message to chat
    this.addUserMessage(message)
    this.inputTarget.value = ''
    this.sendButtonTarget.disabled = true
    this.sendButtonTarget.innerHTML = 'Sending...'

    try {
      const response = await this.sendToBotAPI(message)
      
      if (response.ok) {
        const data = await response.json()
        this.conversationIdValue = data.conversation_id
        this.addBotMessage(data.message.content)
      } else {
        this.addBotMessage("Sorry, I'm having trouble responding right now. Please try again later.")
      }
    } catch (error) {
      console.error('Bot chat error:', error)
      this.addBotMessage("Sorry, I'm having trouble responding right now. Please try again later.")
    } finally {
      this.sendButtonTarget.disabled = false
      this.sendButtonTarget.innerHTML = 'Send'
    }
  }

  async sendToBotAPI(message) {
    const token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
    
    const payload = {
      query: message
    }
    
    if (this.conversationIdValue) {
      payload.conversation_id = this.conversationIdValue
    }

    return fetch('/api/v1/bot/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': token
      },
      body: JSON.stringify(payload)
    })
  }

  addUserMessage(message) {
    const messageElement = this.createMessageElement(message, 'user')
    this.messagesTarget.appendChild(messageElement)
    this.scrollToBottom()
  }

  addBotMessage(message) {
    const messageElement = this.createMessageElement(message, 'bot')
    this.messagesTarget.appendChild(messageElement)
    this.scrollToBottom()
  }

  createMessageElement(message, sender) {
    const messageDiv = document.createElement('div')
    messageDiv.className = `flex ${sender === 'user' ? 'justify-end' : 'justify-start'} mb-4`
    
    const isBot = sender === 'bot'
    const bgColor = isBot ? 'bg-gray-100' : 'bg-emerald-600'
    const textColor = isBot ? 'text-gray-800' : 'text-white'
    const alignment = isBot ? 'rounded-br-lg' : 'rounded-bl-lg'
    
    messageDiv.innerHTML = `
      <div class="max-w-xs lg:max-w-md px-4 py-2 ${bgColor} ${textColor} rounded-lg ${alignment} rounded-t-lg">
        ${isBot ? '<div class="flex items-center mb-1"><span class="text-xs font-semibold text-emerald-600">Ofie Assistant</span></div>' : ''}
        <p class="text-sm">${this.escapeHtml(message)}</p>
        <div class="text-xs opacity-75 mt-1">${new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</div>
      </div>
    `
    
    return messageDiv
  }

  escapeHtml(text) {
    const div = document.createElement('div')
    div.textContent = text
    return div.innerHTML
  }

  scrollToBottom() {
    setTimeout(() => {
      this.messagesTarget.scrollTop = this.messagesTarget.scrollHeight
    }, 100)
  }
}