# Streamlined Bot controller for handling bot interactions
class Api::BotController < ApplicationController
  # Allow bot chat for both authenticated and non-authenticated users
  before_action :authenticate_request, except: [:chat, :suggestions]
  before_action :set_bot

  # POST /api/bot/chat
  def chat
    query = params[:query]
    conversation_id = params[:conversation_id]

    if query.blank?
      return render json: { error: 'Query cannot be blank' }, status: :bad_request
    end

    # For non-authenticated users, provide simple responses
    if current_user.nil?
      response = handle_guest_query(query)
      return render json: {
        conversation_id: nil,
        message: {
          id: SecureRandom.uuid,
          content: response,
          sender: {
            id: 'bot',
            name: 'Ofie Assistant',
            role: 'bot'
          },
          created_at: Time.current
        },
        intent: 'guest_help',
        quick_actions: guest_quick_actions,
        confidence: 0.8
      }
    end

    # For authenticated users, use full conversation system
    conversation = find_or_create_conversation(conversation_id)

    # Process the query with BotService
    bot_response = BotService.new(
      user: current_user,
      query: query,
      conversation: conversation
    ).process_query

    # Create the bot's response message
    message = create_bot_message(conversation, bot_response[:response])

    render json: {
      conversation_id: conversation.id,
      message: {
        id: message.id,
        content: message.content,
        sender: {
          id: @bot.id,
          name: @bot.name,
          role: @bot.role
        },
        created_at: message.created_at
      },
      intent: bot_response[:intent],
      quick_actions: bot_response[:quick_actions],
      confidence: bot_response[:confidence]
    }
  rescue => e
    Rails.logger.error "Bot chat error: #{e.message}"
    render json: { error: 'Something went wrong. Please try again.' }, status: :internal_server_error
  end
  
  # POST /api/bot/start_conversation
  def start_conversation
    property_id = params[:property_id]
    initial_message = params[:message]
    
    property = Property.find(property_id) if property_id.present?
    
    # Create conversation between user and bot
    conversation = create_bot_conversation(property, initial_message)
    
    if conversation
      render json: {
        conversation_id: conversation.id,
        message: 'Conversation started with Ofie Assistant',
        redirect_url: conversation_path(conversation)
      }
    else
      render json: { error: 'Failed to start conversation' }, status: :unprocessable_entity
    end
  end
  
  # GET /api/bot/suggestions
  def suggestions
    if current_user.nil?
      suggestions = [
        'How does Ofie work?',
        'What services do you offer?',
        'How do I get started?',
        'Browse properties',
        'Contact support'
      ]
    else
      user_type = current_user.role

      suggestions = case user_type
      when 'tenant'
        [
          'Find 2-bedroom apartments under $2000',
          'How do I apply for a rental?',
          'What documents do I need?',
          'How do I schedule a viewing?',
          'How do I submit a maintenance request?',
          'How do I pay my rent online?'
        ]
      when 'landlord'
        [
          'How do I list a new property?',
          'How do I review rental applications?',
          'How do I manage maintenance requests?',
          'How do I track rental payments?',
          'What are the platform fees?',
          'How do I communicate with tenants?'
        ]
      else
        [
          'How does the platform work?',
          'What services do you offer?',
          'How do I get started?',
          'Contact support'
        ]
      end
    end

    render json: { suggestions: suggestions }
  end
  
  # GET /api/bot/faqs
  def faqs
    render json: {
      faqs: KnowledgeBase.faqs,
      tips: current_user.tenant? ? KnowledgeBase.tenant_tips : KnowledgeBase.landlord_tips
    }
  end
  
  # POST /api/bot/feedback
  def feedback
    message_id = params[:message_id]
    rating = params[:rating] # 1-5 or thumbs up/down
    comment = params[:comment]
    
    # Log feedback for bot improvement
    Rails.logger.info "Bot feedback - Message: #{message_id}, Rating: #{rating}, Comment: #{comment}"
    
    # Here you could store feedback in a database table for analysis
    
    render json: { message: 'Thank you for your feedback!' }
  end
  
  private
  
  def set_bot
    @bot = Bot.primary_bot
    
    unless @bot
      render json: { error: 'Bot service unavailable' }, status: :service_unavailable
    end
  end
  
  def find_or_create_conversation(conversation_id)
    if conversation_id.present?
      conversation = Conversation.find_by(id: conversation_id)
      return conversation if conversation && conversation_participant?(conversation)
    end
    
    # Create new conversation with bot
    create_bot_conversation
  end
  
  def create_bot_conversation(property = nil, initial_message = nil)
    # Determine conversation participants
    if current_user.tenant?
      landlord = @bot
      tenant = current_user
    else
      landlord = current_user
      tenant = @bot
    end
    
    # Use a default property if none specified
    property ||= Property.active.first
    
    conversation = Conversation.create!(
      landlord: landlord,
      tenant: tenant,
      property: property,
      subject: "Chat with Ofie Assistant",
      status: "active"
    )
    
    # Create initial bot greeting if no initial message
    if initial_message.blank?
      bot_response = BotService.new(
        user: current_user,
        query: '',
        conversation: conversation
      ).process_query
      
      create_bot_message(conversation, bot_response[:response])
    else
      # Create user's initial message first
      Message.create!(
        conversation: conversation,
        sender: current_user,
        content: initial_message,
        message_type: 'text'
      )
      
      # Then create bot response
      bot_response = BotService.new(
        user: current_user,
        query: initial_message,
        conversation: conversation
      ).process_query
      
      create_bot_message(conversation, bot_response[:response])
    end
    
    conversation
  rescue => e
    Rails.logger.error "Failed to create bot conversation: #{e.message}"
    nil
  end
  
  def create_bot_message(conversation, content)
    Message.create!(
      conversation: conversation,
      sender: @bot,
      content: content,
      message_type: 'text'
    )
  end
  
  def conversation_participant?(conversation)
    conversation.landlord_id == current_user.id || conversation.tenant_id == current_user.id
  end
end