/* Ofie Design System - Consistent UI Components */

/* ===== CORE DESIGN TOKENS ===== */

:root {
  /* Glass Morphism */
  --glass-bg: rgba(255, 255, 255, 0.8);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  
  /* Gradients */
  --gradient-primary: linear-gradient(135deg, #3b82f6, #6366f1);
  --gradient-success: linear-gradient(135deg, #10b981, #059669);
  --gradient-warning: linear-gradient(135deg, #f59e0b, #ea580c);
  --gradient-error: linear-gradient(135deg, #ef4444, #ec4899);
  --gradient-background: linear-gradient(135deg, #f8fafc, #dbeafe, #e0e7ff);
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  
  /* Border Radius */
  --radius-sm: 0.5rem;
  --radius-md: 1rem;
  --radius-lg: 1.5rem;
  --radius-xl: 2rem;
  --radius-2xl: 3rem;
  
  /* Spacing */
  --space-xs: 0.5rem;
  --space-sm: 1rem;
  --space-md: 1.5rem;
  --space-lg: 2rem;
  --space-xl: 3rem;
}

/* ===== GLASS MORPHISM COMPONENTS ===== */

.glass-card {
  background: var(--glass-bg);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-xl), 0 0 0 1px rgba(255, 255, 255, 0.05);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.glass-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-2xl), 0 0 0 1px rgba(255, 255, 255, 0.1);
}

.glass-modal {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-2xl);
}

.glass-input {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 2px solid #e5e7eb;
  border-radius: var(--radius-lg);
  transition: all 0.3s ease;
}

.glass-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
}

.glass-input:hover {
  border-color: #9ca3af;
}

/* ===== BUTTON SYSTEM ===== */

.btn-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  border-radius: var(--radius-lg);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: none;
  text-decoration: none;
}

.btn-base:hover {
  transform: translateY(-2px);
}

.btn-primary {
  background: var(--gradient-primary);
  color: white;
  padding: 1rem 2rem;
  box-shadow: var(--shadow-lg), 0 0 0 1px rgba(59, 130, 246, 0.2);
}

.btn-primary:hover {
  box-shadow: var(--shadow-xl), 0 0 0 1px rgba(59, 130, 246, 0.3);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(8px);
  color: #374151;
  padding: 1rem 2rem;
  border: 2px solid #e5e7eb;
}

.btn-secondary:hover {
  background: rgba(255, 255, 255, 0.95);
  border-color: #9ca3af;
}

.btn-success {
  background: var(--gradient-success);
  color: white;
  padding: 1rem 2rem;
  box-shadow: var(--shadow-lg), 0 0 0 1px rgba(16, 185, 129, 0.2);
}

.btn-warning {
  background: var(--gradient-warning);
  color: white;
  padding: 1rem 2rem;
  box-shadow: var(--shadow-lg), 0 0 0 1px rgba(245, 158, 11, 0.2);
}

.btn-error {
  background: var(--gradient-error);
  color: white;
  padding: 1rem 2rem;
  box-shadow: var(--shadow-lg), 0 0 0 1px rgba(239, 68, 68, 0.2);
}

/* ===== FORM COMPONENTS ===== */

.form-group {
  margin-bottom: var(--space-md);
}

.form-label {
  display: block;
  font-weight: 700;
  color: #374151;
  margin-bottom: var(--space-xs);
  font-size: 0.875rem;
}

.form-label svg {
  display: inline;
  width: 1rem;
  height: 1rem;
  margin-right: var(--space-xs);
}

.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid #e5e7eb;
  border-radius: var(--radius-lg);
  font-weight: 500;
  transition: all 0.3s ease;
}

.form-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
}

.form-input:hover {
  border-color: #9ca3af;
}

.form-input.error {
  border-color: #ef4444;
}

.form-input.error:focus {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-input.success {
  border-color: #10b981;
}

.form-input.success:focus {
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* ===== ICON CONTAINERS ===== */

.icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-lg);
  transition: all 0.3s ease;
}

.icon-container.primary {
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-lg), 0 0 0 1px rgba(59, 130, 246, 0.2);
}

.icon-container.success {
  background: var(--gradient-success);
  color: white;
  box-shadow: var(--shadow-lg), 0 0 0 1px rgba(16, 185, 129, 0.2);
}

.icon-container.warning {
  background: var(--gradient-warning);
  color: white;
  box-shadow: var(--shadow-lg), 0 0 0 1px rgba(245, 158, 11, 0.2);
}

.icon-container.error {
  background: var(--gradient-error);
  color: white;
  box-shadow: var(--shadow-lg), 0 0 0 1px rgba(239, 68, 68, 0.2);
}

/* ===== BACKGROUND PATTERNS ===== */

.bg-pattern {
  background-image: url("data:image/svg+xml,%3csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3e%3cg fill='%23f1f5f9' fill-opacity='0.3' fill-rule='evenodd'%3e%3cpath d='m0 40 40-40h-40v40zm40 0v-40h-40z'/%3e%3c/g%3e%3c/svg%3e");
}

.bg-gradient-main {
  background: var(--gradient-background);
}

/* ===== ANIMATIONS ===== */

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-slide-up {
  animation: slideInUp 0.6s ease-out;
}

.animate-fade-in {
  animation: fadeIn 0.4s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.3s ease-out;
}

/* ===== RESPONSIVE UTILITIES ===== */

@media (max-width: 640px) {
  .glass-card {
    border-radius: var(--radius-lg);
    margin: var(--space-sm);
  }
  
  .btn-base {
    padding: 0.75rem 1.5rem;
    font-size: 0.875rem;
  }
  
  .form-input {
    font-size: 1rem; /* Prevents zoom on iOS */
  }
}

/* ===== ACCESSIBILITY ===== */

@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .glass-card {
    border: 2px solid #000;
    background: rgba(255, 255, 255, 0.95);
  }
  
  .btn-primary {
    border: 2px solid #000;
  }
}
