/* Enhanced Navbar Styles */

/* Smooth navbar transitions */
nav {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced scrolled state */
nav.shadow-xl {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Logo animation */
.logo-container {
  transition: transform 0.2s ease-in-out;
}

.logo-container:hover {
  transform: scale(1.05);
}

/* Search input enhancements */
.search-input {
  transition: all 0.2s ease-in-out;
}

.search-input:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Button hover effects */
.nav-button {
  position: relative;
  overflow: hidden;
}

.nav-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.nav-button:hover::before {
  left: 100%;
}

/* Notification badges */
.notification-badge {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

/* Dropdown animations */
.dropdown-menu {
  transform: translateY(-10px);
  opacity: 0;
  transition: all 0.2s ease-out;
}

.dropdown-menu:not(.hidden) {
  transform: translateY(0);
  opacity: 1;
}

/* Mobile menu animations */
.mobile-menu {
  transform: translateX(-100%);
  transition: transform 0.3s ease-in-out;
}

.mobile-menu:not(.hidden) {
  transform: translateX(0);
}

/* Focus visible improvements */
.focus-visible:focus-visible {
  outline: 2px solid #3B82F6;
  outline-offset: 2px;
}

/* Improved button states */
.btn-primary {
  background: linear-gradient(135deg, #3B82F6 0%, #1D4ED8 100%);
  transition: all 0.2s ease-in-out;
  position: relative;
  overflow: hidden;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #1D4ED8 0%, #1E3A8A 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.btn-primary:active {
  transform: translateY(0);
}

/* User avatar improvements */
.user-avatar {
  transition: all 0.2s ease-in-out;
}

.user-avatar:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Responsive improvements */
@media (max-width: 768px) {
  .mobile-menu {
    max-height: calc(100vh - 64px);
    overflow-y: auto;
  }
  
  .mobile-search-input {
    font-size: 16px; /* Prevents zoom on iOS */
  }
}

/* Dark mode support (if needed) */
@media (prefers-color-scheme: dark) {
  nav {
    background: rgba(17, 24, 39, 0.95);
    border-color: rgba(75, 85, 99, 0.3);
  }
  
  .nav-link {
    color: #E5E7EB;
  }
  
  .nav-link:hover {
    color: #60A5FA;
    background: rgba(37, 99, 235, 0.1);
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .nav-link {
    border: 1px solid transparent;
  }
  
  .nav-link:focus {
    border-color: currentColor;
  }
}
