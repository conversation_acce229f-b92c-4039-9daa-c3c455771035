/* Enhanced Landing Page Styles */

/* Floating animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes float-delayed {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-30px);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-delayed {
  animation: float-delayed 8s ease-in-out infinite 2s;
}

/* Gradient animation */
@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient 3s ease infinite;
}

/* Counter animation */
@keyframes countUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.counter {
  animation: countUp 1s ease-out;
}

/* Hover effects */
.property-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.property-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Pulse effect for trending badges */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(239, 68, 68, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(239, 68, 68, 0.8), 0 0 30px rgba(239, 68, 68, 0.6);
  }
}

.animate-pulse {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #3b82f6, #8b5cf6);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #2563eb, #7c3aed);
}

/* Loading animation for images */
.image-loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Responsive typography */
@media (max-width: 640px) {
  .hero-title {
    font-size: 2.5rem;
    line-height: 1.1;
  }
  
  .hero-subtitle {
    font-size: 1.125rem;
  }
}

/* Focus states for accessibility */
.focus-ring:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Button hover effects */
.btn-primary {
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-primary:hover::before {
  left: 100%;
}

/* Enhanced Shadow and Glow Effects */
.group:hover {
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* High Contrast Text Shadows */
.drop-shadow-lg {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.8));
}

.drop-shadow-md {
  filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.6));
}

/* Enhanced Border Glow */
.border-white\/20 {
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* Trending Badge Enhanced Pulse */
.animate-pulse {
  animation: enhancedPulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes enhancedPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

/* Card reveal animation with stagger effect */
@keyframes cardReveal {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.group {
  animation: cardReveal 0.6s ease-out forwards;
}

.group:nth-child(1) { animation-delay: 0.1s; }
.group:nth-child(2) { animation-delay: 0.2s; }
.group:nth-child(3) { animation-delay: 0.3s; }
.group:nth-child(4) { animation-delay: 0.4s; }
.group:nth-child(5) { animation-delay: 0.5s; }
.group:nth-child(6) { animation-delay: 0.6s; }

.card-reveal {
  opacity: 0;
  transform: translateY(30px);
  animation: cardReveal 0.6s ease-out forwards;
}

/* Enhanced Property Cards with High Contrast */
.property-overlay {
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(8px) saturate(150%);
  -webkit-backdrop-filter: blur(8px) saturate(150%);
}

.group:hover .property-overlay {
  transform: translateY(0);
  opacity: 1;
}

/* Staggered Animation for Property Stats */
.property-stats > div {
  transform: translateY(30px) scale(0.9);
  opacity: 0;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.group:hover .property-stats > div:nth-child(1) {
  transform: translateY(0) scale(1);
  opacity: 1;
  transition-delay: 0.1s;
}

.group:hover .property-stats > div:nth-child(2) {
  transform: translateY(0) scale(1);
  opacity: 1;
  transition-delay: 0.2s;
}

.group:hover .property-stats > div:nth-child(3) {
  transform: translateY(0) scale(1);
  opacity: 1;
  transition-delay: 0.3s;
}

/* Amenities animation */
.amenity-tag {
  transform: scale(0.8);
  opacity: 0;
  transition: all 0.2s ease-out;
}

.group:hover .amenity-tag {
  transform: scale(1);
  opacity: 1;
}

.group:hover .amenity-tag:nth-child(1) { transition-delay: 0.1s; }
.group:hover .amenity-tag:nth-child(2) { transition-delay: 0.15s; }
.group:hover .amenity-tag:nth-child(3) { transition-delay: 0.2s; }
.group:hover .amenity-tag:nth-child(4) { transition-delay: 0.25s; }

/* Enhanced Property Image Effects */
.property-image {
  transition: transform 0.8s cubic-bezier(0.4, 0, 0.2, 1), filter 0.5s ease;
  filter: brightness(1) contrast(1);
}

.group:hover .property-image {
  transform: scale(1.15);
  filter: brightness(1.1) contrast(1.2);
}

/* High Contrast Backdrop Blur */
.backdrop-blur-enhanced {
  backdrop-filter: blur(12px) saturate(180%) brightness(1.1);
  -webkit-backdrop-filter: blur(12px) saturate(180%) brightness(1.1);
}

/* Enhanced Price Badge with Contrast */
.price-badge {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.3));
}

.group:hover .price-badge {
  transform: translateY(8px) scale(1.05);
  filter: drop-shadow(0 6px 16px rgba(0, 0, 0, 0.4));
}

/* Enhanced Heart Button with Pulse Effect */
.heart-button {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.2));
}

.heart-button:hover {
  transform: scale(1.15);
  filter: drop-shadow(0 4px 16px rgba(239, 68, 68, 0.4));
  animation: heartPulse 1.5s infinite;
}

@keyframes heartPulse {
  0%, 100% { transform: scale(1.15); }
  50% { transform: scale(1.25); }
}

/* Action Button Enhanced Animation */
.action-buttons {
  transform: translateY(25px) scale(0.95);
  opacity: 0;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.group:hover .action-buttons {
  transform: translateY(0) scale(1);
  opacity: 1;
  transition-delay: 0.4s;
}

/* Stagger animation for cards */
.card-reveal:nth-child(1) { animation-delay: 0.1s; }
.card-reveal:nth-child(2) { animation-delay: 0.2s; }
.card-reveal:nth-child(3) { animation-delay: 0.3s; }
.card-reveal:nth-child(4) { animation-delay: 0.4s; }
.card-reveal:nth-child(5) { animation-delay: 0.5s; }
.card-reveal:nth-child(6) { animation-delay: 0.6s; }