/* Ofie <PERSON> Beautiful Styling */

/* Base Bot Styles */
.ofie-bot-container {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  --bot-primary-color: #3b82f6;
  --bot-secondary-color: #8b5cf6;
  --bot-success-color: #10b981;
  --bot-danger-color: #ef4444;
  --bot-warning-color: #f59e0b;
  --bot-gray-50: #f9fafb;
  --bot-gray-100: #f3f4f6;
  --bot-gray-200: #e5e7eb;
  --bot-gray-300: #d1d5db;
  --bot-gray-400: #9ca3af;
  --bot-gray-500: #6b7280;
  --bot-gray-600: #4b5563;
  --bot-gray-700: #374151;
  --bot-gray-800: #1f2937;
  --bot-gray-900: #111827;
}

/* Floating Bot Widget Animations */
@keyframes bot-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.9;
  }
}

@keyframes bot-bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -10px, 0);
  }
  70% {
    transform: translate3d(0, -5px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes bot-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(139, 92, 246, 0.5);
  }
}

@keyframes bot-typing-dot {
  0%, 60%, 100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}

/* Bot Button Styles */
.bot-button {
  position: relative;
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--bot-primary-color) 0%, var(--bot-secondary-color) 50%, var(--bot-primary-color) 100%);
  border: none;
  cursor: pointer;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.bot-button:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow: 0 12px 40px rgba(59, 130, 246, 0.4);
  animation: bot-glow 2s ease-in-out infinite;
}

.bot-button:active {
  transform: translateY(-2px) scale(0.98);
}

/* Chat Container Styles */
.bot-chat-container {
  width: 384px;
  height: 600px;
  max-height: 80vh;
  max-width: 90vw;
  background: white;
  border-radius: 16px;
  box-shadow: 
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Chat Header Styles */
.bot-chat-header {
  background: linear-gradient(135deg, var(--bot-primary-color) 0%, var(--bot-secondary-color) 100%);
  color: white;
  padding: 16px;
  position: relative;
  overflow: hidden;
}

.bot-chat-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

/* Message Styles */
.bot-message {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
  animation: message-slide-in 0.3s ease-out;
}

.user-message {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 16px;
  flex-direction: row-reverse;
  animation: message-slide-in-right 0.3s ease-out;
}

@keyframes message-slide-in {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes message-slide-in-right {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.bot-message-bubble {
  background: white;
  border: 1px solid var(--bot-gray-200);
  border-radius: 18px;
  border-bottom-left-radius: 4px;
  padding: 12px 16px;
  max-width: 280px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
}

.user-message-bubble {
  background: linear-gradient(135deg, var(--bot-primary-color) 0%, var(--bot-secondary-color) 100%);
  color: white;
  border-radius: 18px;
  border-bottom-right-radius: 4px;
  padding: 12px 16px;
  max-width: 280px;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

/* Avatar Styles */
.bot-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--bot-primary-color) 0%, var(--bot-secondary-color) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--bot-gray-300);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
  overflow: hidden;
}

/* Typing Indicator */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 12px 16px;
}

.typing-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--bot-primary-color);
  animation: bot-typing-dot 1.5s ease-in-out infinite;
}

.typing-dot:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0.4s;
}

/* Smart Actions */
.smart-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin: 8px 0;
}

.smart-action-button {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: var(--bot-gray-50);
  border: 1px solid var(--bot-gray-200);
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  color: var(--bot-gray-700);
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.smart-action-button:hover {
  background: var(--bot-primary-color);
  color: white;
  border-color: var(--bot-primary-color);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

/* Conversation Suggestions */
.conversation-suggestions {
  margin: 12px 0;
}

.suggestion-button {
  display: block;
  width: 100%;
  text-align: left;
  padding: 12px 16px;
  margin-bottom: 8px;
  background: linear-gradient(135deg, var(--bot-gray-50) 0%, rgba(59, 130, 246, 0.05) 100%);
  border: 1px solid var(--bot-gray-200);
  border-radius: 12px;
  font-size: 14px;
  color: var(--bot-gray-700);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.suggestion-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  transition: left 0.5s ease;
}

.suggestion-button:hover::before {
  left: 100%;
}

.suggestion-button:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
  border-color: var(--bot-primary-color);
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

/* Message Input */
.message-input-container {
  padding: 16px;
  background: white;
  border-top: 1px solid var(--bot-gray-200);
}

.message-input {
  display: flex;
  align-items: flex-end;
  gap: 8px;
}

.message-textarea {
  flex: 1;
  min-height: 44px;
  max-height: 120px;
  padding: 12px 16px;
  border: 1px solid var(--bot-gray-300);
  border-radius: 22px;
  resize: none;
  font-size: 14px;
  line-height: 1.4;
  background: linear-gradient(135deg, white 0%, var(--bot-gray-50) 100%);
  transition: all 0.2s ease;
  outline: none;
}

.message-textarea:focus {
  border-color: var(--bot-primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  background: white;
}

.send-button {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  border: none;
  background: linear-gradient(135deg, var(--bot-primary-color) 0%, var(--bot-secondary-color) 100%);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.send-button:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.send-button:active {
  transform: scale(0.98);
}

.send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
}

/* Scrollbar Styling */
.bot-chat-messages::-webkit-scrollbar {
  width: 6px;
}

.bot-chat-messages::-webkit-scrollbar-track {
  background: transparent;
}

.bot-chat-messages::-webkit-scrollbar-thumb {
  background: var(--bot-gray-300);
  border-radius: 3px;
}

.bot-chat-messages::-webkit-scrollbar-thumb:hover {
  background: var(--bot-gray-400);
}

/* Responsive Design */
@media (max-width: 480px) {
  .bot-chat-container {
    width: 100vw;
    height: 100vh;
    border-radius: 0;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
  }
  
  .bot-button {
    width: 56px;
    height: 56px;
  }
  
  .bot-message-bubble,
  .user-message-bubble {
    max-width: 240px;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .ofie-bot-theme-auto {
    --bot-gray-50: #1f2937;
    --bot-gray-100: #374151;
    --bot-gray-200: #4b5563;
    --bot-gray-300: #6b7280;
    --bot-gray-400: #9ca3af;
    --bot-gray-500: #d1d5db;
    --bot-gray-600: #e5e7eb;
    --bot-gray-700: #f3f4f6;
    --bot-gray-800: #f9fafb;
    --bot-gray-900: #ffffff;
  }
  
  .ofie-bot-theme-auto .bot-chat-container {
    background: #1f2937;
    color: white;
  }
  
  .ofie-bot-theme-auto .bot-message-bubble {
    background: #374151;
    border-color: #4b5563;
    color: white;
  }
  
  .ofie-bot-theme-auto .message-input-container {
    background: #1f2937;
    border-color: #4b5563;
  }
  
  .ofie-bot-theme-auto .message-textarea {
    background: #374151;
    border-color: #4b5563;
    color: white;
  }
  
  .ofie-bot-theme-auto .message-textarea::placeholder {
    color: #9ca3af;
  }
}

/* Accessibility */
.bot-button:focus,
.smart-action-button:focus,
.suggestion-button:focus,
.send-button:focus,
.message-textarea:focus {
  outline: 2px solid var(--bot-primary-color);
  outline-offset: 2px;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}