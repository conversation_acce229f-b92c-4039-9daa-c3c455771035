<!-- Professional Ofie Assistant Chat Widget -->
<div data-controller="bot-chat" class="fixed bottom-4 right-4 z-30">
  <!-- Chat Widget -->
  <div data-bot-chat-target="widget" class="bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl shadow-gray-200/50 border border-white/20 w-80 h-96 transform translate-y-full opacity-0 transition-all duration-500 ease-out">
    <!-- Professional Header -->
    <div class="bg-gradient-to-r  from-indigo-950 via-indigo-900 to-indigo-950 text-white p-4 rounded-t-3xl flex items-center justify-between border-b border-slate-700/50">
      <div class="flex items-center space-x-3">
        <div class="w-8 h-8 bg-gradient-to-br from-slate-600 to-slate-700 rounded-xl flex items-center justify-center border border-slate-500/30 shadow-sm">
          <svg class="w-4 h-4 text-slate-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 4v-4z"></path>
          </svg>
        </div>
        <div>
          <h3 class="font-semibold text-sm text-slate-100">Ofie Assistant</h3>
          <div class="flex items-center space-x-1">
            <div class="w-2 h-2 bg-emerald-400 rounded-full animate-pulse"></div>
            <p class="text-xs text-slate-300">Available now</p>
          </div>
        </div>
      </div>
      <button data-bot-chat-target="minimizeButton" data-action="click->bot-chat#toggleWidget"
              class="text-slate-400 hover:text-slate-200 hover:bg-slate-700/50 rounded-xl p-2 transition-all duration-300 transform hover:scale-110">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <!-- Messages Area -->
    <div data-bot-chat-target="messages" class="flex-1 p-4 h-64 overflow-y-auto bg-gradient-to-b from-gray-50/50 to-white/50 backdrop-blur-sm">
      <!-- Messages will be dynamically added here -->
    </div>

    <!-- Input Area -->
    <div class="p-4 border-t border-red-200/30 bg-white/80 backdrop-blur-sm rounded-b-3xl">
      <div class="flex space-x-3">
        <input
          data-bot-chat-target="input"
          type="text"
          placeholder="Ask me anything..."
          class="flex-1 px-4 py-3 bg-white/80 backdrop-blur-sm border border-gray-200/50 rounded-2xl focus:outline-none focus:ring-2 focus:ring-slate-400/50 focus:border-slate-400/50 transition-all duration-300 text-sm placeholder-gray-500"
        >
        <button
          data-bot-chat-target="sendButton"
          data-action="click->bot-chat#sendMessage"
          class="bg-gradient-to-br from-slate-700 via-slate-800 to-slate-900 hover:from-slate-600 hover:via-slate-700 hover:to-slate-800 text-white px-5 py-3 rounded-2xl transition-all duration-300 transform hover:scale-[1.02] shadow-lg hover:shadow-slate-800/50 focus:outline-none focus:ring-2 focus:ring-slate-500/50 text-sm font-medium border border-slate-600/30"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
          </svg>
        </button>
      </div>
    </div>
  </div>

  <!-- Professional Chat Toggle Button -->
  <button
    data-action="click->bot-chat#toggleWidget"
    class="group relative bg-gradient-to-br from-slate-700 via-slate-800 to-slate-900 hover:from-slate-600 hover:via-slate-700 hover:to-slate-800 text-white p-3.5 rounded-2xl shadow-xl shadow-slate-900/30 hover:shadow-2xl hover:shadow-slate-800/50 transition-all duration-300 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-slate-500/50 backdrop-blur-xl border border-slate-600/30 hover:border-slate-500/50
           before:absolute before:inset-0 before:bg-gradient-to-br before:from-white/5 before:to-transparent before:rounded-2xl"
  >
    <!-- Professional Chat Icon -->
    <svg class="w-5 h-5 text-slate-200 group-hover:text-white group-hover:scale-110 transition-all duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 4v-4z"></path>
    </svg>

    <!-- Professional Status Indicator -->
    <div class="absolute -top-0.5 -right-0.5 w-2.5 h-2.5 bg-emerald-400 rounded-full border border-white/30 shadow-sm animate-pulse"></div>

    <!-- Subtle Hover Glow -->
    <div class="absolute inset-0 rounded-2xl bg-gradient-to-br from-slate-600 to-slate-700 opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
  </button>
</div>

<!-- Enhanced Animations and Styles -->
<style>
  @keyframes fade-in-up {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fade-in {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes pulse-glow {
    0%, 100% {
      box-shadow: 0 0 0 0 rgba(71, 85, 105, 0.4);
    }
    50% {
      box-shadow: 0 0 0 8px rgba(71, 85, 105, 0);
    }
  }

  .animate-fade-in-up {
    animation: fade-in-up 0.4s ease-out;
  }

  .animate-fade-in {
    animation: fade-in 0.3s ease-out;
  }

  /* Subtle hover effects for the chat widget */
  [data-controller="bot-chat"] .group:hover {
    animation: pulse-glow 2s infinite;
  }

  /* Smooth scrollbar for messages */
  [data-bot-chat-target="messages"]::-webkit-scrollbar {
    width: 4px;
  }

  [data-bot-chat-target="messages"]::-webkit-scrollbar-track {
    background: transparent;
  }

  [data-bot-chat-target="messages"]::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.3);
    border-radius: 2px;
  }

  [data-bot-chat-target="messages"]::-webkit-scrollbar-thumb:hover {
    background: rgba(156, 163, 175, 0.5);
  }

  /* Enhanced input focus state */
  [data-bot-chat-target="input"]:focus {
    box-shadow: 0 0 0 3px rgba(71, 85, 105, 0.1);
  }

  /* Responsive adjustments */
  @media (max-width: 640px) {
    [data-bot-chat-target="widget"] {
      width: calc(100vw - 2rem);
      right: 1rem;
      left: 1rem;
    }
  }
</style>