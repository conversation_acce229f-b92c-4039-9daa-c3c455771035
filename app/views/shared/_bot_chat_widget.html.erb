<!-- AI-generated code: Bot chat widget -->
<div data-controller="bot-chat" class="fixed bottom-4 right-4 z-50">
  <!-- Chat Widget -->
  <div data-bot-chat-target="widget" class="bg-white rounded-lg shadow-2xl border border-gray-200 w-80 h-96 transform translate-y-full transition-transform duration-300 ease-in-out">
    <!-- Header -->
    <div class="bg-gradient-to-r from-emerald-600 to-emerald-700 text-white p-4 rounded-t-lg flex items-center justify-between">
      <div class="flex items-center space-x-2">
        <div class="w-8 h-8 bg-white rounded-full flex items-center justify-center">
          <svg class="w-5 h-5 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.013 8.013 0 01-7-4c0-4.418 3.582-8 8-8s8 3.582 8 8z"></path>
          </svg>
        </div>
        <div>
          <h3 class="font-semibold text-sm">Ofie Assistant</h3>
          <p class="text-xs opacity-90">Online now</p>
        </div>
      </div>
      <button data-bot-chat-target="minimizeButton" data-action="click->bot-chat#toggleWidget" class="text-white hover:text-gray-200 transition-colors">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <!-- Messages Area -->
    <div data-bot-chat-target="messages" class="flex-1 p-4 h-64 overflow-y-auto bg-gray-50">
      <!-- Messages will be dynamically added here -->
    </div>

    <!-- Input Area -->
    <div class="p-4 border-t border-gray-200 bg-white rounded-b-lg">
      <div class="flex space-x-2">
        <input 
          data-bot-chat-target="input" 
          type="text" 
          placeholder="Type your message..." 
          class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent text-sm"
        >
        <button 
          data-bot-chat-target="sendButton" 
          data-action="click->bot-chat#sendMessage" 
          class="bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 transition-colors focus:outline-none focus:ring-2 focus:ring-emerald-500 text-sm font-medium"
        >
          Send
        </button>
      </div>
    </div>
  </div>

  <!-- Chat Toggle Button -->
  <button 
    data-action="click->bot-chat#toggleWidget" 
    class="bg-emerald-600 text-white p-4 rounded-full shadow-lg hover:bg-emerald-700 transition-all duration-300 transform hover:scale-110 focus:outline-none focus:ring-4 focus:ring-emerald-300"
  >
    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.013 8.013 0 01-7-4c0-4.418 3.582-8 8-8s8 3.582 8 8z"></path>
    </svg>
  </button>
</div>