<!-- Streamlined Ofie Assistant Chat Widget -->
<div data-controller="bot-chat" class="fixed bottom-6 right-6 z-40">
  <!-- Chat Widget -->
  <div data-bot-chat-target="widget" class="bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl shadow-gray-200/50 border border-white/20 w-80 h-96 transform translate-y-full opacity-0 transition-all duration-500 ease-out">
    <!-- Header -->
    <div class="bg-gradient-to-r from-cyan-500 via-blue-500 to-indigo-500 text-white p-4 rounded-t-3xl flex items-center justify-between">
      <div class="flex items-center space-x-3">
        <div class="w-8 h-8 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center border border-white/30">
          <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
          </svg>
        </div>
        <div>
          <h3 class="font-semibold text-sm">Ofie Assistant</h3>
          <div class="flex items-center space-x-1">
            <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <p class="text-xs opacity-90">Available now</p>
          </div>
        </div>
      </div>
      <button data-bot-chat-target="minimizeButton" data-action="click->bot-chat#toggleWidget"
              class="text-white/80 hover:text-white hover:bg-white/20 rounded-xl p-2 transition-all duration-300 transform hover:scale-110">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
        </svg>
      </button>
    </div>

    <!-- Messages Area -->
    <div data-bot-chat-target="messages" class="flex-1 p-4 h-64 overflow-y-auto bg-gradient-to-b from-gray-50/50 to-white/50 backdrop-blur-sm">
      <!-- Messages will be dynamically added here -->
    </div>

    <!-- Input Area -->
    <div class="p-4 border-t border-gray-200/30 bg-white/80 backdrop-blur-sm rounded-b-3xl">
      <div class="flex space-x-3">
        <input
          data-bot-chat-target="input"
          type="text"
          placeholder="Ask me anything..."
          class="flex-1 px-4 py-3 bg-white/80 backdrop-blur-sm border border-gray-200/50 rounded-2xl focus:outline-none focus:ring-2 focus:ring-cyan-400/50 focus:border-cyan-400/50 transition-all duration-300 text-sm placeholder-gray-500"
        >
        <button
          data-bot-chat-target="sendButton"
          data-action="click->bot-chat#sendMessage"
          class="bg-gradient-to-r from-cyan-500 via-blue-500 to-indigo-500 hover:from-cyan-600 hover:via-blue-600 hover:to-indigo-600 text-white px-5 py-3 rounded-2xl transition-all duration-300 transform hover:scale-[1.02] shadow-lg hover:shadow-cyan-500/25 focus:outline-none focus:ring-2 focus:ring-cyan-400/50 text-sm font-medium"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
          </svg>
        </button>
      </div>
    </div>
  </div>

  <!-- Chat Toggle Button -->
  <button
    data-action="click->bot-chat#toggleWidget"
    class="group bg-gradient-to-r from-cyan-500 via-blue-500 to-indigo-500 hover:from-cyan-600 hover:via-blue-600 hover:to-indigo-600 text-white p-4 rounded-2xl shadow-xl shadow-cyan-500/25 hover:shadow-2xl hover:shadow-cyan-500/40 transition-all duration-300 transform hover:scale-110 focus:outline-none focus:ring-4 focus:ring-cyan-300/50 backdrop-blur-sm border border-white/20"
  >
    <svg class="w-6 h-6 group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.013 8.013 0 01-7-4c0-4.418 3.582-8 8-8s8 3.582 8 8z"></path>
    </svg>
    <!-- Subtle notification indicator -->
    <div class="absolute -top-1 -right-1 w-3 h-3 bg-amber-400 rounded-full border-2 border-white animate-pulse opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
  </button>
</div>

<!-- Enhanced Animations and Styles -->
<style>
  @keyframes fade-in-up {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fade-in {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes pulse-glow {
    0%, 100% {
      box-shadow: 0 0 0 0 rgba(6, 182, 212, 0.4);
    }
    50% {
      box-shadow: 0 0 0 10px rgba(6, 182, 212, 0);
    }
  }

  .animate-fade-in-up {
    animation: fade-in-up 0.4s ease-out;
  }

  .animate-fade-in {
    animation: fade-in 0.3s ease-out;
  }

  /* Subtle hover effects for the chat widget */
  [data-controller="bot-chat"] .group:hover {
    animation: pulse-glow 2s infinite;
  }

  /* Smooth scrollbar for messages */
  [data-bot-chat-target="messages"]::-webkit-scrollbar {
    width: 4px;
  }

  [data-bot-chat-target="messages"]::-webkit-scrollbar-track {
    background: transparent;
  }

  [data-bot-chat-target="messages"]::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.3);
    border-radius: 2px;
  }

  [data-bot-chat-target="messages"]::-webkit-scrollbar-thumb:hover {
    background: rgba(156, 163, 175, 0.5);
  }

  /* Enhanced input focus state */
  [data-bot-chat-target="input"]:focus {
    box-shadow: 0 0 0 3px rgba(6, 182, 212, 0.1);
  }

  /* Responsive adjustments */
  @media (max-width: 640px) {
    [data-bot-chat-target="widget"] {
      width: calc(100vw - 2rem);
      right: 1rem;
      left: 1rem;
    }
  }
</style>