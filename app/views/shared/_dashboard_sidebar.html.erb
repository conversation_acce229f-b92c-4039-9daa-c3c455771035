<!-- Enhanced Professional Dashboard Sidebar -->
<div class="fixed inset-y-0 left-0 z-50 w-80 bg-white/95 backdrop-blur-2xl shadow-2xl shadow-gray-200/50 border-r border-gray-200/30 transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 flex flex-col"
     data-sidebar-target="sidebar"
     data-controller="sidebar">

  <!-- Enhanced Sidebar Header -->
  <div class="flex items-center justify-between h-24 px-8 border-b border-gray-200/30 bg-gradient-to-r from-white/80 to-slate-50/80 flex-shrink-0">
    <div class="flex items-center space-x-4">
      <%= link_to root_path, class: "flex items-center space-x-4 group" do %>
        <div class="relative w-12 h-12 bg-gradient-to-br from-blue-600 via-indigo-600 to-purple-600 rounded-2xl flex items-center justify-center shadow-xl shadow-blue-500/25 group-hover:scale-110 group-hover:rotate-3 transition-all duration-300">
          <!-- Advanced Ofie Logo Icon -->
          <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <g opacity="0.95">
              <!-- Building structure -->
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5"></path>
              <!-- Windows grid -->
              <rect x="9" y="7" width="1.5" height="1.5" fill="currentColor" opacity="0.8"/>
              <rect x="13.5" y="7" width="1.5" height="1.5" fill="currentColor" opacity="0.8"/>
              <rect x="9" y="10.5" width="1.5" height="1.5" fill="currentColor" opacity="0.8"/>
              <rect x="13.5" y="10.5" width="1.5" height="1.5" fill="currentColor" opacity="0.8"/>
              <rect x="9" y="14" width="1.5" height="1.5" fill="currentColor" opacity="0.8"/>
              <rect x="13.5" y="14" width="1.5" height="1.5" fill="currentColor" opacity="0.8"/>
              <!-- Door -->
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 17v4h4v-4a1 1 0 00-1-1h-2a1 1 0 00-1 1z" opacity="0.9"/>
              <!-- Roof detail -->
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M6 5l6-2 6 2" opacity="0.7"/>
              <!-- "O" integration -->
              <circle cx="12" cy="4" r="1.5" fill="currentColor" opacity="0.6"/>
            </g>
          </svg>
          <!-- Ambient glow effect -->
          <div class="absolute inset-0 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-2xl blur-xl group-hover:blur-2xl transition-all duration-300"></div>
        </div>
        <div>
          <span class="text-2xl font-bold bg-gradient-to-r from-gray-900 via-slate-800 to-gray-700 bg-clip-text text-transparent group-hover:from-blue-600 group-hover:to-indigo-600 transition-all duration-300">Ofie</span>
          <div class="text-xs text-slate-500 font-medium tracking-wider">DASHBOARD</div>
        </div>
      <% end %>
    </div>
    
    <!-- Mobile Close Button -->
    <button class="lg:hidden p-3 rounded-xl text-gray-500 hover:text-gray-700 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 transition-all duration-200 transform hover:scale-105"
            data-action="click->sidebar#close">
      <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M6 18L18 6M6 6l12 12"></path>
      </svg>
    </button>
  </div>

  <!-- Enhanced User Profile Section -->
  <div class="px-6 py-6 border-b border-gray-200/30 bg-gradient-to-r from-slate-50/50 to-white/50 flex-shrink-0">
    <div class="flex items-center space-x-4">
      <div class="relative">
        <% if current_user.avatar.attached? %>
          <div class="relative">
            <%= image_tag current_user.avatar, class: "h-14 w-14 rounded-2xl object-cover border-3 border-white shadow-xl", alt: "#{current_user.name} avatar" %>
            <div class="absolute -bottom-1 -right-1 w-5 h-5 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full border-3 border-white shadow-lg"></div>
          </div>
        <% else %>
          <div class="relative h-14 w-14 bg-gradient-to-br from-blue-500 via-indigo-600 to-purple-600 rounded-2xl flex items-center justify-center shadow-xl shadow-blue-500/25">
            <span class="text-white font-bold text-xl"><%= current_user.name&.first&.upcase || current_user.email.first.upcase %></span>
            <div class="absolute -bottom-1 -right-1 w-5 h-5 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full border-3 border-white shadow-lg"></div>
          </div>
        <% end %>
        <!-- Status indicator with pulse -->
        <div class="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-emerald-400 to-green-500 rounded-full border-2 border-white animate-pulse"></div>
      </div>
      
      <div class="flex-1 min-w-0">
        <div class="flex items-center space-x-2">
          <p class="text-base font-bold text-gray-900 truncate">
            <%= current_user.name || current_user.email.split('@').first.capitalize %>
          </p>
          <div class="w-1.5 h-1.5 bg-green-400 rounded-full animate-pulse"></div>
        </div>
        <div class="flex items-center space-x-2 mt-1">
          <div class="px-2 py-0.5 bg-gradient-to-r <%= current_user.landlord? ? 'from-blue-100 to-indigo-100 text-blue-700' : 'from-emerald-100 to-green-100 text-emerald-700' %> rounded-lg text-xs font-semibold">
            <%= current_user.landlord? ? "Property Owner" : "Tenant" %>
          </div>
          <div class="text-xs text-slate-400">• Online</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Enhanced Navigation Menu -->
  <nav class="flex-1 px-4 py-6 space-y-3 overflow-y-auto min-h-0">
    <!-- Dashboard Section -->
    <div class="space-y-2">
      <%= link_to dashboard_path,
          class: "group flex items-center px-5 py-4 text-sm font-semibold rounded-2xl transition-all duration-300 transform hover:scale-[1.02] #{current_page?(dashboard_path) ? 'bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50 text-blue-700 border border-blue-200/50 shadow-lg shadow-blue-500/10' : 'text-gray-700 hover:bg-gradient-to-r hover:from-gray-50 hover:to-slate-50 hover:text-gray-900 hover:shadow-lg'}" do %>
        <div class="w-10 h-10 rounded-xl flex items-center justify-center mr-4 transition-all duration-300 #{current_page?(dashboard_path) ? 'bg-gradient-to-br from-blue-500 via-indigo-600 to-purple-600 text-white shadow-xl shadow-blue-500/25' : 'bg-gradient-to-br from-gray-100 to-gray-200 text-gray-600 group-hover:from-blue-100 group-hover:to-indigo-100 group-hover:text-blue-600 group-hover:shadow-lg'}">
          <!-- Premium Dashboard Icon -->
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <g opacity="0.95">
              <!-- Dashboard grid structure -->
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              <!-- Data visualization elements -->
              <circle cx="6" cy="16" r="0.8" fill="currentColor" opacity="0.8"/>
              <circle cx="13" cy="12" r="0.8" fill="currentColor" opacity="0.8"/>
              <circle cx="19" cy="8" r="0.8" fill="currentColor" opacity="0.8"/>
              <!-- Analytics lines -->
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M5 15h2m6-3h2m6-4h2" opacity="0.6"/>
              <!-- Chart trending lines -->
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M6 14l1-1 2 2 1-1m7-6l1-1 2 2 1-1" opacity="0.5"/>
            </g>
          </svg>
        </div>
        <div class="flex-1">
          <div class="font-semibold">Dashboard</div>
          <div class="text-xs opacity-75 mt-0.5">Overview & insights</div>
        </div>
        <% if current_page?(dashboard_path) %>
          <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
        <% end %>
      <% end %>

      <% if current_user.landlord? %>
        <%= link_to analytics_path, 
            class: "group flex items-center px-5 py-4 text-sm font-semibold rounded-2xl transition-all duration-300 transform hover:scale-[1.02] #{current_page?(analytics_path) ? 'bg-gradient-to-r from-emerald-50 via-green-50 to-teal-50 text-emerald-700 border border-emerald-200/50 shadow-lg shadow-emerald-500/10' : 'text-gray-700 hover:bg-gradient-to-r hover:from-gray-50 hover:to-slate-50 hover:text-gray-900 hover:shadow-lg'}" do %>
          <div class="w-10 h-10 rounded-xl flex items-center justify-center mr-4 transition-all duration-300 #{current_page?(analytics_path) ? 'bg-gradient-to-br from-emerald-500 via-green-600 to-teal-600 text-white shadow-xl shadow-emerald-500/25' : 'bg-gradient-to-br from-gray-100 to-gray-200 text-gray-600 group-hover:from-emerald-100 group-hover:to-green-100 group-hover:text-emerald-600 group-hover:shadow-lg'}">
            <!-- Premium Analytics Icon -->
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <g opacity="0.95">
                <!-- Advanced analytics chart -->
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                <!-- Trending arrows and growth indicators -->
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M18 6l-3 3 3 3" opacity="0.7"/>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M6 18l3-3-3-3" opacity="0.7"/>
                <!-- Data points -->
                <circle cx="6" cy="16" r="1" fill="currentColor" opacity="0.6"/>
                <circle cx="13" cy="12" r="1" fill="currentColor" opacity="0.6"/>
                <circle cx="19" cy="8" r="1" fill="currentColor" opacity="0.6"/>
                <!-- Trend lines -->
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M6 16l7-4 6-4" opacity="0.5"/>
                <!-- Performance indicators -->
                <rect x="4" y="18" width="4" height="1" rx="0.5" fill="currentColor" opacity="0.4"/>
                <rect x="11" y="14" width="4" height="1" rx="0.5" fill="currentColor" opacity="0.4"/>
                <rect x="17" y="10" width="4" height="1" rx="0.5" fill="currentColor" opacity="0.4"/>
              </g>
            </svg>
          </div>
          <div class="flex-1">
            <div class="font-semibold">Analytics</div>
            <div class="text-xs opacity-75 mt-0.5">Performance metrics</div>
          </div>
          <% if current_page?(analytics_path) %>
            <div class="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
          <% end %>
        <% end %>
      <% end %>
    </div>

    <!-- Properties Section -->
    <div class="pt-6">
      <div class="flex items-center px-5 mb-4">
        <h3 class="text-xs font-bold text-gray-500 uppercase tracking-wider">
          <%= current_user.landlord? ? "Property Management" : "Properties" %>
        </h3>
        <div class="flex-1 h-px bg-gradient-to-r from-gray-200 to-transparent ml-4"></div>
      </div>
      
      <div class="space-y-2">
        <% if current_user.landlord? %>
          <%= link_to my_properties_properties_path,
              class: "group flex items-center px-5 py-4 text-sm font-semibold rounded-2xl transition-all duration-300 transform hover:scale-[1.02] #{current_page?(my_properties_properties_path) ? 'bg-gradient-to-r from-purple-50 via-indigo-50 to-blue-50 text-purple-700 border border-purple-200/50 shadow-lg shadow-purple-500/10' : 'text-gray-700 hover:bg-gradient-to-r hover:from-gray-50 hover:to-slate-50 hover:text-gray-900 hover:shadow-lg'}" do %>
            <div class="w-10 h-10 rounded-xl flex items-center justify-center mr-4 transition-all duration-300 #{current_page?(my_properties_properties_path) ? 'bg-gradient-to-br from-purple-500 via-indigo-600 to-blue-600 text-white shadow-xl shadow-purple-500/25' : 'bg-gradient-to-br from-gray-100 to-gray-200 text-gray-600 group-hover:from-purple-100 group-hover:to-indigo-100 group-hover:text-purple-600 group-hover:shadow-lg'}">
              <!-- Premium Property Building Icon -->
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <g opacity="0.95">
                  <!-- Main building structure -->
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5"></path>
                  <!-- Building floors and details -->
                  <rect x="9" y="7" width="1.8" height="1.8" rx="0.3" fill="currentColor" opacity="0.8"/>
                  <rect x="13.2" y="7" width="1.8" height="1.8" rx="0.3" fill="currentColor" opacity="0.8"/>
                  <rect x="9" y="10.5" width="1.8" height="1.8" rx="0.3" fill="currentColor" opacity="0.8"/>
                  <rect x="13.2" y="10.5" width="1.8" height="1.8" rx="0.3" fill="currentColor" opacity="0.8"/>
                  <rect x="9" y="14" width="1.8" height="1.8" rx="0.3" fill="currentColor" opacity="0.8"/>
                  <rect x="13.2" y="14" width="1.8" height="1.8" rx="0.3" fill="currentColor" opacity="0.8"/>
                  <!-- Entrance door with details -->
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 17v4h4v-4a1 1 0 00-1-1h-2a1 1 0 00-1 1z"></path>
                  <circle cx="12.8" cy="18.5" r="0.3" fill="currentColor" opacity="0.9"/>
                  <!-- Roof and architectural details -->
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.8" d="M6 5l6-2 6 2" opacity="0.7"/>
                  <circle cx="12" cy="4" r="0.8" fill="currentColor" opacity="0.5"/>
                  <!-- Property management indicators -->
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M7 9h1m7 0h1m-8 3.5h1m7 0h1m-8 3.5h1m7 0h1" opacity="0.4"/>
                  <!-- Building value indicators -->
                  <circle cx="6" cy="6" r="0.8" fill="currentColor" opacity="0.3"/>
                  <circle cx="18" cy="6" r="0.8" fill="currentColor" opacity="0.3"/>
                </g>
              </svg>
            </div>
            <div class="flex-1">
              <div class="font-semibold">My Properties</div>
              <div class="text-xs opacity-75 mt-0.5">Manage portfolio</div>
            </div>
            <% if current_page?(my_properties_properties_path) %>
              <div class="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
            <% end %>
          <% end %>

          <%= link_to new_property_path,
              class: "group flex items-center px-5 py-4 text-sm font-semibold rounded-2xl transition-all duration-300 transform hover:scale-[1.02] text-gray-700 hover:bg-gradient-to-r hover:from-gray-50 hover:to-slate-50 hover:text-gray-900 hover:shadow-lg" do %>
            <div class="w-10 h-10 rounded-xl flex items-center justify-center mr-4 transition-all duration-300 bg-gradient-to-br from-gray-100 to-gray-200 text-gray-600 group-hover:from-emerald-100 group-hover:to-green-100 group-hover:text-emerald-600 group-hover:shadow-lg">
              <!-- Premium Add Property Icon -->
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <g opacity="0.95">
                  <!-- Enhanced plus sign with property context -->
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                  <!-- Building outline integration -->
                  <rect x="4" y="4" width="16" height="16" rx="3" stroke="currentColor" stroke-width="1.5" opacity="0.3"/>
                  <!-- Property creation indicators -->
                  <circle cx="12" cy="12" r="2" stroke="currentColor" stroke-width="1.5" opacity="0.4"/>
                  <!-- Corner property markers -->
                  <rect x="6" y="6" width="2" height="2" rx="0.5" fill="currentColor" opacity="0.5"/>
                  <rect x="16" y="6" width="2" height="2" rx="0.5" fill="currentColor" opacity="0.5"/>
                  <rect x="6" y="16" width="2" height="2" rx="0.5" fill="currentColor" opacity="0.5"/>
                  <rect x="16" y="16" width="2" height="2" rx="0.5" fill="currentColor" opacity="0.5"/>
                  <!-- New property sparkles -->
                  <circle cx="8" cy="8" r="0.5" fill="currentColor" opacity="0.6"/>
                  <circle cx="16" cy="8" r="0.5" fill="currentColor" opacity="0.6"/>
                  <circle cx="8" cy="16" r="0.5" fill="currentColor" opacity="0.6"/>
                  <circle cx="16" cy="16" r="0.5" fill="currentColor" opacity="0.6"/>
                  <!-- Innovation indicators -->
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 9l6 6m0-6l-6 6" opacity="0.2"/>
                </g>
              </svg>
            </div>
            <div class="flex-1">
              <div class="font-semibold">Add Property</div>
              <div class="text-xs opacity-75 mt-0.5">List new property</div>
            </div>
          <% end %>

          <%= link_to batch_properties_path,
              class: "group flex items-center px-5 py-4 text-sm font-semibold rounded-2xl transition-all duration-300 transform hover:scale-[1.02] #{current_page?(batch_properties_path) ? 'bg-gradient-to-r from-cyan-50 via-blue-50 to-indigo-50 text-cyan-700 border border-cyan-200/50 shadow-lg shadow-cyan-500/10' : 'text-gray-700 hover:bg-gradient-to-r hover:from-gray-50 hover:to-slate-50 hover:text-gray-900 hover:shadow-lg'}" do %>
            <div class="w-10 h-10 rounded-xl flex items-center justify-center mr-4 transition-all duration-300 #{current_page?(batch_properties_path) ? 'bg-gradient-to-br from-cyan-500 via-blue-600 to-indigo-600 text-white shadow-xl shadow-cyan-500/25' : 'bg-gradient-to-br from-gray-100 to-gray-200 text-gray-600 group-hover:from-cyan-100 group-hover:to-blue-100 group-hover:text-cyan-600 group-hover:shadow-lg'}">
              <!-- Premium Batch Upload Icon -->
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <g opacity="0.95">
                  <!-- Multiple documents/properties -->
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  <!-- Batch indicator - multiple layers -->
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M7 3h10a2 2 0 012 2v14" opacity="0.6"/>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M5 5h10a2 2 0 012 2v14" opacity="0.4"/>
                  <!-- Upload arrow -->
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16l3-3m0 0l3 3m-3-3v8" opacity="0.8"/>
                  <!-- Data rows -->
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 8h6m-6 4h4" opacity="0.7"/>
                  <!-- Batch processing indicators -->
                  <circle cx="16" cy="8" r="1" fill="currentColor" opacity="0.6"/>
                  <circle cx="18" cy="10" r="0.8" fill="currentColor" opacity="0.5"/>
                  <circle cx="16" cy="12" r="0.6" fill="currentColor" opacity="0.4"/>
                </g>
              </svg>
            </div>
            <div class="flex-1">
              <div class="font-semibold">Batch Upload</div>
              <div class="text-xs opacity-75 mt-0.5">Upload multiple properties</div>
            </div>
            <% if current_page?(batch_properties_path) %>
              <div class="w-2 h-2 bg-cyan-500 rounded-full animate-pulse"></div>
            <% end %>
          <% end %>
        <% else %>
          <%= link_to favorites_path,
              class: "group flex items-center px-5 py-4 text-sm font-semibold rounded-2xl transition-all duration-300 transform hover:scale-[1.02] #{current_page?(favorites_path) ? 'bg-gradient-to-r from-pink-50 via-rose-50 to-red-50 text-pink-700 border border-pink-200/50 shadow-lg shadow-pink-500/10' : 'text-gray-700 hover:bg-gradient-to-r hover:from-gray-50 hover:to-slate-50 hover:text-gray-900 hover:shadow-lg'}" do %>
            <div class="w-10 h-10 rounded-xl flex items-center justify-center mr-4 transition-all duration-300 #{current_page?(favorites_path) ? 'bg-gradient-to-br from-pink-500 via-rose-600 to-red-600 text-white shadow-xl shadow-pink-500/25' : 'bg-gradient-to-br from-gray-100 to-gray-200 text-gray-600 group-hover:from-pink-100 group-hover:to-rose-100 group-hover:text-pink-600 group-hover:shadow-lg'}">
              <!-- Premium Heart/Favorites Icon -->
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <g opacity="0.95">
                  <!-- Enhanced heart with details -->
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                  <!-- Heart pulse and glow -->
                  <circle cx="12" cy="12" r="1.5" fill="currentColor" opacity="0.6"/>
                  <!-- Love connection lines -->
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M8.5 8.5c0-1.1.9-2 2-2s2 .9 2 2" opacity="0.7"/>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M11.5 8.5c0-1.1.9-2 2-2s2 .9 2 2" opacity="0.7"/>
                  <!-- Sparkle effects around heart -->
                  <circle cx="7" cy="9" r="0.8" fill="currentColor" opacity="0.4"/>
                  <circle cx="17" cy="9" r="0.8" fill="currentColor" opacity="0.4"/>
                  <circle cx="9" cy="15" r="0.6" fill="currentColor" opacity="0.5"/>
                  <circle cx="15" cy="15" r="0.6" fill="currentColor" opacity="0.5"/>
                  <!-- Heart rhythm lines -->
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M6 6l1 1m10-1l-1 1m-8 8l1-1m6 1l-1-1" opacity="0.4"/>
                  <!-- Favorite indicators -->
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M12 4v2m0 12v2m8-8h-2M4 12H2" opacity="0.3"/>
                </g>
              </svg>
            </div>
            <div class="flex-1">
              <div class="font-semibold">Favorites</div>
              <div class="text-xs opacity-75 mt-0.5">Saved properties</div>
            </div>
            <% if @favorites_count && @favorites_count > 0 %>
              <div class="flex items-center space-x-2">
                <span class="bg-gradient-to-r from-pink-500 to-rose-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center font-semibold shadow-lg">
                  <%= @favorites_count > 9 ? '9+' : @favorites_count %>
                </span>
                <div class="w-2 h-2 bg-pink-500 rounded-full animate-pulse"></div>
              </div>
            <% end %>
          <% end %>
        <% end %>
      </div>
    </div>

    <!-- Applications & Leases Section -->
    <div class="pt-6">
      <div class="flex items-center px-5 mb-4">
        <h3 class="text-xs font-bold text-gray-500 uppercase tracking-wider">Rental Management</h3>
        <div class="flex-1 h-px bg-gradient-to-r from-gray-200 to-transparent ml-4"></div>
      </div>
      
      <div class="space-y-2">
        <%= link_to rental_applications_path, 
            class: "group flex items-center px-5 py-4 text-sm font-semibold rounded-2xl transition-all duration-300 transform hover:scale-[1.02] #{current_page?(rental_applications_path) ? 'bg-gradient-to-r from-amber-50 via-orange-50 to-yellow-50 text-amber-700 border border-amber-200/50 shadow-lg shadow-amber-500/10' : 'text-gray-700 hover:bg-gradient-to-r hover:from-gray-50 hover:to-slate-50 hover:text-gray-900 hover:shadow-lg'}" do %>
          <div class="w-10 h-10 rounded-xl flex items-center justify-center mr-4 transition-all duration-300 #{current_page?(rental_applications_path) ? 'bg-gradient-to-br from-amber-500 via-orange-600 to-yellow-600 text-white shadow-xl shadow-amber-500/25' : 'bg-gradient-to-br from-gray-100 to-gray-200 text-gray-600 group-hover:from-amber-100 group-hover:to-orange-100 group-hover:text-amber-600 group-hover:shadow-lg'}">
            <!-- Premium Applications Icon -->
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <g opacity="0.95">
                <!-- Document with application details -->
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                <!-- Application form lines -->
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 8h6m-6 4h4m-4 4h2" opacity="0.7"/>
                <!-- Checkboxes and form elements -->
                <rect x="7" y="7.5" width="1" height="1" rx="0.2" fill="currentColor" opacity="0.8"/>
                <rect x="7" y="11.5" width="1" height="1" rx="0.2" fill="currentColor" opacity="0.8"/>
                <rect x="7" y="15.5" width="1" height="1" rx="0.2" fill="currentColor" opacity="0.8"/>
                <!-- Document corner fold -->
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M14 3v4a1 1 0 001 1h4" opacity="0.6"/>
                <!-- Application status indicators -->
                <circle cx="18" cy="6" r="1" fill="currentColor" opacity="0.4"/>
                <circle cx="18" cy="9" r="0.8" fill="currentColor" opacity="0.5"/>
                <!-- Review markers -->
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M16 8l1 1 1-1m-2 4l1 1 1-1" opacity="0.6"/>
              </g>
            </svg>
          </div>
          <div class="flex-1">
            <div class="font-semibold"><%= current_user.landlord? ? "Applications" : "My Applications" %></div>
            <div class="text-xs opacity-75 mt-0.5">Review & manage</div>
          </div>
          <% if current_user.landlord? && @pending_applications_count && @pending_applications_count > 0 %>
            <div class="flex items-center space-x-2">
              <span class="bg-gradient-to-r from-amber-500 to-orange-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center font-semibold shadow-lg">
                <%= @pending_applications_count > 9 ? '9+' : @pending_applications_count %>
              </span>
              <div class="w-2 h-2 bg-amber-500 rounded-full animate-pulse"></div>
            </div>
          <% end %>
        <% end %>

        <%= link_to lease_agreements_path, 
            class: "group flex items-center px-5 py-4 text-sm font-semibold rounded-2xl transition-all duration-300 transform hover:scale-[1.02] #{current_page?(lease_agreements_path) ? 'bg-gradient-to-r from-indigo-50 via-blue-50 to-cyan-50 text-indigo-700 border border-indigo-200/50 shadow-lg shadow-indigo-500/10' : 'text-gray-700 hover:bg-gradient-to-r hover:from-gray-50 hover:to-slate-50 hover:text-gray-900 hover:shadow-lg'}" do %>
          <div class="w-10 h-10 rounded-xl flex items-center justify-center mr-4 transition-all duration-300 #{current_page?(lease_agreements_path) ? 'bg-gradient-to-br from-indigo-500 via-blue-600 to-cyan-600 text-white shadow-xl shadow-indigo-500/25' : 'bg-gradient-to-br from-gray-100 to-gray-200 text-gray-600 group-hover:from-indigo-100 group-hover:to-blue-100 group-hover:text-indigo-600 group-hover:shadow-lg'}">
            <!-- Premium Lease Agreement Icon -->
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <g opacity="0.95">
                <!-- Contract document with signature -->
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                <!-- Legal document details -->
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 8h6m-6 4h4m-4 4h3" opacity="0.7"/>
                <!-- Official seal/stamp -->
                <circle cx="16" cy="16" r="2" stroke="currentColor" stroke-width="1.5" opacity="0.6"/>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M15 16l1 1 2-2" opacity="0.8"/>
                <!-- Signature line -->
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M7 19h5" opacity="0.5"/>
                <!-- Document authenticity markers -->
                <rect x="7" y="7.5" width="0.5" height="0.5" rx="0.1" fill="currentColor" opacity="0.6"/>
                <rect x="7" y="11.5" width="0.5" height="0.5" rx="0.1" fill="currentColor" opacity="0.6"/>
                <rect x="7" y="15.5" width="0.5" height="0.5" rx="0.1" fill="currentColor" opacity="0.6"/>
                <!-- Legal binding indicators -->
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M18 8v2m0 4v2" opacity="0.4"/>
                <!-- Contract validation -->
                <circle cx="19" cy="10" r="0.5" fill="currentColor" opacity="0.5"/>
                <circle cx="19" cy="14" r="0.5" fill="currentColor" opacity="0.5"/>
              </g>
            </svg>
          </div>
          <div class="flex-1">
            <div class="font-semibold"><%= current_user.landlord? ? "Lease Agreements" : "My Leases" %></div>
            <div class="text-xs opacity-75 mt-0.5">Active contracts</div>
          </div>
          <% if current_user.landlord? && @active_leases_count && @active_leases_count > 0 %>
            <div class="flex items-center space-x-2">
              <span class="bg-gradient-to-r from-indigo-500 to-blue-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center font-semibold shadow-lg">
                <%= @active_leases_count > 9 ? '9+' : @active_leases_count %>
              </span>
              <div class="w-2 h-2 bg-indigo-500 rounded-full animate-pulse"></div>
            </div>
          <% end %>
        <% end %>
      </div>
    </div>

    <!-- Financial Section -->
    <div class="pt-6">
      <div class="flex items-center px-5 mb-4">
        <h3 class="text-xs font-bold text-gray-500 uppercase tracking-wider">Financial</h3>
        <div class="flex-1 h-px bg-gradient-to-r from-gray-200 to-transparent ml-4"></div>
      </div>
      
      <div class="space-y-2">
        <%= link_to payments_path,
            class: "group flex items-center px-5 py-4 text-sm font-semibold rounded-2xl transition-all duration-300 transform hover:scale-[1.02] #{current_page?(payments_path) ? 'bg-gradient-to-r from-green-50 via-emerald-50 to-teal-50 text-green-700 border border-green-200/50 shadow-lg shadow-green-500/10' : 'text-gray-700 hover:bg-gradient-to-r hover:from-gray-50 hover:to-slate-50 hover:text-gray-900 hover:shadow-lg'}" do %>
          <div class="w-10 h-10 rounded-xl flex items-center justify-center mr-4 transition-all duration-300 #{current_page?(payments_path) ? 'bg-gradient-to-br from-green-500 via-emerald-600 to-teal-600 text-white shadow-xl shadow-green-500/25' : 'bg-gradient-to-br from-gray-100 to-gray-200 text-gray-600 group-hover:from-green-100 group-hover:to-emerald-100 group-hover:text-green-600 group-hover:shadow-lg'}">
            <!-- Premium Payment/Money Icon -->
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <g opacity="0.95">
                <!-- Enhanced currency and payment flow -->
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                <!-- Financial circle with security -->
                <circle cx="12" cy="12" r="9" stroke="currentColor" stroke-width="1.5" opacity="0.3"/>
                <!-- Payment processing indicators -->
                <circle cx="12" cy="10" r="1.5" stroke="currentColor" stroke-width="1" opacity="0.5"/>
                <circle cx="12" cy="14" r="1.5" stroke="currentColor" stroke-width="1" opacity="0.5"/>
                <!-- Transaction flow arrows -->
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M8 8l1 1m6-1l-1 1m-6 6l1-1m6 1l-1-1" opacity="0.6"/>
                <!-- Financial security markers -->
                <rect x="6" y="11" width="2" height="2" rx="1" stroke="currentColor" stroke-width="1" opacity="0.4"/>
                <rect x="16" y="11" width="2" height="2" rx="1" stroke="currentColor" stroke-width="1" opacity="0.4"/>
                <!-- Value flow lines -->
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M4 12h3m10 0h3" opacity="0.5"/>
                <!-- Payment verification -->
                <circle cx="6" cy="6" r="0.8" fill="currentColor" opacity="0.4"/>
                <circle cx="18" cy="6" r="0.8" fill="currentColor" opacity="0.4"/>
                <circle cx="6" cy="18" r="0.8" fill="currentColor" opacity="0.4"/>
                <circle cx="18" cy="18" r="0.8" fill="currentColor" opacity="0.4"/>
              </g>
            </svg>
          </div>
          <div class="flex-1">
            <div class="font-semibold"><%= current_user.landlord? ? "Payment Management" : "My Payments" %></div>
            <div class="text-xs opacity-75 mt-0.5">Financial overview</div>
          </div>
          <% unless current_user.landlord? %>
            <% if @overdue_payments_count && @overdue_payments_count > 0 %>
              <div class="flex items-center space-x-2">
                <span class="bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center font-semibold shadow-lg">
                  <%= @overdue_payments_count > 9 ? '9+' : @overdue_payments_count %>
                </span>
                <div class="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
              </div>
            <% end %>
          <% end %>
        <% end %>
      </div>
    </div>

    <!-- Communication Section -->
    <div class="pt-6">
      <div class="flex items-center px-5 mb-4">
        <h3 class="text-xs font-bold text-gray-500 uppercase tracking-wider">Communication</h3>
        <div class="flex-1 h-px bg-gradient-to-r from-gray-200 to-transparent ml-4"></div>
      </div>
      
      <div class="space-y-2">
        <%= link_to conversations_path,
            class: "group flex items-center px-5 py-4 text-sm font-semibold rounded-2xl transition-all duration-300 transform hover:scale-[1.02] #{current_page?(conversations_path) ? 'bg-gradient-to-r from-blue-50 via-cyan-50 to-sky-50 text-blue-700 border border-blue-200/50 shadow-lg shadow-blue-500/10' : 'text-gray-700 hover:bg-gradient-to-r hover:from-gray-50 hover:to-slate-50 hover:text-gray-900 hover:shadow-lg'}" do %>
          <div class="w-10 h-10 rounded-xl flex items-center justify-center mr-4 transition-all duration-300 #{current_page?(conversations_path) ? 'bg-gradient-to-br from-blue-500 via-cyan-600 to-sky-600 text-white shadow-xl shadow-blue-500/25' : 'bg-gradient-to-br from-gray-100 to-gray-200 text-gray-600 group-hover:from-blue-100 group-hover:to-cyan-100 group-hover:text-blue-600 group-hover:shadow-lg'}">
            <!-- Premium Messages/Chat Icon -->
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <g opacity="0.95">
                <!-- Enhanced chat bubble with communication details -->
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.2" d="M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                <!-- Message dots with enhanced design -->
                <circle cx="8" cy="12" r="1.3" fill="currentColor" opacity="0.8"/>
                <circle cx="12" cy="12" r="1.3" fill="currentColor" opacity="0.8"/>
                <circle cx="16" cy="12" r="1.3" fill="currentColor" opacity="0.8"/>
                <!-- Chat context lines -->
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M7 9h10m-10 4h6" opacity="0.6"/>
                <!-- Communication activity indicators -->
                <circle cx="18" cy="8" r="1.5" stroke="currentColor" stroke-width="1" opacity="0.4"/>
                <circle cx="6" cy="16" r="1.5" stroke="currentColor" stroke-width="1" opacity="0.4"/>
                <!-- Live conversation pulse -->
                <circle cx="12" cy="12" r="6" stroke="currentColor" stroke-width="1" opacity="0.2"/>
                <!-- Message delivery indicators -->
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M7 19l-2 1" opacity="0.7"/>
                <!-- Online status markers -->
                <circle cx="19" cy="7" r="0.8" fill="currentColor" opacity="0.5"/>
                <circle cx="5" cy="17" r="0.8" fill="currentColor" opacity="0.5"/>
                <!-- Message flow -->
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 10l3 3-3 3m6-6l3 3-3 3" opacity="0.3"/>
              </g>
            </svg>
          </div>
          <div class="flex-1">
            <div class="font-semibold">Messages</div>
            <div class="text-xs opacity-75 mt-0.5">Chat & notifications</div>
          </div>
          <% if @unread_messages_count && @unread_messages_count > 0 %>
            <div class="flex items-center space-x-2">
              <span class="bg-gradient-to-r from-blue-500 to-cyan-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center font-semibold shadow-lg">
                <%= @unread_messages_count > 9 ? '9+' : @unread_messages_count %>
              </span>
              <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            </div>
          <% end %>
        <% end %>

        <%= link_to maintenance_requests_path,
            class: "group flex items-center px-5 py-4 text-sm font-semibold rounded-2xl transition-all duration-300 transform hover:scale-[1.02] #{current_page?(maintenance_requests_path) ? 'bg-gradient-to-r from-orange-50 via-red-50 to-pink-50 text-orange-700 border border-orange-200/50 shadow-lg shadow-orange-500/10' : 'text-gray-700 hover:bg-gradient-to-r hover:from-gray-50 hover:to-slate-50 hover:text-gray-900 hover:shadow-lg'}" do %>
          <div class="w-10 h-10 rounded-xl flex items-center justify-center mr-4 transition-all duration-300 #{current_page?(maintenance_requests_path) ? 'bg-gradient-to-br from-orange-500 via-red-600 to-pink-600 text-white shadow-xl shadow-orange-500/25' : 'bg-gradient-to-br from-gray-100 to-gray-200 text-gray-600 group-hover:from-orange-100 group-hover:to-red-100 group-hover:text-orange-600 group-hover:shadow-lg'}">
            <!-- Premium Maintenance/Tools Icon -->
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <g opacity="0.95">
                <!-- Enhanced maintenance gear with tools -->
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                <!-- Central gear mechanism -->
                <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2" fill="currentColor" opacity="0.5"/>
                <!-- Tool details and enhancements -->
                <circle cx="12" cy="12" r="1.5" fill="currentColor" opacity="0.8"/>
                <!-- Maintenance tool indicators -->
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 9l6 6m0-6l-6 6" opacity="0.6"/>
                <!-- Work status markers -->
                <circle cx="8" cy="8" r="1" stroke="currentColor" stroke-width="1" opacity="0.5"/>
                <circle cx="16" cy="8" r="1" stroke="currentColor" stroke-width="1" opacity="0.5"/>
                <circle cx="8" cy="16" r="1" stroke="currentColor" stroke-width="1" opacity="0.5"/>
                <circle cx="16" cy="16" r="1" stroke="currentColor" stroke-width="1" opacity="0.5"/>
                <!-- Service progress indicators -->
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M6 12h2m8 0h2m-6-6v2m0 8v2" opacity="0.4"/>
                <!-- Quality assurance markers -->
                <rect x="10.5" y="6" width="3" height="1" rx="0.5" fill="currentColor" opacity="0.4"/>
                <rect x="10.5" y="17" width="3" height="1" rx="0.5" fill="currentColor" opacity="0.4"/>
                <rect x="6" y="10.5" width="1" height="3" rx="0.5" fill="currentColor" opacity="0.4"/>
                <rect x="17" y="10.5" width="1" height="3" rx="0.5" fill="currentColor" opacity="0.4"/>
              </g>
            </svg>
          </div>
          <div class="flex-1">
            <div class="font-semibold"><%= current_user.landlord? ? "Maintenance" : "Requests" %></div>
            <div class="text-xs opacity-75 mt-0.5">Service management</div>
          </div>
          <% if @pending_maintenance_count && @pending_maintenance_count > 0 %>
            <div class="flex items-center space-x-2">
              <span class="bg-gradient-to-r from-orange-500 to-red-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center font-semibold shadow-lg">
                <%= @pending_maintenance_count > 9 ? '9+' : @pending_maintenance_count %>
              </span>
              <div class="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
            </div>
          <% end %>
        <% end %>
      </div>
    </div>
  </nav>

  <!-- Enhanced Sidebar Footer -->
  <div class="px-6 py-6 border-t border-gray-200/30 bg-gradient-to-r from-slate-50/50 to-white/50 flex-shrink-0">
    <div class="space-y-3">
      <%= link_to edit_profile_path,
          class: "group flex items-center px-4 py-3 text-sm font-semibold rounded-xl transition-all duration-300 text-gray-700 hover:bg-gradient-to-r hover:from-slate-50 hover:to-gray-50 hover:text-gray-900 transform hover:scale-[1.02]" do %>
        <div class="w-8 h-8 rounded-lg flex items-center justify-center mr-3 transition-all duration-300 bg-gradient-to-br from-gray-100 to-gray-200 text-gray-500 group-hover:from-slate-100 group-hover:to-gray-100 group-hover:text-slate-600">
          <!-- Premium Settings Icon -->
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <g opacity="0.9">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
              <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2" fill="currentColor" opacity="0.3"/>
              <circle cx="12" cy="12" r="1" fill="currentColor" opacity="0.8"/>
            </g>
          </svg>
        </div>
        <div>
          <div class="font-semibold">Settings</div>
          <div class="text-xs opacity-75">Account & preferences</div>
        </div>
      <% end %>

      <%= link_to help_path, 
          class: "group flex items-center px-4 py-3 text-sm font-semibold rounded-xl transition-all duration-300 text-gray-700 hover:bg-gradient-to-r hover:from-slate-50 hover:to-gray-50 hover:text-gray-900 transform hover:scale-[1.02]" do %>
        <div class="w-8 h-8 rounded-lg flex items-center justify-center mr-3 transition-all duration-300 bg-gradient-to-br from-gray-100 to-gray-200 text-gray-500 group-hover:from-blue-100 group-hover:to-indigo-100 group-hover:text-blue-600">
          <!-- Premium Help Icon -->
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <g opacity="0.9">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              <circle cx="12" cy="19" r="1.5" fill="currentColor" opacity="0.6"/>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M15 9a3 3 0 11-6 0 3 3 0 016 0z" opacity="0.5"/>
              <circle cx="12" cy="12" r="8" stroke="currentColor" stroke-width="0.5" opacity="0.3"/>
            </g>
          </svg>
        </div>
        <div>
          <div class="font-semibold">Help & Support</div>
          <div class="text-xs opacity-75">Get assistance</div>
        </div>
      <% end %>
    </div>
  </div>
</div>

<!-- Mobile Sidebar Overlay -->
<div class="fixed inset-0 z-40 bg-gray-900/50 backdrop-blur-sm transition-opacity duration-300 ease-linear lg:hidden hidden"
     data-sidebar-target="overlay"
     data-action="click->sidebar#close"></div>
