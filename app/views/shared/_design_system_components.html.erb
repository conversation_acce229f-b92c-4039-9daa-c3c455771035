<%# Ofie Design System Components Library %>
<%# This file contains reusable components following the established design patterns %>

<%# ===== GLASS MORPHISM CARDS ===== %>

<%# Primary Card Component %>
<% content_for :glass_card do |title:, icon_class: "from-blue-500 to-indigo-600", icon_svg:, hover: true| %>
  <div class="group bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 <%= 'hover:shadow-2xl hover:-translate-y-1' if hover %> transition-all duration-300 p-8">
    <div class="flex items-center space-x-3 mb-6">
      <div class="w-10 h-10 bg-gradient-to-r <%= icon_class %> rounded-2xl flex items-center justify-center shadow-lg">
        <%= icon_svg %>
      </div>
      <h3 class="text-2xl font-bold text-gray-900"><%= title %></h3>
    </div>
    <%= yield %>
  </div>
<% end %>

<%# ===== FORM COMPONENTS ===== %>

<%# Enhanced Form Input %>
<% content_for :form_input do |form:, field:, label:, type: :text_field, icon_svg: nil, placeholder: "", required: false, **options| %>
  <div class="space-y-2">
    <%= form.label field, class: "block text-sm font-bold text-gray-700" do %>
      <%= icon_svg if icon_svg %>
      <%= label %>
      <span class="text-red-500">*</span> if required
    <% end %>
    <%= form.send(type, field, {
      class: "w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 font-medium placeholder-gray-400 hover:border-gray-300",
      placeholder: placeholder,
      required: required
    }.merge(options)) %>
  </div>
<% end %>

<%# Enhanced Select Input %>
<% content_for :form_select do |form:, field:, label:, options:, icon_svg: nil, prompt: nil, required: false, **html_options| %>
  <div class="space-y-2">
    <%= form.label field, class: "block text-sm font-bold text-gray-700" do %>
      <%= icon_svg if icon_svg %>
      <%= label %>
      <span class="text-red-500">*</span> if required
    <% end %>
    <%= form.select field, options, 
        { prompt: prompt }, 
        { 
          class: "w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 font-medium hover:border-gray-300",
          required: required
        }.merge(html_options) %>
  </div>
<% end %>

<%# ===== BUTTON COMPONENTS ===== %>

<%# Primary Button %>
<% content_for :btn_primary do |text:, href: nil, **options| %>
  <% if href %>
    <%= link_to href, {
      class: "group inline-flex items-center justify-center px-6 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
    }.merge(options) do %>
      <%= text %>
    <% end %>
  <% else %>
    <button type="submit" class="group inline-flex items-center justify-center px-6 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 <%= options[:class] %>">
      <%= text %>
    </button>
  <% end %>
<% end %>

<%# Secondary Button %>
<% content_for :btn_secondary do |text:, href: nil, **options| %>
  <% if href %>
    <%= link_to href, {
      class: "group inline-flex items-center justify-center px-6 py-3 border-2 border-gray-200 text-gray-700 bg-white/80 backdrop-blur-sm hover:bg-white hover:border-gray-300 rounded-2xl font-semibold transition-all duration-300 transform hover:-translate-y-0.5 hover:shadow-lg"
    }.merge(options) do %>
      <%= text %>
    <% end %>
  <% else %>
    <button type="button" class="group inline-flex items-center justify-center px-6 py-3 border-2 border-gray-200 text-gray-700 bg-white/80 backdrop-blur-sm hover:bg-white hover:border-gray-300 rounded-2xl font-semibold transition-all duration-300 transform hover:-translate-y-0.5 hover:shadow-lg <%= options[:class] %>">
      <%= text %>
    </button>
  <% end %>
<% end %>

<%# Success Button %>
<% content_for :btn_success do |text:, href: nil, **options| %>
  <% if href %>
    <%= link_to href, {
      class: "group inline-flex items-center justify-center px-6 py-4 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
    }.merge(options) do %>
      <%= text %>
    <% end %>
  <% else %>
    <button type="submit" class="group inline-flex items-center justify-center px-6 py-4 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 <%= options[:class] %>">
      <%= text %>
    </button>
  <% end %>
<% end %>

<%# Warning Button %>
<% content_for :btn_warning do |text:, href: nil, **options| %>
  <% if href %>
    <%= link_to href, {
      class: "group inline-flex items-center justify-center px-6 py-4 bg-gradient-to-r from-amber-600 to-orange-600 hover:from-amber-700 hover:to-orange-700 text-white rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
    }.merge(options) do %>
      <%= text %>
    <% end %>
  <% else %>
    <button type="submit" class="group inline-flex items-center justify-center px-6 py-4 bg-gradient-to-r from-amber-600 to-orange-600 hover:from-amber-700 hover:to-orange-700 text-white rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 <%= options[:class] %>">
      <%= text %>
    </button>
  <% end %>
<% end %>

<%# ===== ICON CONTAINERS ===== %>

<%# Primary Icon Container %>
<% content_for :icon_primary do |size: "w-10 h-10", icon_size: "w-6 h-6"| %>
  <div class="<%= size %> bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg shadow-blue-500/25">
    <svg class="<%= icon_size %> text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <%= yield %>
    </svg>
  </div>
<% end %>

<%# Success Icon Container %>
<% content_for :icon_success do |size: "w-10 h-10", icon_size: "w-6 h-6"| %>
  <div class="<%= size %> bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center shadow-lg shadow-green-500/25">
    <svg class="<%= icon_size %> text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <%= yield %>
    </svg>
  </div>
<% end %>

<%# Warning Icon Container %>
<% content_for :icon_warning do |size: "w-10 h-10", icon_size: "w-6 h-6"| %>
  <div class="<%= size %> bg-gradient-to-r from-amber-500 to-orange-600 rounded-2xl flex items-center justify-center shadow-lg shadow-amber-500/25">
    <svg class="<%= icon_size %> text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <%= yield %>
    </svg>
  </div>
<% end %>

<%# Error Icon Container %>
<% content_for :icon_error do |size: "w-10 h-10", icon_size: "w-6 h-6"| %>
  <div class="<%= size %> bg-gradient-to-r from-red-500 to-pink-600 rounded-2xl flex items-center justify-center shadow-lg shadow-red-500/25">
    <svg class="<%= icon_size %> text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <%= yield %>
    </svg>
  </div>
<% end %>

<%# ===== BACKGROUND PATTERNS ===== %>

<%# Main Background with Pattern %>
<% content_for :main_background do %>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 bg-grid-slate-100/50 [mask-image:linear-gradient(0deg,transparent,black)] pointer-events-none"></div>
    <div class="absolute top-0 right-0 w-96 h-96 bg-gradient-to-l from-purple-300/20 to-pink-300/20 rounded-full blur-3xl"></div>
    <div class="absolute bottom-0 left-0 w-96 h-96 bg-gradient-to-r from-blue-300/20 to-cyan-300/20 rounded-full blur-3xl"></div>
    
    <div class="relative z-10">
      <%= yield %>
    </div>
  </div>
<% end %>

<%# ===== STATUS INDICATORS ===== %>

<%# Status Badge %>
<% content_for :status_badge do |status:, text:| %>
  <% 
    case status.to_s.downcase
    when 'success', 'active', 'approved'
      classes = "bg-green-100 text-green-800 border-green-200"
    when 'warning', 'pending'
      classes = "bg-amber-100 text-amber-800 border-amber-200"
    when 'error', 'rejected', 'inactive'
      classes = "bg-red-100 text-red-800 border-red-200"
    else
      classes = "bg-blue-100 text-blue-800 border-blue-200"
    end
  %>
  <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border <%= classes %>">
    <%= text %>
  </span>
<% end %>

<%# ===== LOADING STATES ===== %>

<%# Skeleton Loader %>
<% content_for :skeleton_card do %>
  <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-8 animate-pulse">
    <div class="flex items-center space-x-3 mb-6">
      <div class="w-10 h-10 bg-gray-300 rounded-2xl"></div>
      <div class="h-6 bg-gray-300 rounded w-1/3"></div>
    </div>
    <div class="space-y-3">
      <div class="h-4 bg-gray-300 rounded w-full"></div>
      <div class="h-4 bg-gray-300 rounded w-3/4"></div>
      <div class="h-4 bg-gray-300 rounded w-1/2"></div>
    </div>
  </div>
<% end %>

<%# ===== COMMON SVG ICONS ===== %>

<%# Property Icon %>
<% content_for :icon_property do %>
  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
<% end %>

<%# User Icon %>
<% content_for :icon_user do %>
  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
<% end %>

<%# Settings Icon %>
<% content_for :icon_settings do %>
  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
<% end %>

<%# Check Icon %>
<% content_for :icon_check do %>
  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
<% end %>

<%# X Icon %>
<% content_for :icon_x do %>
  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
<% end %>

<style>
  .bg-grid-slate-100\/50 {
    background-image: url("data:image/svg+xml,%3csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3e%3cg fill='%23f1f5f9' fill-opacity='0.3' fill-rule='evenodd'%3e%3cpath d='m0 40 40-40h-40v40zm40 0v-40h-40z'/%3e%3c/g%3e%3c/svg%3e");
  }
</style>
