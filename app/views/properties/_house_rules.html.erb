<!-- Enhanced House Rules -->
<div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl shadow-gray-200/50 border border-white/20 p-8 sticky top-8 hover:shadow-3xl transition-all duration-300">
  <div class="flex items-center justify-between mb-8">
    <div class="flex items-center space-x-4">
      <div class="w-10 h-10 bg-gradient-to-br from-amber-500 to-orange-600 rounded-2xl flex items-center justify-center shadow-lg">
        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        </svg>
      </div>
      <h3 class="text-2xl font-bold text-slate-800">House Rules</h3>
    </div>
    <div class="h-1 w-12 bg-gradient-to-r from-amber-500 to-orange-600 rounded-full"></div>
  </div>

  <div class="space-y-6">
    <!-- Check-in/Check-out Times -->
    <div class="space-y-4">
      <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-200/50 hover:shadow-lg transition-all duration-200">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
              </svg>
            </div>
            <div>
              <h4 class="text-lg font-bold text-blue-900">Check-in</h4>
              <p class="text-blue-700 text-sm">Move-in time</p>
            </div>
          </div>
          <span class="text-blue-900 font-bold text-lg">3:00 PM - 9:00 PM</span>
        </div>
      </div>

      <div class="bg-gradient-to-r from-emerald-50 to-teal-50 rounded-2xl p-6 border border-emerald-200/50 hover:shadow-lg transition-all duration-200">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="w-12 h-12 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center shadow-lg">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
              </svg>
            </div>
            <div>
              <h4 class="text-lg font-bold text-emerald-900">Check-out</h4>
              <p class="text-emerald-700 text-sm">Move-out time</p>
            </div>
          </div>
          <span class="text-emerald-900 font-bold text-lg">11:00 AM</span>
        </div>
      </div>

      <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl p-6 border border-purple-200/50 hover:shadow-lg transition-all duration-200">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center shadow-lg">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
              </svg>
            </div>
            <div>
              <h4 class="text-lg font-bold text-purple-900">Maximum guests</h4>
              <p class="text-purple-700 text-sm">Occupancy limit</p>
            </div>
          </div>
          <span class="text-purple-900 font-bold text-lg">4 guests</span>
        </div>
      </div>
    </div>

    <!-- Restrictions -->
    <div class="border-t border-slate-200/50 pt-6">
      <h4 class="text-lg font-bold text-slate-800 mb-4 flex items-center">
        <svg class="w-5 h-5 mr-2 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
        </svg>
        Important Restrictions
      </h4>
      <div class="space-y-3">
        <div class="flex items-center p-4 bg-gradient-to-r from-red-50 to-rose-50 rounded-2xl border border-red-200/50">
          <div class="w-8 h-8 bg-gradient-to-br from-red-500 to-rose-600 rounded-xl flex items-center justify-center mr-4 flex-shrink-0">
            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </div>
          <span class="text-red-800 font-semibold text-lg">No smoking anywhere in the property</span>
        </div>
        
        <div class="flex items-center p-4 bg-gradient-to-r from-red-50 to-rose-50 rounded-2xl border border-red-200/50">
          <div class="w-8 h-8 bg-gradient-to-br from-red-500 to-rose-600 rounded-xl flex items-center justify-center mr-4 flex-shrink-0">
            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </div>
          <span class="text-red-800 font-semibold text-lg">No pets allowed</span>
        </div>
        
        <div class="flex items-center p-4 bg-gradient-to-r from-red-50 to-rose-50 rounded-2xl border border-red-200/50">
          <div class="w-8 h-8 bg-gradient-to-br from-red-500 to-rose-600 rounded-xl flex items-center justify-center mr-4 flex-shrink-0">
            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </div>
          <span class="text-red-800 font-semibold text-lg">No parties or large gatherings</span>
        </div>
      </div>
    </div>

    <!-- Additional Rules -->
    <div class="border-t border-slate-200/50 pt-6">
      <h4 class="text-lg font-bold text-slate-800 mb-4 flex items-center">
        <svg class="w-5 h-5 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        Additional Guidelines
      </h4>
      <div class="space-y-3">
        <div class="flex items-start p-4 bg-slate-50 rounded-2xl border border-slate-200/50">
          <div class="w-2 h-2 bg-slate-400 rounded-full mt-3 mr-3 flex-shrink-0"></div>
          <span class="text-slate-700 font-medium">Please respect quiet hours from 10 PM to 8 AM</span>
        </div>
        
        <div class="flex items-start p-4 bg-slate-50 rounded-2xl border border-slate-200/50">
          <div class="w-2 h-2 bg-slate-400 rounded-full mt-3 mr-3 flex-shrink-0"></div>
          <span class="text-slate-700 font-medium">Keep common areas clean and tidy</span>
        </div>
        
        <div class="flex items-start p-4 bg-slate-50 rounded-2xl border border-slate-200/50">
          <div class="w-2 h-2 bg-slate-400 rounded-full mt-3 mr-3 flex-shrink-0"></div>
          <span class="text-slate-700 font-medium">Please notify host of any maintenance issues promptly</span>
        </div>
      </div>
    </div>
  </div>
</div>