<% content_for :title, "Browse Properties - Ofie" %>
<% content_for :description, "Discover amazing rental properties with the best amenities and competitive prices on Ofie's advanced property platform." %>

<!-- Enhanced Hero Section with Sophisticated Search -->
<div class="relative min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 overflow-hidden">
  <!-- Background Effects -->
  <div class="absolute inset-0 bg-gradient-to-br from-black/30 via-transparent to-black/20"></div>
  <div class="absolute top-0 left-1/4 w-96 h-96 bg-gradient-to-r from-blue-400/20 to-purple-500/20 rounded-full blur-3xl"></div>
  <div class="absolute bottom-0 right-1/4 w-96 h-96 bg-gradient-to-l from-emerald-400/20 to-teal-500/20 rounded-full blur-3xl"></div>
  
  <!-- Floating Elements -->
  <div class="absolute top-20 right-10 w-32 h-32 bg-white/5 rounded-full blur-2xl animate-pulse"></div>
  <div class="absolute bottom-40 left-20 w-48 h-48 bg-blue-400/10 rounded-full blur-3xl animate-pulse" style="animation-delay: 2s;"></div>
  
  <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
    <!-- Hero Content -->
    <div class="text-center text-white mb-16">
      <div class="mb-8">
        <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-3xl mb-8 shadow-2xl shadow-blue-500/25">
          <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
          </svg>
        </div>
        <h1 class="text-6xl md:text-7xl font-bold mb-6 leading-tight">
          Find Your <span class="bg-gradient-to-r from-blue-400 via-purple-400 to-indigo-400 bg-clip-text text-transparent">Perfect</span> Home
        </h1>
        <p class="text-2xl md:text-3xl text-blue-100 mb-8 max-w-4xl mx-auto leading-relaxed">
          Discover amazing properties with premium amenities and competitive prices
        </p>
      </div>
      
      <!-- Stats -->
      <div class="flex flex-wrap items-center justify-center gap-8 text-blue-200 mb-12">
        <div class="flex items-center space-x-3 bg-white/10 backdrop-blur-sm rounded-2xl px-6 py-3 border border-white/20">
          <div class="w-10 h-10 bg-gradient-to-r from-emerald-400 to-teal-500 rounded-xl flex items-center justify-center">
            <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
          </div>
          <span class="font-bold text-lg"><%= @properties.count %>+ Properties</span>
        </div>
        <div class="flex items-center space-x-3 bg-white/10 backdrop-blur-sm rounded-2xl px-6 py-3 border border-white/20">
          <div class="w-10 h-10 bg-gradient-to-r from-amber-400 to-orange-500 rounded-xl flex items-center justify-center">
            <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <span class="font-bold text-lg">Verified Listings</span>
        </div>
        <div class="flex items-center space-x-3 bg-white/10 backdrop-blur-sm rounded-2xl px-6 py-3 border border-white/20">
          <div class="w-10 h-10 bg-gradient-to-r from-purple-400 to-indigo-500 rounded-xl flex items-center justify-center">
            <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
          </div>
          <span class="font-bold text-lg">Instant Connect</span>
        </div>
      </div>
    </div>

    <!-- Enhanced Advanced Search Form -->
    <div class="bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl shadow-black/20 p-8 max-w-7xl mx-auto border border-white/20">
      <div class="mb-8 text-center">
        <h2 class="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-3">
          Advanced Property Search
        </h2>
        <p class="text-gray-600 text-lg">Find your dream home with our comprehensive search filters</p>
      </div>
    
      <%= form_with url: properties_path, 
                    method: :get, 
                    local: true, 
                    class: "space-y-8", 
                    data: { 
                      search_target: "form",
                      action: "submit->search#submit input->search#input change->search#input"
                    } do |form| %>
        
        <!-- Main Search Bar -->
        <div class="relative group">
          <div class="absolute inset-y-0 left-0 pl-6 flex items-center pointer-events-none">
            <svg class="h-6 w-6 text-gray-400 group-focus-within:text-blue-500 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
          <%= form.text_field :search, 
                              value: params[:search],
                              placeholder: "Search by location, property type, or keywords...",
                              class: "w-full pl-16 pr-6 py-5 text-lg font-medium border-3 border-gray-200 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 shadow-lg hover:shadow-xl hover:border-gray-300 bg-white" %>
          <div class="absolute inset-y-0 right-0 pr-6 flex items-center">
            <button type="submit" class="group bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg">
              <svg class="w-5 h-5 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </button>
          </div>
        </div>
      
        <!-- Enhanced Filter Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
          <!-- Location -->
          <div class="space-y-2">
            <label class="block text-sm font-bold text-gray-700">
              <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
              Location
            </label>
            <%= form.text_field :city, 
                                value: params[:city],
                                placeholder: "Enter city",
                                class: "w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 font-medium hover:border-gray-300" %>
          </div>
          
          <!-- Property Type -->
          <div class="space-y-2">
            <label class="block text-sm font-bold text-gray-700">
              <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
              </svg>
              Property Type
            </label>
            <%= form.select :property_type, 
                            options_for_select([['Any Type', '']] + Property.property_types.map { |key, value| [key.humanize, key] }, params[:property_type]),
                            {},
                            { class: "w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 font-medium hover:border-gray-300" } %>
          </div>
          
          <!-- Bedrooms -->
          <div class="space-y-2">
            <label class="block text-sm font-bold text-gray-700">
              <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
              </svg>
              Bedrooms
            </label>
            <%= form.select :bedrooms, 
                            options_for_select([['Any', '']] + (1..5).map { |i| ["#{i}+", i] }, params[:bedrooms]),
                            {},
                            { class: "w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 font-medium hover:border-gray-300" } %>
          </div>
          
          <!-- Bathrooms -->
          <div class="space-y-2">
            <label class="block text-sm font-bold text-gray-700">
              <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10h16v11H4V10z"></path>
              </svg>
              Bathrooms
            </label>
            <%= form.select :bathrooms, 
                            options_for_select([['Any', '']] + (1..4).map { |i| ["#{i}+", i] }, params[:bathrooms]),
                            {},
                            { class: "w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 font-medium hover:border-gray-300" } %>
          </div>
          
          <!-- Price Range Toggle -->
          <div class="space-y-2">
            <label class="block text-sm font-bold text-gray-700">
              <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
              </svg>
              Price Range
            </label>
            <button type="button" onclick="togglePriceRange()" class="w-full px-4 py-3 border-2 border-gray-200 rounded-xl font-medium text-gray-700 hover:border-gray-300 hover:bg-gray-50 transition-all duration-200 text-left">
              Set Price Range
            </button>
          </div>
        </div>
      
        <!-- Collapsible Price Range -->
        <div id="price-range" class="hidden">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl border border-blue-200">
            <div class="space-y-2">
              <label class="block text-sm font-bold text-gray-700">Min Price ($)</label>
              <%= form.number_field :min_price, 
                                    value: params[:min_price],
                                    placeholder: "0",
                                    class: "w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 font-medium" %>
            </div>
            
            <div class="space-y-2">
              <label class="block text-sm font-bold text-gray-700">Max Price ($)</label>
              <%= form.number_field :max_price, 
                                    value: params[:max_price],
                                    placeholder: "No limit",
                                    class: "w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 font-medium" %>
            </div>
          </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <%= form.submit "Search Properties", 
                          class: "group bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-bold py-4 px-8 rounded-2xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" %>
          <%= link_to "Clear All Filters", properties_path, 
                      class: "bg-gradient-to-r from-gray-100 to-gray-200 hover:from-gray-200 hover:to-gray-300 text-gray-700 font-semibold py-4 px-8 rounded-2xl transition-all duration-300 border-2 border-gray-200 hover:border-gray-300 transform hover:-translate-y-0.5" %>
        </div>
      <% end %>
    </div>
  </div>
</div>

<!-- Main Content Section -->
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 relative">
  <!-- Background Pattern -->
  <div class="absolute inset-0 bg-grid-slate-100/50 [mask-image:linear-gradient(0deg,transparent,black)] pointer-events-none"></div>
  
  <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16" 
       data-controller="search" 
       data-search-url-value="<%= properties_path %>">
    
    <!-- Enhanced Landlord Actions -->
    <% if user_signed_in? && current_user.landlord? %>
      <div class="mb-12">
        <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-8 text-center">
          <div class="flex items-center justify-center mb-4">
            <div class="w-12 h-12 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg shadow-emerald-500/25">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
              </svg>
            </div>
            <h3 class="text-2xl font-bold text-gray-900">Landlord Dashboard</h3>
          </div>
          <p class="text-gray-600 mb-6">Manage your properties and reach more tenants</p>
          <div class="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-4">
            <%= link_to new_property_path, 
                        class: "group bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-8 py-4 rounded-2xl font-bold transition-all duration-300 shadow-lg shadow-blue-500/25 hover:shadow-xl transform hover:-translate-y-0.5" do %>
              <svg class="h-5 w-5 inline mr-2 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              Add New Property
            <% end %>
            <%= link_to "My Dashboard", my_properties_properties_path, 
                        class: "group border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white px-8 py-4 rounded-2xl font-bold transition-all duration-300 transform hover:-translate-y-0.5" %>
          </div>
        </div>
      </div>
    <% end %>

    <!-- Enhanced Results Section -->
    <div data-search-target="results">
      <!-- Results Header -->
      <div class="mb-12">
        <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-8">
          <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
            <div>
              <h2 class="text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-3">
                <%= pluralize(@properties.count, 'Property') %> Available
              </h2>
              <p class="text-lg text-gray-600">Showing all available properties matching your criteria</p>
            </div>
            <div class="flex flex-col sm:flex-row items-stretch sm:items-center gap-4">
              <div class="flex items-center text-sm text-gray-500 bg-gray-100 px-4 py-2 rounded-xl">
                <svg class="w-4 h-4 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Updated <%= time_ago_in_words(Time.current) %> ago
              </div>
              <!-- Enhanced Sort Options -->
              <select class="px-4 py-3 bg-white border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 font-medium transition-all duration-200 hover:border-gray-300">
                <option>Sort by: Featured</option>
                <option>Price: Low to High</option>
                <option>Price: High to Low</option>
                <option>Newest First</option>
                <option>Most Popular</option>
              </select>
              <!-- View Toggle -->
              <div class="flex bg-gray-100 rounded-xl p-1">
                <button class="px-4 py-2 bg-white rounded-lg shadow-sm font-medium text-gray-900 transition-all duration-200">Grid</button>
                <button class="px-4 py-2 text-gray-600 hover:text-gray-900 rounded-lg transition-all duration-200">List</button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Enhanced Properties Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <% @properties.each do |property| %>
          <div class="group bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 overflow-hidden hover:shadow-2xl hover:shadow-gray-200/60 transition-all duration-500 transform hover:-translate-y-2">
            <!-- Enhanced Property Image -->
            <div class="relative h-72 bg-gradient-to-br from-gray-100 to-gray-200 overflow-hidden">
              <% if property.photos.attached? && property.photos.any? %>
                <%= image_tag property.photos.first, 
                             alt: property.title,
                             class: "w-full h-full object-cover transition-transform duration-700 group-hover:scale-110" %>
              <% else %>
                <div class="w-full h-full flex items-center justify-center text-gray-400 bg-gradient-to-br from-gray-100 to-gray-200">
                  <div class="text-center">
                    <svg class="w-16 h-16 mx-auto mb-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
                    </svg>
                    <p class="text-sm">No image available</p>
                  </div>
                </div>
              <% end %>
              
              <!-- Enhanced Badges -->
              <% if property.respond_to?(:featured?) && property.featured? %>
                <div class="absolute top-4 left-4">
                  <span class="inline-flex items-center bg-gradient-to-r from-orange-500 to-red-500 text-white px-3 py-1 rounded-xl text-xs font-bold shadow-lg">
                    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                    FEATURED
                  </span>
                </div>
              <% end %>
              
              <!-- Property Type Badge -->
              <div class="absolute top-4 <%= (property.respond_to?(:featured?) && property.featured?) ? 'right-4' : 'left-4' %>">
                <span class="bg-white/90 backdrop-blur-sm text-gray-800 px-3 py-1 rounded-xl text-xs font-bold shadow-lg border border-white/20">
                  <%= property.property_type&.humanize || 'Property' %>
                </span>
              </div>
              
              <!-- Enhanced Favorite Button -->
              <div class="absolute top-4 right-4">
                <button class="favorite-btn group/fav w-10 h-10 bg-white/90 backdrop-blur-sm hover:bg-white text-gray-600 hover:text-red-500 rounded-full shadow-lg transition-all duration-300 opacity-0 group-hover:opacity-100 transform hover:scale-110 flex items-center justify-center">
                  <svg class="w-5 h-5 group-hover/fav:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                  </svg>
                </button>
              </div>
              
              <!-- Enhanced Price Badge -->
              <div class="absolute bottom-4 left-4">
                <span class="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-4 py-2 rounded-2xl text-lg font-bold shadow-lg border border-white/20">
                  $<%= number_with_delimiter(property.price) %>
                  <span class="text-sm font-medium opacity-90">/month</span>
                </span>
              </div>
            </div>
             
            <!-- Enhanced Property Content -->
            <div class="p-8">
              <!-- Title and Location -->
              <div class="mb-6">
                <h3 class="text-xl font-bold text-gray-900 mb-3 line-clamp-2 leading-tight group-hover:text-blue-600 transition-colors duration-200">
                  <%= link_to property.title, property_path(property), class: "hover:text-blue-600 transition-colors duration-200" %>
                </h3>
                <div class="flex items-center text-gray-600">
                  <svg class="w-4 h-4 mr-2 flex-shrink-0 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  </svg>
                  <span class="truncate font-medium"><%= property.address %>, <%= property.city %></span>
                </div>
              </div>
              
              <!-- Enhanced Property Features -->
              <div class="flex items-center justify-between mb-6">
                <div class="flex items-center space-x-6 text-gray-600">
                  <div class="flex items-center bg-gray-50 px-3 py-2 rounded-xl">
                    <svg class="w-4 h-4 mr-2 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                    </svg>
                    <span class="font-bold text-gray-900"><%= property.bedrooms %></span>
                  </div>
                  <div class="flex items-center bg-gray-50 px-3 py-2 rounded-xl">
                    <svg class="w-4 h-4 mr-2 text-emerald-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="font-bold text-gray-900"><%= property.bathrooms %></span>
                  </div>
                  <% if property.square_feet %>
                    <div class="flex items-center bg-gray-50 px-3 py-2 rounded-xl">
                      <svg class="w-4 h-4 mr-2 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                      </svg>
                      <span class="font-bold text-gray-900"><%= number_with_delimiter(property.square_feet) %></span>
                    </div>
                  <% end %>
                </div>
                <div>
                  <span class="inline-flex items-center px-3 py-1 text-xs font-bold rounded-full
                    <%= property.available? ? 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800' : 'bg-gradient-to-r from-red-100 to-pink-100 text-red-800' %>">
                    <%= property.availability_status&.humanize || 'Available' %>
                  </span>
                </div>
              </div>
              
              <!-- Enhanced Description -->
              <p class="text-gray-600 mb-6 line-clamp-2 leading-relaxed">
                <%= truncate(property.description, length: 120) %>
              </p>
              
              <!-- Enhanced Action Buttons -->
              <div class="space-y-3">
                <%= link_to "View Property", property_path(property), 
                            class: "group block w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white text-center py-4 px-6 rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" %>
                <div class="flex space-x-3">
                  <button class="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 py-3 px-4 rounded-xl font-semibold transition-all duration-200 flex items-center justify-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                    Contact
                  </button>
                  <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 p-3 rounded-xl transition-all duration-200">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        <% end %>
      </div>
      
      <!-- Enhanced Load More / Pagination -->
      <% if @properties.count >= 12 %>
        <div class="text-center mt-16">
          <button class="group bg-white/80 backdrop-blur-xl border-2 border-gray-200 hover:border-blue-500 text-gray-700 hover:text-blue-600 font-bold py-4 px-8 rounded-2xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
            <svg class="w-5 h-5 inline mr-2 group-hover:animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            Load More Properties
          </button>
        </div>
      <% end %>
    </div>
  </div>
</div>

<script>
  function togglePriceRange() {
    const element = document.getElementById('price-range');
    element.classList.toggle('hidden');
  }

  document.addEventListener('DOMContentLoaded', function() {
    // Enhanced favorite button functionality
    const favoriteButtons = document.querySelectorAll('.favorite-btn');
    favoriteButtons.forEach(button => {
      button.addEventListener('click', function(e) {
        e.preventDefault();
        this.classList.toggle('text-red-500');
        this.classList.toggle('text-gray-600');
        
        // Add pulse animation
        this.classList.add('animate-pulse');
        setTimeout(() => {
          this.classList.remove('animate-pulse');
        }, 600);
      });
    });

    // Property card hover effects
    const propertyCards = document.querySelectorAll('.property-card');
    propertyCards.forEach(card => {
      card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-8px)';
      });
      
      card.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0)';
      });
    });

    // Search form enhancements
    const searchInput = document.querySelector('input[name="search"]');
    if (searchInput) {
      searchInput.addEventListener('focus', function() {
        this.parentElement.classList.add('ring-2', 'ring-blue-500');
      });
      
      searchInput.addEventListener('blur', function() {
        this.parentElement.classList.remove('ring-2', 'ring-blue-500');
      });
    }
  });
</script>

<style>
  .bg-grid-slate-100\/50 {
    background-image: url("data:image/svg+xml,%3csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3e%3cg fill='%23f1f5f9' fill-opacity='0.3' fill-rule='evenodd'%3e%3cpath d='m0 40 40-40h-40v40zm40 0v-40h-40z'/%3e%3c/g%3e%3c/svg%3e");
  }
  
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
</style>
