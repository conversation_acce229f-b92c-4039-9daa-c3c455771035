<!-- Property Location Map -->
<div class="bg-white rounded-2xl shadow-lg overflow-hidden mb-8">
  <div class="p-6">
    <h2 class="text-2xl font-bold text-gray-900 mb-4">Location</h2>
    
    <!-- Address Display -->
    <div class="mb-4">
      <div class="flex items-start space-x-3">
        <div class="flex-shrink-0 mt-1">
          <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
          </svg>
        </div>
        <div>
          <p class="text-lg font-medium text-gray-900"><%= @property.address %></p>
          <p class="text-gray-600"><%= @property.city %></p>
        </div>
      </div>
    </div>
    
    <!-- Map Container -->
    <% if @property.latitude.present? && @property.longitude.present? %>
      <div id="property-map" 
           class="w-full h-80 rounded-xl border border-gray-200"
           data-controller="map"
           data-map-latitude-value="<%= @property.latitude %>"
           data-map-longitude-value="<%= @property.longitude %>"
           data-map-address-value="<%= @property.address %>, <%= @property.city %>">
      </div>
    <% else %>
      <!-- Fallback when no coordinates are available -->
      <div class="w-full h-80 bg-gray-100 rounded-xl border border-gray-200 flex items-center justify-center">
        <div class="text-center text-gray-500">
          <svg class="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
          </svg>
          <p class="text-lg font-medium mb-2">Map Not Available</p>
          <p class="text-sm">Location coordinates are not set for this property</p>
        </div>
      </div>
    <% end %>
    
    <!-- Neighborhood Info -->
    <div class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
      <div class="bg-gray-50 rounded-lg p-4">
        <div class="flex items-center space-x-2 mb-2">
          <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10"></path>
          </svg>
          <h3 class="font-medium text-gray-900">Schools</h3>
        </div>
        <p class="text-sm text-gray-600">Great schools nearby</p>
      </div>
      
      <div class="bg-gray-50 rounded-lg p-4">
        <div class="flex items-center space-x-2 mb-2">
          <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 3H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17M17 13v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01"></path>
          </svg>
          <h3 class="font-medium text-gray-900">Shopping</h3>
        </div>
        <p class="text-sm text-gray-600">Convenient shopping options</p>
      </div>
      
      <div class="bg-gray-50 rounded-lg p-4">
        <div class="flex items-center space-x-2 mb-2">
          <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
          </svg>
          <h3 class="font-medium text-gray-900">Transit</h3>
        </div>
        <p class="text-sm text-gray-600">Public transportation access</p>
      </div>
    </div>
  </div>
</div>

<!-- Leaflet CSS -->
<%= content_for :head do %>
  <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
        integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
        crossorigin="" />
<% end %>

<!-- Leaflet JS -->
<%= content_for :javascript do %>
  <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
          integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
          crossorigin=""></script>
<% end %>