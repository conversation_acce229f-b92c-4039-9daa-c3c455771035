<!-- Enhanced Property Header -->
<div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl shadow-gray-200/50 border border-white/20 p-8 hover:shadow-3xl transition-all duration-300">
  <div class="flex flex-col lg:flex-row lg:items-start lg:justify-between mb-8">
    <div class="flex-1">
      <div class="mb-8">
        <h1 class="text-5xl md:text-6xl font-bold bg-gradient-to-r from-slate-900 via-slate-800 to-slate-700 bg-clip-text text-transparent mb-6 leading-tight">
          <%= @property.title %>
        </h1>
        <div class="flex items-center text-slate-600 mb-8">
          <div class="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
          </div>
          <span class="text-xl font-semibold"><%= @property.address %>, <%= @property.city %></span>
        </div>
      </div>

      <!-- Compact Property Stats -->
      <div class="grid grid-cols-2 md:grid-cols-3 gap-4 mb-8">
        <div class="group bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-4 border border-blue-200/50 hover:border-blue-400 hover:shadow-lg transition-all duration-300">
          <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center shadow-sm group-hover:scale-105 transition-transform duration-200">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
              </svg>
            </div>
            <div>
              <div class="text-xl font-bold text-slate-900 group-hover:text-blue-600 transition-colors duration-200"><%= @property.bedrooms %></div>
              <div class="text-xs font-medium text-slate-600 uppercase tracking-wide">Bed<%= @property.bedrooms != 1 ? 's' : '' %></div>
            </div>
          </div>
        </div>

        <div class="group bg-gradient-to-br from-emerald-50 to-teal-50 rounded-xl p-4 border border-emerald-200/50 hover:border-emerald-400 hover:shadow-lg transition-all duration-300">
          <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-lg flex items-center justify-center shadow-sm group-hover:scale-105 transition-transform duration-200">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10v11M20 10v11"></path>
              </svg>
            </div>
            <div>
              <div class="text-xl font-bold text-slate-900 group-hover:text-emerald-600 transition-colors duration-200"><%= @property.bathrooms %></div>
              <div class="text-xs font-medium text-slate-600 uppercase tracking-wide">Bath<%= @property.bathrooms != 1 ? 's' : '' %></div>
            </div>
          </div>
        </div>

        <% if @property.square_feet.present? %>
          <div class="group bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl p-4 border border-purple-200/50 hover:border-purple-400 hover:shadow-lg transition-all duration-300 col-span-2 md:col-span-1">
            <div class="flex items-center space-x-3">
              <div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center shadow-sm group-hover:scale-105 transition-transform duration-200">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"></path>
                </svg>
              </div>
              <div>
                <div class="text-lg font-bold text-slate-900 group-hover:text-purple-600 transition-colors duration-200"><%= number_with_delimiter(@property.square_feet) %></div>
                <div class="text-xs font-medium text-slate-600 uppercase tracking-wide">Sq Ft</div>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    </div>

    <!-- Enhanced Price Badge -->
    <div class="mt-8 lg:mt-0 lg:ml-8">
      <div class="relative">
        <div class="absolute inset-0 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-3xl opacity-90"></div>
        <div class="relative bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-10 py-8 rounded-3xl text-center shadow-2xl shadow-indigo-500/30 border border-white/20 transform hover:scale-105 transition-all duration-300">
          <div class="text-5xl font-bold mb-3">$<%= number_with_delimiter(@property.price) %></div>
          <div class="text-indigo-100 text-xl font-semibold">per month</div>
          <div class="absolute -top-2 -right-2 w-6 h-6 bg-emerald-500 rounded-full animate-pulse"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Enhanced Availability Status & Features -->
  <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between pt-8 border-t border-slate-200/50 space-y-6 sm:space-y-0">
    <div class="flex flex-wrap items-center gap-4">
      <% if @property.available? %>
        <div class="inline-flex items-center bg-gradient-to-r from-emerald-500 to-green-600 text-white px-6 py-3 rounded-2xl font-bold shadow-xl">
          <div class="w-4 h-4 bg-white rounded-full mr-3 animate-pulse shadow-lg"></div>
          <span>Available Now</span>
        </div>
      <% else %>
        <div class="inline-flex items-center bg-gradient-to-r from-red-500 to-rose-600 text-white px-6 py-3 rounded-2xl font-bold shadow-xl">
          <div class="w-4 h-4 bg-white rounded-full mr-3"></div>
          <span>Currently Rented</span>
        </div>
      <% end %>

      <div class="inline-flex items-center bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-6 py-3 rounded-2xl font-bold shadow-xl">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
        </svg>
        <span>Verified Listing</span>
      </div>
    </div>

    <% if @property.reviews_count > 0 %>
      <div class="flex items-center bg-gradient-to-r from-amber-50 to-yellow-50 px-6 py-3 rounded-2xl border-2 border-amber-200 shadow-lg">
        <div class="flex items-center mr-4">
          <% (1..5).each do |i| %>
            <svg class="h-6 w-6 <%= i <= @property.average_rating ? 'text-yellow-400' : 'text-slate-300' %>" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
          <% end %>
        </div>
        <span class="text-amber-800 font-bold"><%= @property.average_rating %> (<%= pluralize(@property.reviews_count, 'review') %>)</span>
      </div>
    <% end %>
  </div>
</div>