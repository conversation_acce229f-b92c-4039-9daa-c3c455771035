<!-- Property Actions Modal -->
<div data-properties-target="modal" class="fixed inset-0 bg-gray-500 bg-opacity-75 z-50 hidden">
  <div class="flex items-center justify-center min-h-screen p-4">
    <div class="bg-white rounded-lg max-w-sm w-full">
      
      <!-- Header -->
      <div class="p-4 border-b border-gray-200">
        <div class="flex justify-between items-center">
          <div>
            <h3 class="text-lg font-medium text-gray-900" data-properties-target="modalTitle">Property Actions</h3>
            <p class="text-sm text-gray-500" data-properties-target="modalSubtitle">Choose an action</p>
          </div>
          <button type="button" data-action="click->properties#closeModal" class="text-gray-400 hover:text-gray-600 text-2xl">
            ×
          </button>
        </div>
      </div>

      <!-- Actions -->
      <div class="p-4">
        <div class="space-y-2">
          
          <!-- View Property -->
          <button type="button"
                  data-action="click->properties#viewProperty"
                  class="w-full text-left px-4 py-3 rounded-md border border-gray-300 hover:bg-gray-50 flex items-center transition-colors duration-200">
            <svg class="w-5 h-5 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
            </svg>
            <span class="text-sm font-medium">View Property</span>
          </button>

          <!-- Edit Property -->
          <button type="button"
                  data-action="click->properties#editProperty"
                  class="w-full text-left px-4 py-3 rounded-md border border-gray-300 hover:bg-gray-50 flex items-center transition-colors duration-200">
            <svg class="w-5 h-5 text-green-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
            </svg>
            <span class="text-sm font-medium">Edit Property</span>
          </button>

          <!-- Duplicate Property -->
          <button type="button"
                  data-action="click->properties#duplicateProperty"
                  class="w-full text-left px-4 py-3 rounded-md border border-gray-300 hover:bg-gray-50 flex items-center transition-colors duration-200">
            <svg class="w-5 h-5 text-purple-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
            </svg>
            <span class="text-sm font-medium">Duplicate Property</span>
          </button>

          <!-- Delete Property -->
          <button type="button"
                  data-action="click->properties#deleteProperty"
                  class="w-full text-left px-4 py-3 rounded-md border border-red-300 hover:bg-red-50 flex items-center transition-colors duration-200">
            <svg class="w-5 h-5 text-red-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
            </svg>
            <span class="text-sm font-medium text-red-600">Delete Property</span>
          </button>

        </div>
      </div>

    </div>
  </div>
</div>
