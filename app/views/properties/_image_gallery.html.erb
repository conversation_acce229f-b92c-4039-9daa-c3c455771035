<!-- Property Image Gallery -->
<div class="relative bg-black rounded-2xl overflow-hidden mb-8" data-controller="gallery">
  <!-- Main Image -->
  <div class="aspect-[16/10] relative">
    <% if @property.photos.attached? && @property.photos.any? %>
      <% @property.photos.each_with_index do |image, index| %>
        <div class="<%= index == 0 ? 'block' : 'hidden' %> w-full h-full" data-gallery-target="slide" data-slide-index="<%= index %>">
          <%= image_tag image, 
                       class: "w-full h-full object-cover", 
                       alt: "#{@property.title} - Image #{index + 1}" %>
        </div>
      <% end %>
    <% else %>
      <div class="w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
        <div class="text-center text-gray-500">
          <svg class="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
          </svg>
          <p class="text-lg font-medium">No images available</p>
        </div>
      </div>
    <% end %>
    
    <!-- Navigation Arrows -->
    <% if @property.photos.attached? && @property.photos.any? && @property.photos.count > 1 %>
      <button class="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-all"
              data-action="click->gallery#previousSlide">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
      </button>
      
      <button class="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-all"
              data-action="click->gallery#nextSlide">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
        </svg>
      </button>
    <% end %>
    
    <!-- Image Counter -->
    <% if @property.photos.attached? && @property.photos.any? && @property.photos.count > 1 %>
      <div class="absolute bottom-4 right-4 bg-black bg-opacity-50 text-white px-3 py-1 rounded-full text-sm">
        <span data-gallery-target="counter">1</span> / <%= @property.photos.count %>
      </div>
    <% end %>
  </div>
  
  <!-- Thumbnail Navigation -->
  <% if @property.photos.attached? && @property.photos.any? && @property.photos.count > 1 %>
    <div class="mt-6">
      <div class="flex space-x-3 overflow-x-auto pb-2">
        <% @property.photos.each_with_index do |image, index| %>
          <button class="flex-shrink-0 w-20 h-16 rounded-lg overflow-hidden border-2 transition-all duration-200 <%= index == 0 ? 'border-blue-500' : 'border-transparent hover:border-blue-300' %>"
                  data-gallery-target="thumbnail"
                  data-action="click->gallery#goToSlide"
                  data-slide-index="<%= index %>">
            <%= image_tag image,
                         class: "w-full h-full object-cover thumbnail-clear",
                         style: "filter: none !important; opacity: 1 !important; backdrop-filter: none !important;",
                         alt: "#{@property.title} - Thumbnail #{index + 1}" %>
          </button>
        <% end %>
      </div>
    </div>
  <% end %>
</div>