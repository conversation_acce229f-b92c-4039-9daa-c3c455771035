<%= form_with model: property, local: true, multipart: true, class: "space-y-8", data: { controller: "property-form" } do |form| %>
  <!-- Enhanced <PERSON><PERSON><PERSON> -->
  <% if property.errors.any? %>
    <div class="bg-gradient-to-r from-red-50 to-pink-50 border-2 border-red-200 rounded-2xl p-6 shadow-lg">
      <div class="flex items-start">
        <div class="flex-shrink-0">
          <div class="w-10 h-10 bg-gradient-to-r from-red-500 to-pink-600 rounded-xl flex items-center justify-center">
            <svg class="h-6 w-6 text-white" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
          </div>
        </div>
        <div class="ml-4">
          <h3 class="text-lg font-bold text-red-900 mb-2">
            <%= pluralize(property.errors.count, "error") %> prevented your property from being saved
          </h3>
          <div class="space-y-1">
            <% property.errors.full_messages.each do |message| %>
              <div class="flex items-center text-sm text-red-800">
                <svg class="w-4 h-4 mr-2 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                <%= message %>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <!-- Section 1: Property Photos -->
  <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-8" data-section="photos">
    <div class="flex items-center mb-6">
      <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg shadow-purple-500/25">
        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
        </svg>
      </div>
      <div>
        <h3 class="text-2xl font-bold text-gray-900">Property Photos</h3>
        <p class="text-gray-600">Upload high-quality images to attract potential tenants</p>
      </div>
    </div>
    
    <div class="space-y-6">
      <!-- Enhanced File Upload -->
      <div class="relative">
        <%= form.label :photos, class: "block text-sm font-semibold text-gray-700 mb-3" do %>
          <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
          </svg>
          Upload Photos
        <% end %>
        
        <div class="relative group">
          <div class="border-3 border-dashed border-gray-300 rounded-2xl p-8 text-center hover:border-blue-400 transition-all duration-300 bg-gradient-to-br from-gray-50 to-gray-100 group-hover:from-blue-50 group-hover:to-indigo-50">
            <%= form.file_field :photos, 
                                multiple: true, 
                                accept: "image/*",
                                class: "absolute inset-0 w-full h-full opacity-0 cursor-pointer",
                                data: { 
                                  controller: "image-preview", 
                                  action: "change->image-preview#preview",
                                  "property-form-target": "photos"
                                } %>
            <div class="space-y-4">
              <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto shadow-lg">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
              </div>
              <div>
                <p class="text-lg font-semibold text-gray-900">Drop photos here or click to browse</p>
                <p class="text-sm text-gray-500 mt-1">PNG, JPG, GIF up to 10MB each. You can select multiple images.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Enhanced Image Preview -->
      <div data-image-preview-target="container" class="hidden">
        <div class="flex items-center justify-between mb-4">
          <h4 class="text-lg font-semibold text-gray-900">Photo Preview</h4>
          <span class="text-sm text-gray-500">Drag to reorder</span>
        </div>
        <div data-image-preview-target="grid" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"></div>
      </div>
      
      <!-- Existing Images (for edit) -->
      <% if property.persisted? && property.photos.attached? %>
        <div>
          <h4 class="text-lg font-semibold text-gray-900 mb-4">Current Photos</h4>
          <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            <% property.photos.each do |photo| %>
              <div class="relative group">
                <div class="relative overflow-hidden rounded-2xl shadow-lg">
                  <%= image_tag photo, class: "w-full h-32 object-cover group-hover:scale-105 transition-transform duration-300" %>
                  <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
                <%= link_to remove_photo_property_path(property, photo_id: photo.id), 
                            method: :delete,
                            data: { confirm: "Are you sure you want to remove this photo?" },
                            class: "absolute top-2 right-2 w-8 h-8 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300 transform hover:scale-110 shadow-lg" do %>
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                  </svg>
                <% end %>
              </div>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
  </div>

  <!-- Section 2: Basic Information -->
  <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-8" data-section="basic">
    <div class="flex items-center mb-6">
      <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg shadow-blue-500/25">
        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
      </div>
      <div>
        <h3 class="text-2xl font-bold text-gray-900">Basic Information</h3>
        <p class="text-gray-600">Essential details about your property</p>
      </div>
    </div>
    
    <div class="space-y-6">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div class="space-y-2">
          <%= form.label :title, class: "block text-sm font-semibold text-gray-700" do %>
            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
            </svg>
            Property Title
          <% end %>
          <%= form.text_field :title, 
                              class: "w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 font-medium placeholder-gray-400 hover:border-gray-300",
                              placeholder: "e.g., Beautiful 2BR Apartment in Downtown",
                              data: { "property-form-target": "title" } %>
        </div>
        
        <div class="space-y-2">
          <%= form.label :property_type, class: "block text-sm font-semibold text-gray-700" do %>
            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
            </svg>
            Property Type
          <% end %>
          <%= form.select :property_type, 
                          options_for_select(Property.property_types.map { |key, value| [key.humanize, key] }, property.property_type),
                          { prompt: "Select property type" },
                          { class: "w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 font-medium hover:border-gray-300",
                            data: { "property-form-target": "propertyType" } } %>
        </div>
      </div>
      
      <div class="space-y-2">
        <%= form.label :description, class: "block text-sm font-semibold text-gray-700" do %>
          <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7"></path>
          </svg>
          Description
        <% end %>
        <%= form.text_area :description, 
                           rows: 5,
                           class: "w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 font-medium placeholder-gray-400 hover:border-gray-300 resize-none",
                           placeholder: "Describe your property, amenities, and what makes it special. Mention nearby attractions, transportation, and unique features...",
                           data: { "property-form-target": "description" } %>
        <div class="flex justify-between text-xs text-gray-500 mt-2">
          <span>Be descriptive and highlight key features</span>
          <span id="description-count">0 characters</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Section 3: Location -->
  <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-8" data-section="location">
    <div class="flex items-center mb-6">
      <div class="w-12 h-12 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg shadow-emerald-500/25">
        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
        </svg>
      </div>
      <div>
        <h3 class="text-2xl font-bold text-gray-900">Location</h3>
        <p class="text-gray-600">Where is your property located?</p>
      </div>
    </div>
    
    <div class="space-y-6">
      <div class="space-y-2">
        <%= form.label :address, class: "block text-sm font-semibold text-gray-700" do %>
          <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
          </svg>
          Street Address
        <% end %>
        <%= form.text_field :address, 
                            class: "w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-2xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all duration-300 font-medium placeholder-gray-400 hover:border-gray-300",
                            placeholder: "123 Main Street",
                            data: { "property-form-target": "address" } %>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="space-y-2">
          <%= form.label :city, class: "block text-sm font-semibold text-gray-700" do %>
            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16l3-2 3 2 3-2 3 2z"></path>
            </svg>
            City
          <% end %>
          <%= form.text_field :city, 
                              class: "w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-2xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all duration-300 font-medium placeholder-gray-400 hover:border-gray-300",
                              placeholder: "City",
                              data: { "property-form-target": "city" } %>
        </div>
        
        <div class="space-y-2">
          <%= form.label :state, class: "block text-sm font-semibold text-gray-700" do %>
            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7"></path>
            </svg>
            State
          <% end %>
          <%= form.text_field :state, 
                              class: "w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-2xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all duration-300 font-medium placeholder-gray-400 hover:border-gray-300",
                              placeholder: "State",
                              data: { "property-form-target": "state" } %>
        </div>
        
        <div class="space-y-2">
          <%= form.label :zip_code, class: "block text-sm font-semibold text-gray-700" do %>
            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 8h10m-5-8v8m-5 4h10a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
            </svg>
            ZIP Code
          <% end %>
          <%= form.text_field :zip_code, 
                              class: "w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-2xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-all duration-300 font-medium placeholder-gray-400 hover:border-gray-300",
                              placeholder: "12345",
                              data: { "property-form-target": "zipCode" } %>
        </div>
      </div>
    </div>
  </div>

  <!-- Section 4: Property Details -->
  <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-8" data-section="details">
    <div class="flex items-center mb-6">
      <div class="w-12 h-12 bg-gradient-to-r from-amber-500 to-orange-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg shadow-amber-500/25">
        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
        </svg>
      </div>
      <div>
        <h3 class="text-2xl font-bold text-gray-900">Property Details</h3>
        <p class="text-gray-600">Specifications and rental information</p>
      </div>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div class="space-y-2">
        <%= form.label :bedrooms, class: "block text-sm font-semibold text-gray-700" do %>
          <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
          </svg>
          Bedrooms
        <% end %>
        <%= form.number_field :bedrooms, 
                              min: 0,
                              class: "w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-2xl focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-all duration-300 font-semibold text-center text-lg hover:border-gray-300",
                              data: { "property-form-target": "bedrooms" } %>
      </div>
      
      <div class="space-y-2">
        <%= form.label :bathrooms, class: "block text-sm font-semibold text-gray-700" do %>
          <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10h16v11H4V10z"></path>
          </svg>
          Bathrooms
        <% end %>
        <%= form.number_field :bathrooms, 
                              min: 0.5,
                              step: 0.5,
                              class: "w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-2xl focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-all duration-300 font-semibold text-center text-lg hover:border-gray-300",
                              data: { "property-form-target": "bathrooms" } %>
      </div>
      
      <div class="space-y-2">
        <%= form.label :square_feet, "Square Feet", class: "block text-sm font-semibold text-gray-700" do %>
          <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"></path>
          </svg>
          Square Feet
        <% end %>
        <%= form.number_field :square_feet, 
                              min: 1,
                              class: "w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-2xl focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-all duration-300 font-semibold text-center text-lg hover:border-gray-300",
                              placeholder: "1200",
                              data: { "property-form-target": "squareFeet" } %>
      </div>
      
      <div class="space-y-2">
        <%= form.label :price, "Monthly Rent", class: "block text-sm font-semibold text-gray-700" do %>
          <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
          </svg>
          Monthly Rent ($)
        <% end %>
        <div class="relative">
          <span class="absolute left-4 top-1/2 transform -translate-y-1/2 text-lg font-bold text-gray-500">$</span>
          <%= form.number_field :price, 
                                min: 1,
                                class: "w-full pl-8 pr-4 py-3 bg-white border-2 border-gray-200 rounded-2xl focus:ring-2 focus:ring-amber-500 focus:border-amber-500 transition-all duration-300 font-semibold text-center text-lg hover:border-gray-300",
                                placeholder: "2500",
                                data: { "property-form-target": "price" } %>
        </div>
      </div>
    </div>
  </div>

  <!-- Section 5: Availability -->
  <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-8" data-section="availability">
    <div class="flex items-center mb-6">
      <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mr-4 shadow-lg shadow-green-500/25">
        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a4 4 0 118 0v4m-4 6v6m-4-6h8m-8 0a2 2 0 00-2 2v6a2 2 0 002 2h8a2 2 0 002-2v-6a2 2 0 00-2-2z"></path>
        </svg>
      </div>
      <div>
        <h3 class="text-2xl font-bold text-gray-900">Availability</h3>
        <p class="text-gray-600">When is your property available for rent?</p>
      </div>
    </div>
    
    <div class="space-y-2">
      <%= form.label :availability_status, class: "block text-sm font-semibold text-gray-700" do %>
        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        Availability Status
      <% end %>
      <%= form.select :availability_status, 
                      options_for_select(Property.availability_statuses.map { |key, value| [key.humanize, key] }, property.availability_status),
                      {},
                      { class: "w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-2xl focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-300 font-medium hover:border-gray-300",
                        data: { "property-form-target": "availability" } } %>
    </div>
  </div>

  <!-- Enhanced Form Actions -->
  <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-8">
    <div class="flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0 sm:space-x-6">
      <div class="flex items-center space-x-4">
        <%= link_to properties_path, 
                    class: "group inline-flex items-center px-6 py-3 border-2 border-gray-200 text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-300 rounded-2xl font-semibold transition-all duration-300 transform hover:-translate-y-0.5" do %>
          <svg class="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
          Cancel
        <% end %>
        
        <button type="button" class="group inline-flex items-center px-6 py-3 bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 hover:from-gray-200 hover:to-gray-300 rounded-2xl font-semibold transition-all duration-300 transform hover:-translate-y-0.5">
          <svg class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"></path>
          </svg>
          Save as Draft
        </button>
      </div>
      
      <div class="flex items-center space-x-4">
        <button type="button" class="group inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-500 to-indigo-600 text-white hover:from-blue-600 hover:to-indigo-700 rounded-2xl font-semibold transition-all duration-300 transform hover:-translate-y-0.5 shadow-lg shadow-blue-500/25 hover:shadow-xl hover:shadow-blue-500/30">
          <svg class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
          </svg>
          Preview Listing
        </button>
        
        <%= form.submit "Publish Property", 
                        class: "group inline-flex items-center px-8 py-3 bg-gradient-to-r from-emerald-600 to-teal-600 text-white hover:from-emerald-700 hover:to-teal-700 rounded-2xl font-bold text-lg transition-all duration-300 transform hover:-translate-y-0.5 shadow-lg shadow-emerald-500/25 hover:shadow-xl hover:shadow-emerald-500/30" %>
      </div>
    </div>
  </div>
<% end %>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Character counter for description
    const descriptionField = document.querySelector('textarea[name="property[description]"]');
    const charCounter = document.getElementById('description-count');
    
    if (descriptionField && charCounter) {
      const updateCharCount = () => {
        const count = descriptionField.value.length;
        charCounter.textContent = `${count} characters`;
        
        if (count > 500) {
          charCounter.classList.add('text-red-500');
          charCounter.classList.remove('text-gray-500');
        } else {
          charCounter.classList.add('text-gray-500');
          charCounter.classList.remove('text-red-500');
        }
      };
      
      descriptionField.addEventListener('input', updateCharCount);
      updateCharCount(); // Initial count
    }

    // Form validation visual feedback
    const requiredFields = document.querySelectorAll('input[required], select[required], textarea[required]');
    requiredFields.forEach(field => {
      field.addEventListener('blur', function() {
        if (this.value.trim() === '') {
          this.classList.add('border-red-300', 'focus:border-red-500', 'focus:ring-red-500');
          this.classList.remove('border-gray-200', 'focus:border-blue-500', 'focus:ring-blue-500');
        } else {
          this.classList.remove('border-red-300', 'focus:border-red-500', 'focus:ring-red-500');
          this.classList.add('border-green-300', 'focus:border-green-500', 'focus:ring-green-500');
        }
      });
    });

    // Smooth scrolling for form sections
    function scrollToSection(sectionName) {
      const section = document.querySelector(`[data-section="${sectionName}"]`);
      if (section) {
        section.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }

    // Auto-save functionality
    let autoSaveTimeout;
    const formFields = document.querySelectorAll('input, select, textarea');
    formFields.forEach(field => {
      field.addEventListener('input', function() {
        clearTimeout(autoSaveTimeout);
        autoSaveTimeout = setTimeout(() => {
          // Show auto-save indicator
          console.log('Auto-saving property data...');
        }, 3000);
      });
    });
  });
</script>
