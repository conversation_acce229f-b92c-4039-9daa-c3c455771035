<% content_for :title, "#{@property.title} - Ofie" %>
<% content_for :meta_description, truncate(@property.description, length: 160) %>

<div class="min-h-screen bg-gradient-to-br from-neutral-50 via-slate-50 to-stone-50 relative overflow-hidden">
  <!-- Advanced Background Layers -->
  <div class="absolute inset-0 bg-gradient-to-br from-indigo-50/30 via-transparent to-purple-50/20"></div>
  <div class="absolute inset-0 bg-grid-neutral-100/40 [mask-image:radial-gradient(ellipse_at_center,transparent_20%,black)] pointer-events-none"></div>
  
  <!-- Dynamic Floating Elements -->
  <div class="absolute top-1/4 -right-20 w-80 h-80 bg-gradient-to-br from-indigo-200/15 via-purple-200/10 to-pink-200/15 rounded-full blur-3xl animate-float-slow"></div>
  <div class="absolute -bottom-20 -left-20 w-96 h-96 bg-gradient-to-tr from-cyan-200/15 via-blue-200/10 to-indigo-200/15 rounded-full blur-3xl animate-float-reverse"></div>
  <div class="absolute top-1/2 left-1/2 w-64 h-64 bg-gradient-to-r from-emerald-200/10 to-teal-200/10 rounded-full blur-2xl animate-pulse-slow"></div>

  <div class="relative z-10">
    <!-- Enhanced Hero Section -->
    <div class="relative">
      <%= render 'hero_gallery' %>
      
      <!-- Hero Overlay Gradient -->
      <div class="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-white/80 via-white/40 to-transparent backdrop-blur-sm"></div>
    </div>

    <!-- Main Content Container -->
    <div class="max-w-8xl mx-auto px-4 sm:px-6 lg:px-8 xl:px-12">
      <!-- Property Title Bar -->
      <div class="relative -mt-16 z-20 mb-12">
        <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl shadow-neutral-900/5 border border-white/60 p-8 mx-4 sm:mx-6 lg:mx-0">
          <%= render 'property_header' %>
        </div>
      </div>

      <!-- Main Content Grid -->
      <div class="grid grid-cols-1 xl:grid-cols-12 gap-8 lg:gap-12 pb-20">
        <!-- Primary Content Area -->
        <div class="xl:col-span-8 space-y-10">
          <!-- Property Description Card -->
          <div class="bg-white/70 backdrop-blur-xl rounded-3xl shadow-xl shadow-neutral-900/5 border border-white/60 p-8 transition-all duration-500 hover:shadow-2xl hover:shadow-neutral-900/10">
            <%= render 'property_description' %>
          </div>

          <!-- Amenities Showcase -->
          <div class="bg-white/70 backdrop-blur-xl rounded-3xl shadow-xl shadow-neutral-900/5 border border-white/60 p-8 transition-all duration-500 hover:shadow-2xl hover:shadow-neutral-900/10">
            <%= render 'amenities_section' %>
          </div>

          <!-- Interactive Map -->
          <div class="bg-white/70 backdrop-blur-xl rounded-3xl shadow-xl shadow-neutral-900/5 border border-white/60 overflow-hidden transition-all duration-500 hover:shadow-2xl hover:shadow-neutral-900/10">
            <%= render 'map' %>
          </div>
          
          <!-- Reviews Carousel -->
          <div class="bg-white/70 backdrop-blur-xl rounded-3xl shadow-xl shadow-neutral-900/5 border border-white/60 p-8 transition-all duration-500 hover:shadow-2xl hover:shadow-neutral-900/10">
            <%= render 'reviews_section' %>
          </div>

          <!-- Enhanced Comments Section -->
          <div class="bg-white/70 backdrop-blur-xl rounded-3xl shadow-xl shadow-neutral-900/5 border border-white/60 transition-all duration-500 hover:shadow-2xl hover:shadow-neutral-900/10">
            <%= render 'property_comments/comments_section' %>
          </div>
        </div>
        
        <!-- Enhanced Sidebar -->
        <div class="xl:col-span-4 space-y-8">
          <!-- Sticky Sidebar Container -->
          <div class="sticky top-8 space-y-8">
            <!-- Premium Booking Card -->
            <div class="bg-gradient-to-br from-white/90 to-white/70 backdrop-blur-2xl rounded-3xl shadow-2xl shadow-neutral-900/10 border border-white/60 overflow-hidden transition-all duration-500 hover:shadow-3xl hover:shadow-neutral-900/15 hover:scale-[1.02]">
              <%= render 'booking_card' %>
            </div>
            
            <!-- Host Profile Card -->
            <div class="bg-white/70 backdrop-blur-xl rounded-3xl shadow-xl shadow-neutral-900/5 border border-white/60 p-6 transition-all duration-500 hover:shadow-2xl hover:shadow-neutral-900/10">
              <%= render 'host_information' %>
            </div>
            
            <!-- House Rules Card -->
            <div class="bg-white/70 backdrop-blur-xl rounded-3xl shadow-xl shadow-neutral-900/5 border border-white/60 p-6 transition-all duration-500 hover:shadow-2xl hover:shadow-neutral-900/10">
              <%= render 'house_rules' %>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Related Properties Showcase -->
      <div class="bg-gradient-to-r from-white/80 via-white/60 to-white/80 backdrop-blur-xl rounded-3xl shadow-2xl shadow-neutral-900/5 border border-white/60 p-10 mb-16 transition-all duration-500 hover:shadow-3xl hover:shadow-neutral-900/10">
        <%= render 'related_properties' %>
      </div>
    </div>
  </div>
</div>

<!-- Enhanced Scheduling Modal -->
<%= render 'scheduling_modal' %>

<!-- Advanced Custom Styles -->
<style>
  /* Enhanced Grid Background */
  .bg-grid-neutral-100\/40 {
    background-image: url("data:image/svg+xml,%3csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3e%3cg fill='none' fill-rule='evenodd'%3e%3cg fill='%23f5f5f5' fill-opacity='0.3'%3e%3cpath d='M30 30h30v30H30V30zm-30 0h30v30H0V30z'/%3e%3c/g%3e%3c/g%3e%3c/svg%3e");
  }

  /* Advanced Animations */
  @keyframes float-slow {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(5deg); }
  }

  @keyframes float-reverse {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(15px) rotate(-3deg); }
  }

  @keyframes pulse-slow {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 0.5; transform: scale(1.05); }
  }

  @keyframes slideInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fadeInScale {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  .animate-float-slow {
    animation: float-slow 8s ease-in-out infinite;
  }

  .animate-float-reverse {
    animation: float-reverse 10s ease-in-out infinite;
  }

  .animate-pulse-slow {
    animation: pulse-slow 6s ease-in-out infinite;
  }

  .animate-slide-up {
    animation: slideInUp 0.8s ease-out;
  }

  .animate-fade-in-scale {
    animation: fadeInScale 0.6s ease-out;
  }

  /* Enhanced Glass Morphism */
  .backdrop-blur-xl {
    backdrop-filter: blur(24px);
    -webkit-backdrop-filter: blur(24px);
  }

  .backdrop-blur-2xl {
    backdrop-filter: blur(40px);
    -webkit-backdrop-filter: blur(40px);
  }

  /* Enhanced Shadows */
  .shadow-3xl {
    box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
  }

  /* Text Enhancement */
  .text-balance {
    text-wrap: balance;
  }

  /* Enhanced Hover Effects */
  .hover-lift {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .hover-lift:hover {
    transform: translateY(-4px);
  }

  /* Smooth Scroll Behavior */
  html {
    scroll-behavior: smooth;
  }

  /* Enhanced Focus States */
  *:focus {
    outline: 2px solid rgba(99, 102, 241, 0.5);
    outline-offset: 2px;
  }

  /* Progressive Enhancement for Reduced Motion */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
    
    .animate-float-slow,
    .animate-float-reverse,
    .animate-pulse-slow {
      animation: none;
    }
  }

  /* Enhanced Typography */
  .font-display {
    font-feature-settings: 'cv03', 'cv04', 'cv09';
  }

  /* Dark Mode Considerations */
  @media (prefers-color-scheme: dark) {
    .bg-grid-neutral-100\/40 {
      background-image: url("data:image/svg+xml,%3csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3e%3cg fill='none' fill-rule='evenodd'%3e%3cg fill='%23374151' fill-opacity='0.2'%3e%3cpath d='M30 30h30v30H30V30zm-30 0h30v30H0V30z'/%3e%3c/g%3e%3c/g%3e%3c/svg%3e");
    }
  }

  /* Performance Optimizations */
  .will-change-transform {
    will-change: transform;
  }

  .will-change-auto {
    will-change: auto;
  }

  /* Container Queries for Enhanced Responsiveness */
  @container (min-width: 768px) {
    .container-responsive {
      padding: 2rem;
    }
  }
</style>
