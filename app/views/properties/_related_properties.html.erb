<!-- Enhanced Related Properties Section -->
<div class="mt-16">
  <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-2xl shadow-gray-200/50 border border-white/20 p-8 hover:shadow-3xl transition-all duration-300">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-12">
      <div class="flex items-center space-x-4 mb-4 sm:mb-0">
        <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-xl">
          <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
          </svg>
        </div>
        <div>
          <h2 class="text-3xl font-bold text-slate-800">More Places to Stay</h2>
          <p class="text-slate-600 text-lg">Discover other amazing properties in the area</p>
        </div>
      </div>
      <%= link_to properties_path, 
                  class: "group inline-flex items-center px-8 py-4 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white rounded-2xl font-bold transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-1" do %>
        View All Properties
        <svg class="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
        </svg>
      <% end %>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
      <% Property.where.not(id: @property.id).available.limit(4).each do |property| %>
        <div class="group bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 overflow-hidden hover:shadow-2xl hover:-translate-y-2 transition-all duration-300">
          <div class="aspect-[4/3] relative overflow-hidden">
            <% if property.photos.attached? && property.photos.any? %>
              <%= image_tag property.photos.first,
                            class: "w-full h-full object-cover group-hover:scale-110 transition-transform duration-500",
                            alt: property.title %>
            <% else %>
              <div class="w-full h-full bg-gradient-to-br from-slate-100 to-slate-200 flex items-center justify-center">
                <div class="w-16 h-16 bg-gradient-to-br from-slate-300 to-slate-400 rounded-3xl flex items-center justify-center shadow-xl">
                  <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                  </svg>
                </div>
              </div>
            <% end %>

            <!-- Rating Badge -->
            <% if property.reviews_count > 0 %>
              <div class="absolute top-4 left-4 bg-white/90 backdrop-blur-sm px-4 py-2 rounded-2xl flex items-center shadow-lg border border-white/20">
                <svg class="h-4 w-4 text-yellow-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
                <span class="text-sm font-bold text-slate-900"><%= property.average_rating %></span>
              </div>
            <% end %>

            <!-- Availability Badge -->
            <div class="absolute top-4 right-4 bg-gradient-to-r from-emerald-500 to-green-600 text-white px-4 py-2 rounded-2xl text-xs font-bold shadow-xl">
              Available
            </div>

            <!-- Favorite Button -->
            <div class="absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <button class="w-10 h-10 bg-white/90 backdrop-blur-sm hover:bg-white text-slate-600 hover:text-rose-500 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 flex items-center justify-center border border-white/20">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                </svg>
              </button>
            </div>
          </div>

          <div class="p-6">
            <h3 class="font-bold text-slate-900 mb-3 line-clamp-2 text-lg group-hover:text-indigo-600 transition-colors duration-200">
              <%= link_to property.title, property_path(property), class: "hover:text-indigo-600 transition-colors duration-200" %>
            </h3>
            
            <div class="flex items-center text-slate-600 mb-4">
              <div class="w-5 h-5 bg-gradient-to-br from-slate-400 to-slate-500 rounded-lg flex items-center justify-center mr-2">
                <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
              </div>
              <span class="text-sm font-semibold"><%= property.city %></span>
            </div>

            <!-- Property Stats -->
            <div class="flex items-center justify-between mb-6">
              <div class="flex items-center space-x-4 text-sm text-slate-600">
                <div class="flex items-center bg-slate-100 px-3 py-1 rounded-xl">
                  <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                  </svg>
                  <span class="font-bold"><%= property.bedrooms %></span>
                </div>
                <div class="flex items-center bg-slate-100 px-3 py-1 rounded-xl">
                  <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10v11M20 10v11"></path>
                  </svg>
                  <span class="font-bold"><%= property.bathrooms %></span>
                </div>
              </div>
            </div>

            <!-- Price and CTA -->
            <div class="flex items-center justify-between">
              <div>
                <div class="text-2xl font-bold text-slate-900">$<%= number_with_delimiter(property.price) %></div>
                <div class="text-sm text-slate-500 font-semibold">per month</div>
              </div>
              <%= link_to property_path(property), 
                          class: "group inline-flex items-center px-4 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white rounded-xl font-bold transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" do %>
                View
                <svg class="w-4 h-4 ml-1 group-hover:translate-x-0.5 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              <% end %>
            </div>
          </div>
        </div>
      <% end %>
    </div>

    <!-- No Properties Fallback -->
    <% if Property.where.not(id: @property.id).available.count == 0 %>
      <div class="text-center py-16">
        <div class="w-24 h-24 bg-gradient-to-br from-slate-100 to-slate-200 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-xl">
          <svg class="w-12 h-12 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
          </svg>
        </div>
        <h3 class="text-2xl font-bold text-slate-800 mb-4">No Other Properties Available</h3>
        <p class="text-slate-600 mb-8 max-w-md mx-auto">Check back soon for more amazing places to stay in this area.</p>
        <%= link_to properties_path, 
                    class: "inline-flex items-center px-8 py-4 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white rounded-2xl font-bold transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-1" do %>
          Browse All Properties
          <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
          </svg>
        <% end %>
      </div>
    <% end %>
  </div>
</div>