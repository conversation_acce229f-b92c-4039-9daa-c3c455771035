<!-- Clean Hero Image Gallery -->
<div class="bg-white shadow-lg border-b border-gray-100" data-controller="gallery">
  <!-- Main Image Container - Responsive Aspect Ratios -->
  <div class="aspect-[4/3] sm:aspect-[16/9] lg:aspect-[21/9] relative overflow-hidden">
    <% if @property.photos.attached? && @property.photos.any? %>
      <% @property.photos.each_with_index do |image, index| %>
        <div class="<%= index == 0 ? 'block' : 'hidden' %> w-full h-full" data-gallery-target="slide" data-slide-index="<%= index %>">
          <%= image_tag image,
                       class: "w-full h-full object-cover transition-transform duration-700 hover:scale-105",
                       alt: "#{@property.title} - Image #{index + 1}" %>
        </div>
      <% end %>
    <% else %>
      <div class="w-full h-full bg-gradient-to-br from-slate-100 to-slate-200 flex items-center justify-center">
        <div class="text-center text-slate-400">
          <div class="w-16 h-16 sm:w-24 sm:h-24 bg-gradient-to-br from-slate-300 to-slate-400 rounded-3xl flex items-center justify-center mx-auto mb-4 sm:mb-6 shadow-xl">
            <svg class="w-8 h-8 sm:w-12 sm:h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
          </div>
          <p class="text-xl sm:text-2xl font-bold">No images available</p>
          <p class="text-slate-500 mt-2 text-sm sm:text-base">Property photos will appear here</p>
        </div>
      </div>
    <% end %>
    
    <!-- Simplified Mobile Action Buttons -->
    <div class="absolute top-4 right-4 sm:top-6 sm:right-6 flex space-x-2 sm:space-x-3 z-10">
      <% if user_signed_in? %>
        <button class="group w-10 h-10 sm:w-14 sm:h-14 bg-white/95 hover:bg-white text-slate-600 hover:text-rose-500 rounded-xl sm:rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 flex items-center justify-center border border-white/50">
          <svg class="w-4 h-4 sm:w-6 sm:h-6 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
          </svg>
        </button>
      <% end %>

      <button class="group w-10 h-10 sm:w-14 sm:h-14 bg-white/95 hover:bg-white text-slate-600 hover:text-blue-500 rounded-xl sm:rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 flex items-center justify-center border border-white/50">
        <svg class="w-4 h-4 sm:w-6 sm:h-6 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
        </svg>
      </button>

      <!-- Hide download button on mobile to reduce clutter -->
      <button class="hidden sm:flex group w-14 h-14 bg-white/95 hover:bg-white text-slate-600 hover:text-emerald-500 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 items-center justify-center border border-white/50">
        <svg class="w-6 h-6 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
        </svg>
      </button>
    </div>

    <!-- Clean Navigation Arrows -->
    <% if @property.photos.attached? && @property.photos.any? && @property.photos.count > 1 %>
      <button class="absolute left-3 sm:left-6 top-1/2 -translate-y-1/2 w-10 h-10 sm:w-14 sm:h-14 bg-white/95 hover:bg-white text-slate-700 hover:text-indigo-600 rounded-xl sm:rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 flex items-center justify-center border border-white/50"
              data-action="click->gallery#previousSlide">
        <svg class="w-4 h-4 sm:w-6 sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
      </button>

      <button class="absolute right-3 sm:right-6 top-1/2 -translate-y-1/2 w-10 h-10 sm:w-14 sm:h-14 bg-white/95 hover:bg-white text-slate-700 hover:text-indigo-600 rounded-xl sm:rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 flex items-center justify-center border border-white/50"
              data-action="click->gallery#nextSlide">
        <svg class="w-4 h-4 sm:w-6 sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
        </svg>
      </button>
    <% end %>

    <!-- Clean Image Counter -->
    <% if @property.photos.attached? && @property.photos.any? && @property.photos.count > 1 %>
      <div class="absolute bottom-3 sm:bottom-6 right-3 sm:right-6 bg-white/95 text-slate-800 px-3 py-2 sm:px-6 sm:py-3 rounded-xl sm:rounded-2xl font-bold shadow-lg border border-white/50 text-sm sm:text-base">
        <span data-gallery-target="counter">1</span> / <%= @property.photos.count %>
      </div>
    <% end %>
  </div>
  
  <!-- Crystal Clear Thumbnail Navigation -->
  <% if @property.photos.attached? && @property.photos.any? && @property.photos.count > 1 %>
    <div class="py-4 sm:py-8 bg-white border-t border-gray-100">
      <div class="max-w-6xl mx-auto px-4 sm:px-6">
        <!-- Mobile: Show fewer thumbnails with horizontal scroll -->
        <div class="sm:hidden">
          <div class="flex space-x-3 overflow-x-auto pb-2 snap-x snap-mandatory">
            <% @property.photos.each_with_index do |image, index| %>
              <button class="flex-shrink-0 w-20 h-16 rounded-lg overflow-hidden border-2 transition-all duration-300 shadow-md hover:shadow-lg snap-start <%= index == 0 ? 'border-indigo-500 shadow-indigo-500/25' : 'border-gray-200 hover:border-indigo-300' %>"
                      data-gallery-target="thumbnail"
                      data-action="click->gallery#goToSlide"
                      data-slide-index="<%= index %>">
                <%= image_tag image,
                             class: "w-full h-full object-cover thumbnail-clear",
                             style: "filter: none !important; opacity: 1 !important; backdrop-filter: none !important;",
                             alt: "#{@property.title} - Thumbnail #{index + 1}" %>
              </button>
            <% end %>
          </div>
        </div>

        <!-- Desktop: Show all thumbnails centered -->
        <div class="hidden sm:flex justify-center space-x-4 overflow-x-auto pb-2">
          <% @property.photos.each_with_index do |image, index| %>
            <button class="flex-shrink-0 w-32 h-24 lg:w-40 lg:h-32 rounded-xl lg:rounded-2xl overflow-hidden border-3 lg:border-4 transition-all duration-300 shadow-lg hover:shadow-xl <%= index == 0 ? 'border-indigo-500 shadow-indigo-500/25' : 'border-gray-200 hover:border-indigo-300' %>"
                    data-gallery-target="thumbnail"
                    data-action="click->gallery#goToSlide"
                    data-slide-index="<%= index %>">
              <%= image_tag image,
                           class: "w-full h-full object-cover thumbnail-clear",
                           style: "filter: none !important; opacity: 1 !important; backdrop-filter: none !important;",
                           alt: "#{@property.title} - Thumbnail #{index + 1}" %>
            </button>
          <% end %>
        </div>
      </div>
    </div>
  <% end %>
</div>