<% content_for :title, seo_title %>
<% content_for :description, seo_description %>
<% content_for :keywords, seo_keywords %>

<!-- Hero Section with Sophisticated Design -->
<section class="relative min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-indigo-950 text-white overflow-hidden">
  <!-- Advanced Background Layers -->
  <div class="absolute inset-0">
    <!-- Primary gradient overlay -->
    <div class="absolute inset-0 bg-gradient-to-r from-slate-900/90 via-indigo-900/80 to-slate-900/90"></div>
    <!-- Animated mesh gradient -->
    <div class="absolute inset-0 opacity-40">
      <div class="absolute inset-0 bg-gradient-to-r from-blue-600/20 via-purple-600/30 to-pink-600/20 animate-pulse"></div>
      <div class="absolute inset-0" style="background: radial-gradient(circle at 20% 30%, rgba(59, 130, 246, 0.15) 0%, transparent 40%), radial-gradient(circle at 80% 70%, rgba(147, 51, 234, 0.15) 0%, transparent 40%), radial-gradient(circle at 40% 80%, rgba(236, 72, 153, 0.10) 0%, transparent 40%);"></div>
    </div>
    <!-- Subtle noise texture -->
    <div class="absolute inset-0 opacity-20" style="background-image: url('data:image/svg+xml,%3Csvg viewBox="0 0 256 256" xmlns="http://www.w3.org/2000/svg"%3E%3Cfilter id="noiseFilter"%3E%3CfeTurbulence type="fractalNoise" baseFrequency="0.9" numOctaves="1" stitchTiles="stitch"/%3E%3C/filter%3E%3Crect width="100%25" height="100%25" filter="url(%23noiseFilter)" opacity="0.1"/%3E%3C/svg%3E');"></div>
  </div>
  
  <!-- Floating Glass Elements -->
  <div class="absolute inset-0 overflow-hidden pointer-events-none">
    <div class="absolute top-1/4 left-1/6 w-72 h-72 bg-gradient-to-br from-blue-400/8 to-cyan-400/8 rounded-full blur-3xl animate-float"></div>
    <div class="absolute bottom-1/3 right-1/6 w-96 h-96 bg-gradient-to-br from-purple-400/8 to-pink-400/8 rounded-full blur-3xl animate-float-delayed"></div>
    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-br from-indigo-400/6 to-blue-400/6 rounded-full blur-3xl animate-pulse"></div>
  </div>
  
  <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 lg:py-24 flex items-center min-h-screen">
    <div class="w-full">
      <!-- Trust Badge -->
      <div class="text-center mb-8">
        <div class="inline-flex items-center space-x-3 bg-white/5 backdrop-blur-md rounded-full px-6 py-3 mb-8 border border-white/10">
          <div class="flex items-center space-x-2">
            <svg class="w-5 h-5 text-emerald-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            <span class="text-sm font-medium text-white/90">Trusted by <%= format_number_with_suffix(total_families_count) %>+ Families</span>
          </div>
          <div class="w-px h-4 bg-white/20"></div>
          <div class="flex items-center space-x-1">
            <div class="flex -space-x-1">
              <div class="w-6 h-6 bg-gradient-to-r from-emerald-400 to-cyan-400 rounded-full border border-white/20"></div>
              <div class="w-6 h-6 bg-gradient-to-r from-blue-400 to-indigo-400 rounded-full border border-white/20"></div>
              <div class="w-6 h-6 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full border border-white/20"></div>
            </div>
            <span class="text-xs text-white/70 ml-2"><%= platform_rating %>★</span>
          </div>
        </div>
      </div>
      
      <!-- Hero Title -->
      <div class="text-center mb-16">
        <h1 class="text-6xl md:text-8xl font-light mb-8 leading-[0.9] tracking-tight">
          <span class="block font-extralight text-white/95"><%= hero_title_line_1 %></span>
          <span class="block text-transparent bg-clip-text bg-gradient-to-r from-cyan-300 via-blue-300 to-indigo-300 font-medium animate-gradient">
            <%= hero_title_line_2 %>
          </span>
          <span class="block text-2xl md:text-3xl mt-6 text-white/70 font-light tracking-wide">
            <%= hero_subtitle %>
          </span>
        </h1>
        
        <p class="text-lg md:text-xl mb-12 text-white/70 max-w-3xl mx-auto leading-relaxed font-light">
          <%= hero_description %>
          <% hero_features.each_with_index do |feature, index| %>
            <span class="text-white/90"><%= feature %></span><%= index < hero_features.length - 1 ? ' • ' : '' %>
          <% end %>
        </p>
        
        <!-- Refined Social Proof -->
        <div class="flex justify-center items-center space-x-8 mb-16">
          <div class="flex items-center space-x-3">
            <div class="flex -space-x-3">
              <div class="w-10 h-10 bg-gradient-to-r from-emerald-400 to-cyan-400 rounded-full border-2 border-slate-800 shadow-lg"></div>
              <div class="w-10 h-10 bg-gradient-to-r from-blue-400 to-indigo-400 rounded-full border-2 border-slate-800 shadow-lg"></div>
              <div class="w-10 h-10 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full border-2 border-slate-800 shadow-lg"></div>
              <div class="w-10 h-10 bg-gradient-to-r from-orange-400 to-yellow-400 rounded-full border-2 border-slate-800 shadow-lg flex items-center justify-center">
                <span class="text-xs font-semibold text-slate-800"><%= format_number_with_suffix(monthly_joiners_count) %>+</span>
              </div>
            </div>
            <div class="text-left">
              <div class="text-sm font-medium text-white/90">Happy Homeowners</div>
              <div class="text-xs text-white/60">Joined this month</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Compact Search Form -->
      <div class="relative max-w-5xl mx-auto">
        <!-- Glass morphism container -->
        <div class="bg-white/[0.08] backdrop-blur-xl rounded-3xl p-6 lg:p-8 border border-white/10 shadow-2xl">
          <div class="text-center mb-6">
            <h2 class="text-xl lg:text-2xl font-light text-white mb-2">Begin Your Search</h2>
            <p class="text-sm text-white/60"><%= format_large_number(verified_properties_count) %>+ verified properties • Updated in real-time</p>
          </div>
          
          <%= form_with url: properties_path, method: :get, local: true, class: "space-y-6" do |form| %>
            <!-- Compact Search Fields -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              <!-- Property Type -->
              <div>
                <label class="block text-xs font-medium text-white/90 mb-2">Property Type</label>
                <%= form.select :property_type,
                    options_for_select(property_type_options, params[:property_type]),
                    {},
                    { class: "w-full px-4 py-3 rounded-xl bg-white/[0.08] border border-white/20 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-400/50 focus:border-blue-400/50 transition-all duration-300 hover:bg-white/[0.12] backdrop-blur-md text-sm" } %>
              </div>
              
              <!-- Location -->
              <div>
                <label class="block text-xs font-medium text-white/90 mb-2">Location</label>
                <%= form.text_field :city, 
                    placeholder: "City or ZIP", 
                    value: params[:city],
                    class: "w-full px-4 py-3 rounded-xl bg-white/[0.08] border border-white/20 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-400/50 focus:border-blue-400/50 transition-all duration-300 hover:bg-white/[0.12] backdrop-blur-md text-sm" %>
              </div>
            
              <!-- Bedrooms -->
              <div>
                <label class="block text-xs font-medium text-white/90 mb-2">Bedrooms</label>
                <%= form.select :bedrooms,
                    options_for_select(bedroom_options, params[:bedrooms]),
                    {},
                    { class: "w-full px-4 py-3 rounded-xl bg-white/[0.08] border border-white/20 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-400/50 focus:border-blue-400/50 transition-all duration-300 hover:bg-white/[0.12] backdrop-blur-md text-sm" } %>
              </div>
              
              <!-- Price Range -->
              <div>
                <label class="block text-xs font-medium text-white/90 mb-2">Min Price</label>
                <%= form.text_field :min_price,
                    placeholder: min_price_placeholder,
                    value: params[:min_price],
                    class: "w-full px-4 py-3 rounded-xl bg-white/[0.08] border border-white/20 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-400/50 focus:border-blue-400/50 transition-all duration-300 hover:bg-white/[0.12] backdrop-blur-md text-sm" %>
              </div>
              
              <div>
                <label class="block text-xs font-medium text-white/90 mb-2">Max Price</label>
                <%= form.text_field :max_price,
                    placeholder: max_price_placeholder,
                    value: params[:max_price],
                    class: "w-full px-4 py-3 rounded-xl bg-white/[0.08] border border-white/20 text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-blue-400/50 focus:border-blue-400/50 transition-all duration-300 hover:bg-white/[0.12] backdrop-blur-md text-sm" %>
              </div>
            </div>
            
            <!-- Search Button -->
            <div class="text-center">
              <%= form.submit "Search Properties", 
                  class: "bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500 hover:from-blue-600 hover:via-indigo-600 hover:to-purple-600 text-white font-medium py-3 px-10 rounded-xl transition-all duration-300 transform hover:scale-[1.02] focus:outline-none focus:ring-4 focus:ring-blue-400/30 shadow-2xl" %>
              <div class="mt-3 flex items-center justify-center space-x-4 text-xs text-white/60">
                <% search_benefits.each do |benefit| %>
                  <span><%= benefit %></span>
                <% end %>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Refined Statistics Section -->
<section class="py-24 bg-gradient-to-br from-slate-50 via-white to-blue-50/30 relative overflow-hidden">
  <!-- Subtle background pattern -->
  <div class="absolute inset-0 opacity-[0.02]">
    <div class="absolute inset-0" style="background-image: radial-gradient(circle at 25% 25%, #3b82f6 2px, transparent 2px), radial-gradient(circle at 75% 75%, #8b5cf6 2px, transparent 2px); background-size: 60px 60px;"></div>
  </div>
  
  <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-20">
      <div class="inline-flex items-center space-x-2 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-full px-6 py-2 mb-8 border border-blue-100">
        <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
          <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="text-sm font-medium text-blue-800">Platform Statistics</span>
      </div>
      <h2 class="text-4xl lg:text-6xl font-light text-slate-900 mb-6 tracking-tight">
        Trusted by 
        <span class="font-medium text-transparent bg-clip-text bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600">
          Thousands
        </span>
      </h2>
      <p class="text-xl text-slate-600 max-w-3xl mx-auto font-light leading-relaxed">
        Join our growing community of satisfied homeowners who discovered their perfect property through our platform
      </p>
    </div>
    
    <div class="grid grid-cols-2 lg:grid-cols-4 gap-8">
      <!-- Total Properties -->
      <div class="group text-center">
        <div class="bg-white/80 backdrop-blur-sm rounded-3xl p-8 lg:p-10 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-slate-100/50 hover:border-blue-200/50">
          <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
            </svg>
          </div>
          <div class="text-4xl lg:text-5xl font-light text-slate-900 mb-3 counter" data-target="<%= total_properties_stat %>">0</div>
          <div class="text-slate-600 font-medium mb-2">Premium Properties</div>
          <div class="text-sm text-emerald-600 font-medium"><%= growth_rate_text %></div>
        </div>
      </div>
      
      <!-- Available Properties -->
      <div class="group text-center">
        <div class="bg-white/80 backdrop-blur-sm rounded-3xl p-8 lg:p-10 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-slate-100/50 hover:border-emerald-200/50">
          <div class="w-16 h-16 bg-gradient-to-br from-emerald-500 to-green-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <div class="text-4xl lg:text-5xl font-light text-slate-900 mb-3 counter" data-target="<%= available_properties_stat %>">0</div>
          <div class="text-slate-600 font-medium mb-2">Available Now</div>
          <div class="text-sm text-blue-600 font-medium">Updated hourly</div>
        </div>
      </div>
      
      <!-- Happy Clients -->
      <div class="group text-center">
        <div class="bg-white/80 backdrop-blur-sm rounded-3xl p-8 lg:p-10 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-slate-100/50 hover:border-purple-200/50">
          <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <div class="text-4xl lg:text-5xl font-light text-slate-900 mb-3 counter" data-target="<%= total_users_stat %>">0</div>
          <div class="text-slate-600 font-medium mb-2">Happy Families</div>
          <div class="text-sm text-amber-600 font-medium"><%= platform_rating %>★ rating</div>
        </div>
      </div>
      
      <!-- Cities Covered -->
      <div class="group text-center">
        <div class="bg-white/80 backdrop-blur-sm rounded-3xl p-8 lg:p-10 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-slate-100/50 hover:border-orange-200/50">
          <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <div class="text-4xl lg:text-5xl font-light text-slate-900 mb-3 counter" data-target="<%= cities_count_stat %>">0</div>
          <div class="text-slate-600 font-medium mb-2">Cities Covered</div>
          <div class="text-sm text-indigo-600 font-medium">Nationwide</div>
        </div>
      </div>
    </div>
    
    <!-- Trust Indicators -->
    <div class="mt-20 text-center">
      <p class="text-slate-400 mb-8 font-light">Trusted by industry leaders</p>
      <div class="flex justify-center items-center space-x-12 lg:space-x-16">
        <div class="flex items-center space-x-3 text-slate-400 hover:text-slate-600 transition-colors duration-300">
          <div class="w-8 h-8 bg-gradient-to-br from-amber-400 to-orange-500 rounded-lg flex items-center justify-center">
            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
            </svg>
          </div>
          <span class="font-medium"><%= trust_badges[0][:text] if trust_badges[0] %></span>
        </div>
        <div class="flex items-center space-x-3 text-slate-400 hover:text-slate-600 transition-colors duration-300">
          <div class="w-8 h-8 bg-gradient-to-br from-green-400 to-emerald-500 rounded-lg flex items-center justify-center">
            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <span class="font-medium"><%= trust_badges[1][:text] if trust_badges[1] %></span>
        </div>
        <div class="flex items-center space-x-3 text-slate-400 hover:text-slate-600 transition-colors duration-300">
          <div class="w-8 h-8 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-lg flex items-center justify-center">
            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <span class="font-medium"><%= trust_badges[2][:text] if trust_badges[2] %></span>
        </div>
        <div class="flex items-center space-x-3 text-slate-400 hover:text-slate-600 transition-colors duration-300">
          <div class="w-8 h-8 bg-gradient-to-br from-purple-400 to-pink-500 rounded-lg flex items-center justify-center">
            <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
            </svg>
          </div>
          <span class="font-medium"><%= trust_badges[3][:text] if trust_badges[3] %></span>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Enhanced Featured Properties Section -->
<section class="py-24 bg-gradient-to-br from-white via-slate-50/50 to-indigo-50/30 relative overflow-hidden">
  <!-- Ambient background effects -->
  <div class="absolute inset-0 overflow-hidden">
    <div class="absolute -top-48 -right-48 w-96 h-96 bg-gradient-to-br from-blue-100/40 to-indigo-100/40 rounded-full blur-3xl"></div>
    <div class="absolute -bottom-48 -left-48 w-96 h-96 bg-gradient-to-br from-purple-100/40 to-pink-100/40 rounded-full blur-3xl"></div>
  </div>
  
  <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-20">
      <!-- Premium badge -->
      <div class="inline-flex items-center space-x-3 bg-gradient-to-r from-slate-50 to-white rounded-full px-6 py-3 mb-8 border border-slate-200 shadow-sm">
        <div class="w-2 h-2 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full animate-pulse"></div>
        <span class="text-sm font-medium text-slate-700">Premium Collection</span>
        <div class="flex items-center space-x-1">
          <svg class="w-4 h-4 text-amber-400" fill="currentColor" viewBox="0 0 20 20">
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
          </svg>
          <span class="text-xs text-slate-500">Curated</span>
        </div>
      </div>
      
      <h2 class="text-5xl lg:text-7xl font-light text-slate-900 mb-8 tracking-tight">
        <span class="block">Featured</span>
        <span class="text-transparent bg-clip-text bg-gradient-to-r from-slate-700 via-blue-600 to-indigo-600 font-medium">
          Properties
        </span>
      </h2>
      <p class="text-xl text-slate-600 max-w-4xl mx-auto leading-relaxed font-light">
        🌟 Handpicked luxury homes • 💎 Exclusive listings • 🔥 Limited time offers
      </p>
      
      <!-- Live update indicator -->
      <div class="mt-8 flex justify-center">
        <div class="bg-emerald-50 border border-emerald-200 rounded-full px-4 py-2">
          <div class="flex items-center space-x-2">
            <div class="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
            <span class="text-sm font-medium text-emerald-700">⚡ New properties added daily • 🎯 Personalized matches</span>
          </div>
        </div>
      </div>
    </div>
    
    <% if @featured_properties.any? %>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10">
        <% @featured_properties.each_with_index do |property, index| %>
          <div class="group relative bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-700 transform hover:-translate-y-3 border border-slate-100/50 overflow-hidden">
            <!-- Trending Badge -->
            <% if index < 2 %>
              <div class="absolute top-4 left-4 z-30">
                <div class="bg-gradient-to-r from-red-500 to-pink-500 text-white px-3 py-1.5 rounded-full text-xs font-semibold animate-pulse shadow-lg backdrop-blur-sm border border-white/20">
                  🔥 TRENDING
                </div>
              </div>
            <% end %>
            
            <div class="relative h-80 lg:h-96 overflow-hidden">
              <% if property.photos.attached? && property.photos.any? %>
                <%= image_tag property.photos.first, 
                    class: "property-image w-full h-full object-cover group-hover:scale-110 transition-transform duration-700", 
                    alt: property.title,
                    loading: "lazy" %>
              <% else %>
                <div class="w-full h-full bg-gradient-to-br from-slate-200 via-gray-300 to-slate-400 flex items-center justify-center group-hover:from-slate-300 group-hover:to-slate-500 transition-all duration-500">
                  <div class="text-center">
                    <svg class="w-20 h-20 text-white/80 mx-auto mb-3 drop-shadow-lg" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd"></path>
                    </svg>
                    <p class="text-white font-semibold text-lg drop-shadow-md">🏠 Premium Property</p>
                  </div>
                </div>
              <% end %>
              
              <!-- High Contrast Gradient Overlay -->
              <div class="absolute inset-0 bg-gradient-to-t from-black/80 via-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              
              <!-- Property Type Badge -->
              <div class="absolute top-4 right-4 z-20">
                <span class="bg-black/70 backdrop-blur-sm text-white px-3 py-1.5 rounded-full text-sm font-bold border border-white/20 shadow-lg">
                  🏡 <%= property.property_type.humanize %>
                </span>
              </div>
              
              <!-- Minimal Hover Details Overlay -->
              <div class="property-overlay absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                <!-- Essential Property Details on Hover -->
                <div class="absolute bottom-0 left-0 right-0 p-6 text-white">
                  <div class="mb-4">
                    <h4 class="text-2xl font-bold mb-2 drop-shadow-lg"><%= property.title %></h4>
                    <p class="text-sm opacity-90 mb-4 line-clamp-1 drop-shadow-md"><%= property.description %></p>
                  </div>
                  
                  <!-- Compact Quick Stats -->
                  <div class="property-stats flex gap-4 mb-4">
                    <div class="bg-white/25 backdrop-blur-md rounded-lg px-3 py-2 text-center border border-white/20">
                      <div class="text-lg font-bold"><%= property.bedrooms %></div>
                      <div class="text-xs opacity-90">🛏️ Beds</div>
                    </div>
                    <div class="bg-white/25 backdrop-blur-md rounded-lg px-3 py-2 text-center border border-white/20">
                      <div class="text-lg font-bold"><%= property.bathrooms %></div>
                      <div class="text-xs opacity-90">🚿 Baths</div>
                    </div>
                    <div class="bg-white/25 backdrop-blur-md rounded-lg px-3 py-2 text-center border border-white/20">
                      <div class="text-lg font-bold"><%= property.square_feet %></div>
                      <div class="text-xs opacity-90">📐 Sq Ft</div>
                    </div>
                  </div>
                  
                  <!-- Single Action Button -->
                  <div class="action-buttons">
                    <%= link_to property_path(property), class: "w-full bg-white/90 backdrop-blur-md text-gray-900 py-3 px-6 rounded-xl font-bold text-center hover:bg-white transition-all duration-300 transform hover:scale-105 shadow-lg border border-white/30" do %>
                      👁️ View Property Details
                    <% end %>
                  </div>
                </div>
              </div>
              
              <!-- High Contrast Price Badge -->
              <div class="price-badge absolute bottom-4 left-4 z-20">
                <div class="bg-gradient-to-r from-emerald-600 to-green-600 text-white px-4 py-2.5 rounded-xl shadow-xl border border-white/20">
                  <span class="text-xl font-bold drop-shadow-md">$<%= number_with_delimiter(property.price) %></span>
                </div>
              </div>
              
              <!-- Heart/Save Button -->
              <div class="absolute top-4 right-16 opacity-0 group-hover:opacity-100 transition-all duration-300 transform -translate-y-2 group-hover:translate-y-0 z-30">
                <button class="heart-button bg-white/90 backdrop-blur-md text-red-500 p-3 rounded-full shadow-xl hover:bg-white hover:scale-110 transition-all duration-300 border border-white/30">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
                  </svg>
                </button>
              </div>
            </div>
          </div>
        <% end %>
      </div>
      
      <div class="text-center mt-16">
        <%= link_to properties_path, 
            class: "inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white font-semibold rounded-2xl hover:from-blue-700 hover:to-blue-800 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl" do %>
          🏠 View All Properties
          <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
          </svg>
        <% end %>
      </div>
    <% else %>
      <div class="text-center py-20">
        <div class="max-w-md mx-auto">
          <div class="w-32 h-32 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <svg class="w-16 h-16 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-gray-900 mb-4">🚀 New Properties Coming Soon!</h3>
          <p class="text-gray-600 mb-6">We're curating the best properties just for you. Be the first to know when they're available!</p>
          <button class="bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold py-3 px-8 rounded-xl hover:from-blue-700 hover:to-purple-700 transition-all duration-300 transform hover:scale-105">
            🔔 Notify Me
          </button>
        </div>
      </div>
    <% end %>
    
    <!-- View All Properties CTA -->
    <div class="text-center mt-16">
      <%= link_to properties_path, class: "inline-flex items-center space-x-2 bg-white text-gray-900 font-bold py-4 px-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 border border-gray-200" do %>
        <span>🏠 Explore All Properties</span>
        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
        </svg>
      <% end %>
    </div>
  </div>
</section>

<!-- Modern Why Choose Us Section -->
<section class="py-24 bg-gradient-to-br from-slate-900 via-slate-800 to-indigo-900 relative overflow-hidden">
  <!-- Ambient background effects -->
  <div class="absolute inset-0">
    <div class="absolute inset-0 bg-gradient-to-r from-blue-900/20 via-purple-900/20 to-indigo-900/20"></div>
    <div class="absolute top-1/4 left-1/6 w-96 h-96 bg-gradient-to-br from-blue-400/5 to-cyan-400/5 rounded-full blur-3xl"></div>
    <div class="absolute bottom-1/4 right-1/6 w-80 h-80 bg-gradient-to-br from-purple-400/5 to-pink-400/5 rounded-full blur-3xl"></div>
  </div>
  
  <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-20">
      <div class="inline-flex items-center space-x-2 bg-white/5 backdrop-blur-md rounded-full px-6 py-3 mb-8 border border-white/10">
        <svg class="w-5 h-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
        </svg>
        <span class="text-sm font-medium text-white/90">Why Choose Ofie</span>
      </div>
      
      <h2 class="text-5xl lg:text-6xl font-light text-white mb-8 tracking-tight">
        Experience the
        <span class="block text-transparent bg-clip-text bg-gradient-to-r from-cyan-300 via-blue-300 to-indigo-300 font-medium">
          Difference
        </span>
      </h2>
      <p class="text-xl text-white/70 max-w-3xl mx-auto font-light leading-relaxed">
        <%= features_section_description %>
      </p>
    </div>
    
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-10">
      <!-- Trust & Experience -->
      <div class="group">
        <div class="bg-white/[0.08] backdrop-blur-xl rounded-3xl p-8 lg:p-10 border border-white/10 hover:bg-white/[0.12] transition-all duration-500 transform hover:-translate-y-2">
          <div class="w-16 h-16 bg-gradient-to-br from-emerald-400 to-cyan-500 rounded-2xl flex items-center justify-center mb-8 group-hover:scale-110 transition-transform duration-300 shadow-xl">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-semibold text-white mb-4">Trusted Excellence</h3>
          <p class="text-white/70 leading-relaxed mb-6">Over <%= format_large_number(total_families_count) %> successful transactions and a <%= platform_rating %>-star rating from satisfied clients who found their perfect home through our platform.</p>
          <div class="flex items-center space-x-3">
            <div class="flex -space-x-1">
              <div class="w-8 h-8 bg-gradient-to-r from-emerald-400 to-cyan-400 rounded-full border-2 border-slate-800"></div>
              <div class="w-8 h-8 bg-gradient-to-r from-blue-400 to-indigo-400 rounded-full border-2 border-slate-800"></div>
              <div class="w-8 h-8 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full border-2 border-slate-800"></div>
            </div>
            <span class="text-sm text-white/60">Join thousands of happy families</span>
          </div>
        </div>
      </div>
      
      <!-- Speed & Efficiency -->
      <div class="group">
        <div class="bg-white/[0.08] backdrop-blur-xl rounded-3xl p-8 lg:p-10 border border-white/10 hover:bg-white/[0.12] transition-all duration-500 transform hover:-translate-y-2">
          <div class="w-16 h-16 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-2xl flex items-center justify-center mb-8 group-hover:scale-110 transition-transform duration-300 shadow-xl">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-semibold text-white mb-4">Lightning Fast</h3>
          <p class="text-white/70 leading-relaxed mb-6">AI-powered search delivers instant results. Smart matching algorithms connect you with perfect properties in seconds, not days.</p>
          <div class="bg-white/10 rounded-xl p-4 border border-white/20">
            <div class="flex items-center justify-between text-sm">
              <span class="text-white/80">Average search time</span>
              <span class="text-white font-semibold"><%= search_time_text %></span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Quality & Premium -->
      <div class="group">
        <div class="bg-white/[0.08] backdrop-blur-xl rounded-3xl p-8 lg:p-10 border border-white/10 hover:bg-white/[0.12] transition-all duration-500 transform hover:-translate-y-2">
          <div class="w-16 h-16 bg-gradient-to-br from-purple-400 to-pink-500 rounded-2xl flex items-center justify-center mb-8 group-hover:scale-110 transition-transform duration-300 shadow-xl">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-semibold text-white mb-4">Premium Quality</h3>
          <p class="text-white/70 leading-relaxed mb-6">Every property undergoes rigorous verification. Professional photography, detailed inspections, and quality guarantees ensure premium standards.</p>
          <div class="space-y-2">
            <div class="flex items-center space-x-2">
              <svg class="w-4 h-4 text-emerald-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              <span class="text-sm text-white/80">Verified listings</span>
            </div>
            <div class="flex items-center space-x-2">
              <svg class="w-4 h-4 text-emerald-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              <span class="text-sm text-white/80">Professional photography</span>
            </div>
            <div class="flex items-center space-x-2">
              <svg class="w-4 h-4 text-emerald-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              <span class="text-sm text-white/80">Quality inspections</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Ultimate CTA Section -->
<section class="py-24 bg-gradient-to-br from-indigo-900 via-purple-900 to-slate-900 relative overflow-hidden">
  <!-- Dynamic background effects -->
  <div class="absolute inset-0">
    <div class="absolute inset-0 bg-gradient-to-r from-indigo-600/10 via-purple-600/10 to-pink-600/10 animate-pulse"></div>
    <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-br from-cyan-400/8 to-blue-400/8 rounded-full blur-3xl animate-float"></div>
    <div class="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-br from-purple-400/8 to-pink-400/8 rounded-full blur-3xl animate-float-delayed"></div>
  </div>
  
  <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
    <div class="max-w-5xl mx-auto">
      <!-- Main CTA content -->
      <div class="mb-16">
        <div class="inline-flex items-center space-x-3 bg-white/10 backdrop-blur-md rounded-full px-6 py-3 mb-8 border border-white/20">
          <div class="w-2 h-2 bg-emerald-400 rounded-full animate-pulse"></div>
          <span class="text-sm font-medium text-white/90">Ready to Get Started?</span>
        </div>
        
        <h2 class="text-5xl lg:text-7xl font-light text-white mb-8 tracking-tight">
          <span class="block"><%= cta_title_line_1 %></span>
          <span class="text-transparent bg-clip-text bg-gradient-to-r from-cyan-300 via-blue-300 to-purple-300 font-medium">
            <%= cta_title_line_2 %>
          </span>
        </h2>
        
        <p class="text-xl lg:text-2xl text-white/70 mb-12 leading-relaxed max-w-4xl mx-auto font-light">
          <%= cta_description %>
        </p>
      </div>
      
      <!-- Feature highlights -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
        <div class="bg-white/[0.08] backdrop-blur-xl rounded-2xl p-6 border border-white/10">
          <div class="text-3xl mb-4">⚡</div>
          <h3 class="text-lg font-semibold text-white mb-2">Instant Results</h3>
          <p class="text-white/70 text-sm">AI-powered matching finds your ideal home in seconds</p>
        </div>
        <div class="bg-white/[0.08] backdrop-blur-xl rounded-2xl p-6 border border-white/10">
          <div class="text-3xl mb-4">🛡️</div>
          <h3 class="text-lg font-semibold text-white mb-2">Verified Quality</h3>
          <p class="text-white/70 text-sm">Every property verified and quality-guaranteed</p>
        </div>
        <div class="bg-white/[0.08] backdrop-blur-xl rounded-2xl p-6 border border-white/10">
          <div class="text-3xl mb-4">🎯</div>
          <h3 class="text-lg font-semibold text-white mb-2">Expert Support</h3>
          <p class="text-white/70 text-sm">Dedicated team guides you every step of the way</p>
        </div>
      </div>
      
      <!-- CTA buttons -->
      <div class="flex flex-col sm:flex-row gap-6 justify-center items-center mb-12">
        <%= link_to properties_path, class: "group bg-gradient-to-r from-cyan-500 via-blue-500 to-indigo-500 hover:from-cyan-600 hover:via-blue-600 hover:to-indigo-600 text-white font-semibold py-4 px-10 rounded-2xl transition-all duration-300 transform hover:scale-[1.02] shadow-2xl hover:shadow-cyan-500/25 text-lg min-w-[200px]" do %>
          <span class="flex items-center space-x-2">
            <span>Start Searching</span>
            <svg class="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
            </svg>
          </span>
        <% end %>
        
        <button class="bg-white/10 backdrop-blur-md border border-white/20 text-white font-semibold py-4 px-8 rounded-2xl hover:bg-white/20 transition-all duration-300 transform hover:scale-[1.02] text-lg min-w-[200px]">
          <span class="flex items-center space-x-2">
            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
            </svg>
            <span>Talk to Expert</span>
          </span>
        </button>
      </div>
      
      <!-- Trust indicators -->
      <div class="flex flex-wrap justify-center items-center gap-8 text-sm text-white/60">
        <% cta_trust_indicators.each do |indicator| %>
          <div class="flex items-center space-x-2">
            <svg class="w-4 h-4 text-emerald-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            <span><%= indicator %></span>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</section>

<!-- Enhanced CSS for animations -->
<style>
  @keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
  }
  
  @keyframes float-delayed {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-15px) rotate(-180deg); }
  }
  
  @keyframes gradient {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }
  
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }
  
  .animate-float-delayed {
    animation: float-delayed 8s ease-in-out infinite;
  }
  
  .animate-gradient {
    background-size: 200% 200%;
    animation: gradient 3s ease infinite;
  }
  
  .counter {
    transition: all 0.5s ease-out;
  }
  
  .line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  /* Backdrop blur fallback */
  @supports not (backdrop-filter: blur()) {
    .backdrop-blur-xl, .backdrop-blur-md, .backdrop-blur-sm {
      background-color: rgba(255, 255, 255, 0.1);
    }
  }
</style>

<!-- JavaScript for counter animation -->
<script>
  document.addEventListener('DOMContentLoaded', function() {
    const counters = document.querySelectorAll('.counter');
    
    const animateCounter = (counter) => {
      const target = parseInt(counter.dataset.target);
      const increment = target / 100;
      let current = 0;
      
      const updateCounter = () => {
        if (current < target) {
          current += increment;
          counter.textContent = Math.floor(current).toLocaleString();
          requestAnimationFrame(updateCounter);
        } else {
          counter.textContent = target.toLocaleString();
        }
      };
      
      updateCounter();
    };
    
    // Intersection Observer for counter animation
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          animateCounter(entry.target);
          observer.unobserve(entry.target);
        }
      });
    }, { threshold: 0.5 });
    
    counters.forEach(counter => observer.observe(counter));
  });
</script>
