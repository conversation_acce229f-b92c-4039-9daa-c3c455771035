<% content_for :title, "Cookie Policy - Ofie | How We Use Cookies & Your Privacy Options" %>
<% content_for :description, "Learn about <PERSON><PERSON>'s cookie policy, how we use cookies to enhance your experience, and how to manage your preferences. Transparent information about tracking and analytics." %>
<% content_for :keywords, "cookie policy, privacy settings, data tracking, web analytics, user preferences, ofie cookies" %>

<!-- Hero Section -->
<section class="relative min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-purple-950 text-white overflow-hidden">
  <!-- Advanced Background Layers -->
  <div class="absolute inset-0">
    <div class="absolute inset-0 bg-gradient-to-r from-slate-900/90 via-purple-900/80 to-slate-900/90"></div>
    <div class="absolute inset-0 opacity-20">
      <div class="absolute inset-0 bg-gradient-to-r from-purple-600/20 via-blue-600/30 to-indigo-600/20 animate-pulse"></div>
      <div class="absolute inset-0" style="background: radial-gradient(circle at 30% 20%, rgba(147, 51, 234, 0.15) 0%, transparent 40%), radial-gradient(circle at 70% 80%, rgba(59, 130, 246, 0.15) 0%, transparent 40%), radial-gradient(circle at 20% 90%, rgba(99, 102, 241, 0.10) 0%, transparent 40%);"></div>
    </div>
  </div>
  
  <!-- Floating Elements -->
  <div class="absolute inset-0 overflow-hidden pointer-events-none">
    <div class="absolute top-1/4 left-1/6 w-64 h-64 bg-gradient-to-br from-purple-400/8 to-blue-400/8 rounded-full blur-3xl animate-float"></div>
    <div class="absolute bottom-1/3 right-1/6 w-80 h-80 bg-gradient-to-br from-indigo-400/8 to-purple-400/8 rounded-full blur-3xl animate-float-delayed"></div>
  </div>
  
  <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-32 flex items-center min-h-screen">
    <div class="w-full">
      <!-- Policy Badge -->
      <div class="text-center mb-12">
        <div class="inline-flex items-center space-x-3 bg-white/5 backdrop-blur-md rounded-full px-8 py-4 mb-8 border border-white/10">
          <div class="w-12 h-12 bg-gradient-to-r from-purple-400 to-indigo-500 rounded-full flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <div class="text-left">
            <div class="text-sm font-medium text-white/90">Privacy & Transparency</div>
            <div class="text-xs text-white/60">Last updated: <%= Date.current.strftime('%B %Y') %></div>
          </div>
        </div>
      </div>
      
      <!-- Hero Title -->
      <div class="text-center mb-16">
        <h1 class="text-6xl md:text-8xl font-light mb-8 leading-[0.9] tracking-tight">
          <span class="block font-extralight text-white/95">Cookie</span>
          <span class="block text-transparent bg-clip-text bg-gradient-to-r from-purple-300 via-blue-300 to-indigo-300 font-medium animate-gradient">
            Policy
          </span>
          <span class="block text-2xl md:text-3xl mt-6 text-white/70 font-light tracking-wide">
            Your Privacy, Your Choice
          </span>
        </h1>
        
        <p class="text-lg md:text-xl mb-12 text-white/70 max-w-4xl mx-auto leading-relaxed font-light">
          We believe in transparency about how we use cookies and similar technologies to improve your experience on Ofie. This policy explains what cookies are, how we use them, and how you can control them.
        </p>
        
        <!-- Quick Stats -->
        <div class="flex flex-wrap justify-center items-center gap-8 mb-16">
          <div class="flex items-center space-x-3">
            <div class="w-12 h-12 bg-gradient-to-r from-purple-400 to-indigo-400 rounded-2xl flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div class="text-left">
              <div class="text-lg font-semibold text-white">GDPR Compliant</div>
              <div class="text-sm text-white/60">Full compliance</div>
            </div>
          </div>
          <div class="flex items-center space-x-3">
            <div class="w-12 h-12 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-2xl flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div class="text-left">
              <div class="text-lg font-semibold text-white">Full Control</div>
              <div class="text-sm text-white/60">Manage preferences</div>
            </div>
          </div>
          <div class="flex items-center space-x-3">
            <div class="w-12 h-12 bg-gradient-to-r from-emerald-400 to-teal-400 rounded-2xl flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div class="text-left">
              <div class="text-lg font-semibold text-white">Transparent</div>
              <div class="text-sm text-white/60">No hidden tracking</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- What Are Cookies Section -->
<section class="py-24 bg-gradient-to-br from-slate-50 via-white to-purple-50/30 relative overflow-hidden">
  <div class="absolute inset-0 opacity-[0.02]">
    <div class="absolute inset-0" style="background-image: radial-gradient(circle at 25% 25%, #8b5cf6 2px, transparent 2px), radial-gradient(circle at 75% 75%, #3b82f6 2px, transparent 2px); background-size: 60px 60px;"></div>
  </div>
  
  <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Section Header -->
    <div class="text-center mb-20">
      <div class="inline-flex items-center space-x-2 bg-gradient-to-r from-purple-50 to-blue-50 rounded-full px-6 py-2 mb-8 border border-purple-100">
        <svg class="w-5 h-5 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
        </svg>
        <span class="text-sm font-medium text-purple-800">Understanding Cookies</span>
      </div>
      <h2 class="text-4xl lg:text-6xl font-light text-slate-900 mb-6 tracking-tight">
        What Are 
        <span class="font-medium text-transparent bg-clip-text bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600">
          Cookies?
        </span>
      </h2>
      <p class="text-xl text-slate-600 max-w-3xl mx-auto font-light leading-relaxed">
        Simple explanation of cookies and how they enhance your browsing experience
      </p>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-20">
      <!-- Content -->
      <div class="space-y-8">
        <div class="bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-lg border border-slate-100/50">
          <div class="flex items-start space-x-4">
            <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-2xl flex items-center justify-center flex-shrink-0">
              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8 0a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1v-2z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div>
              <h3 class="text-xl font-semibold text-slate-900 mb-3">What Are Cookies?</h3>
              <p class="text-slate-600 leading-relaxed">
                Cookies are small text files that websites store on your device when you visit them. They help websites remember your preferences, login status, and other information to provide a better, more personalized experience.
              </p>
            </div>
          </div>
        </div>

        <div class="bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-lg border border-slate-100/50">
          <div class="flex items-start space-x-4">
            <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center flex-shrink-0">
              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div>
              <h3 class="text-xl font-semibold text-slate-900 mb-3">How Do They Work?</h3>
              <p class="text-slate-600 leading-relaxed">
                When you visit Ofie, our servers send cookies to your browser. These cookies contain information like your search preferences, login status, and settings. Your browser stores them and sends them back to us on subsequent visits.
              </p>
            </div>
          </div>
        </div>

        <div class="bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-lg border border-slate-100/50">
          <div class="flex items-start space-x-4">
            <div class="w-12 h-12 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-2xl flex items-center justify-center flex-shrink-0">
              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div>
              <h3 class="text-xl font-semibold text-slate-900 mb-3">Are They Safe?</h3>
              <p class="text-slate-600 leading-relaxed">
                Yes, cookies themselves are safe. They're just text files and cannot run programs or carry viruses. However, we're transparent about what information we collect and how we use it to maintain your trust and privacy.
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Visual Representation -->
      <div class="relative">
        <div class="bg-gradient-to-br from-purple-50 to-indigo-100 rounded-3xl p-8 shadow-2xl">
          <div class="text-center mb-8">
            <div class="w-24 h-24 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-xl shadow-purple-500/25">
              <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8 0a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1v-2z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <h3 class="text-2xl font-bold text-slate-900 mb-4">Cookie Types We Use</h3>
            <p class="text-slate-600 mb-6">Understanding the different types of cookies on Ofie</p>
          </div>
          
          <!-- Cookie Types Grid -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="bg-white/70 backdrop-blur-sm rounded-xl p-4 border border-white/50">
              <div class="flex items-center space-x-3 mb-3">
                <div class="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-600 rounded-lg flex items-center justify-center">
                  <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                  </svg>
                </div>
                <h4 class="font-bold text-slate-900">Essential</h4>
              </div>
              <p class="text-slate-600 text-sm">Required for the website to function properly</p>
            </div>

            <div class="bg-white/70 backdrop-blur-sm rounded-xl p-4 border border-white/50">
              <div class="flex items-center space-x-3 mb-3">
                <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                  <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"></path>
                  </svg>
                </div>
                <h4 class="font-bold text-slate-900">Functional</h4>
              </div>
              <p class="text-slate-600 text-sm">Remember your preferences and settings</p>
            </div>

            <div class="bg-white/70 backdrop-blur-sm rounded-xl p-4 border border-white/50">
              <div class="flex items-center space-x-3 mb-3">
                <div class="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center">
                  <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"></path>
                  </svg>
                </div>
                <h4 class="font-bold text-slate-900">Analytics</h4>
              </div>
              <p class="text-slate-600 text-sm">Help us improve our website performance</p>
            </div>

            <div class="bg-white/70 backdrop-blur-sm rounded-xl p-4 border border-white/50">
              <div class="flex items-center space-x-3 mb-3">
                <div class="w-8 h-8 bg-gradient-to-r from-orange-500 to-red-600 rounded-lg flex items-center justify-center">
                  <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                  </svg>
                </div>
                <h4 class="font-bold text-slate-900">Marketing</h4>
              </div>
              <p class="text-slate-600 text-sm">Show you relevant property recommendations</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- How We Use Cookies Section -->
<section class="py-24 bg-gradient-to-br from-slate-900 via-slate-800 to-purple-900 relative overflow-hidden">
  <div class="absolute inset-0">
    <div class="absolute inset-0 bg-gradient-to-r from-purple-900/20 via-blue-900/20 to-indigo-900/20"></div>
    <div class="absolute top-1/4 left-1/6 w-96 h-96 bg-gradient-to-br from-purple-400/5 to-blue-400/5 rounded-full blur-3xl"></div>
    <div class="absolute bottom-1/4 right-1/6 w-80 h-80 bg-gradient-to-br from-indigo-400/5 to-purple-400/5 rounded-full blur-3xl"></div>
  </div>
  
  <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-20">
      <div class="inline-flex items-center space-x-2 bg-white/5 backdrop-blur-md rounded-full px-6 py-3 mb-8 border border-white/10">
        <svg class="w-5 h-5 text-purple-400" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"></path>
        </svg>
        <span class="text-sm font-medium text-white/90">How We Use Them</span>
      </div>
      
      <h2 class="text-5xl lg:text-6xl font-light text-white mb-8 tracking-tight">
        Cookie
        <span class="block text-transparent bg-clip-text bg-gradient-to-r from-purple-300 via-blue-300 to-indigo-300 font-medium">
          Categories
        </span>
      </h2>
      <p class="text-xl text-white/70 max-w-3xl mx-auto font-light leading-relaxed">
        Detailed breakdown of how different types of cookies enhance your Ofie experience
      </p>
    </div>
    
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-10">
      <!-- Essential Cookies -->
      <div class="group">
        <div class="bg-white/[0.08] backdrop-blur-xl rounded-3xl p-8 lg:p-10 border border-white/10 hover:bg-white/[0.12] transition-all duration-500 transform hover:-translate-y-2">
          <div class="w-16 h-16 bg-gradient-to-br from-emerald-400 to-green-500 rounded-2xl flex items-center justify-center mb-8 group-hover:scale-110 transition-transform duration-300 shadow-xl">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-semibold text-white mb-4">Essential Cookies</h3>
          <p class="text-white/70 leading-relaxed mb-6">These cookies are necessary for the website to function and cannot be switched off. They enable core functionality like security, network management, and accessibility.</p>
          <div class="space-y-2">
            <div class="flex items-center space-x-2">
              <svg class="w-4 h-4 text-emerald-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              <span class="text-sm text-white/80">Login authentication</span>
            </div>
            <div class="flex items-center space-x-2">
              <svg class="w-4 h-4 text-emerald-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              <span class="text-sm text-white/80">Security features</span>
            </div>
            <div class="flex items-center space-x-2">
              <svg class="w-4 h-4 text-emerald-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              <span class="text-sm text-white/80">Basic functionality</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Functional Cookies -->
      <div class="group">
        <div class="bg-white/[0.08] backdrop-blur-xl rounded-3xl p-8 lg:p-10 border border-white/10 hover:bg-white/[0.12] transition-all duration-500 transform hover:-translate-y-2">
          <div class="w-16 h-16 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-2xl flex items-center justify-center mb-8 group-hover:scale-110 transition-transform duration-300 shadow-xl">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-semibold text-white mb-4">Functional Cookies</h3>
          <p class="text-white/70 leading-relaxed mb-6">These cookies enable enhanced functionality and personalization, such as remembering your search filters and preferred property types.</p>
          <div class="bg-white/10 rounded-xl p-4 border border-white/20">
            <div class="flex items-center justify-between text-sm mb-2">
              <span class="text-white/80">Search preferences</span>
              <span class="text-white font-semibold">Saved</span>
            </div>
            <div class="flex items-center justify-between text-sm">
              <span class="text-white/80">Location settings</span>
              <span class="text-white font-semibold">Remembered</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Analytics Cookies -->
      <div class="group">
        <div class="bg-white/[0.08] backdrop-blur-xl rounded-3xl p-8 lg:p-10 border border-white/10 hover:bg-white/[0.12] transition-all duration-500 transform hover:-translate-y-2">
          <div class="w-16 h-16 bg-gradient-to-br from-purple-400 to-pink-500 rounded-2xl flex items-center justify-center mb-8 group-hover:scale-110 transition-transform duration-300 shadow-xl">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-semibold text-white mb-4">Analytics Cookies</h3>
          <p class="text-white/70 leading-relaxed mb-6">Help us understand how visitors interact with our website by collecting and reporting information anonymously to improve user experience.</p>
          <div class="space-y-2">
            <div class="flex items-center space-x-2">
              <svg class="w-4 h-4 text-purple-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              <span class="text-sm text-white/80">Google Analytics</span>
            </div>
            <div class="flex items-center space-x-2">
              <svg class="w-4 h-4 text-purple-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              <span class="text-sm text-white/80">Performance monitoring</span>
            </div>
            <div class="flex items-center space-x-2">
              <svg class="w-4 h-4 text-purple-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              <span class="text-sm text-white/80">User behavior insights</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Marketing Cookies -->
      <div class="group">
        <div class="bg-white/[0.08] backdrop-blur-xl rounded-3xl p-8 lg:p-10 border border-white/10 hover:bg-white/[0.12] transition-all duration-500 transform hover:-translate-y-2">
          <div class="w-16 h-16 bg-gradient-to-br from-orange-400 to-red-500 rounded-2xl flex items-center justify-center mb-8 group-hover:scale-110 transition-transform duration-300 shadow-xl">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-semibold text-white mb-4">Marketing Cookies</h3>
          <p class="text-white/70 leading-relaxed mb-6">Used to track visitors across websites to display relevant property recommendations and advertisements based on your interests and search history.</p>
          <div class="bg-white/10 rounded-xl p-4 border border-white/20">
            <div class="flex items-center justify-between text-sm mb-2">
              <span class="text-white/80">Personalized recommendations</span>
              <span class="text-white font-semibold">Enhanced</span>
            </div>
            <div class="flex items-center justify-between text-sm">
              <span class="text-white/80">Relevant advertisements</span>
              <span class="text-white font-semibold">Targeted</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Cookie Management Section -->
<section class="py-24 bg-gradient-to-br from-white via-slate-50/50 to-purple-50/30 relative overflow-hidden">
  <div class="absolute inset-0 overflow-hidden">
    <div class="absolute -top-48 -right-48 w-96 h-96 bg-gradient-to-br from-purple-100/40 to-indigo-100/40 rounded-full blur-3xl"></div>
    <div class="absolute -bottom-48 -left-48 w-96 h-96 bg-gradient-to-br from-blue-100/40 to-purple-100/40 rounded-full blur-3xl"></div>
  </div>
  
  <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-20">
      <div class="inline-flex items-center space-x-3 bg-gradient-to-r from-slate-50 to-white rounded-full px-6 py-3 mb-8 border border-slate-200 shadow-sm">
        <div class="w-2 h-2 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-full animate-pulse"></div>
        <span class="text-sm font-medium text-slate-700">Your Control</span>
      </div>
      
      <h2 class="text-5xl lg:text-7xl font-light text-slate-900 mb-8 tracking-tight">
        <span class="block">Manage Your</span>
        <span class="text-transparent bg-clip-text bg-gradient-to-r from-slate-700 via-purple-600 to-indigo-600 font-medium">
          Preferences
        </span>
      </h2>
      <p class="text-xl text-slate-600 max-w-4xl mx-auto leading-relaxed font-light">
        You have full control over your cookie preferences and can change them at any time
      </p>
    </div>

    <!-- Cookie Management Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-20">
      <!-- Management Options -->
      <div class="space-y-8">
        <div class="bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-lg border border-slate-100/50">
          <div class="flex items-start space-x-4">
            <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-2xl flex items-center justify-center flex-shrink-0">
              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div>
              <h3 class="text-xl font-semibold text-slate-900 mb-3">Cookie Settings</h3>
              <p class="text-slate-600 leading-relaxed mb-4">
                Use our cookie preference center to easily enable or disable different types of cookies. Your choices are saved and applied immediately.
              </p>
              <button class="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-xl font-semibold transition-colors duration-200 shadow-lg">
                Manage Cookie Preferences
              </button>
            </div>
          </div>
        </div>

        <div class="bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-lg border border-slate-100/50">
          <div class="flex items-start space-x-4">
            <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center flex-shrink-0">
              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div>
              <h3 class="text-xl font-semibold text-slate-900 mb-3">Browser Controls</h3>
              <p class="text-slate-600 leading-relaxed">
                Most browsers allow you to control cookies through their settings. You can delete existing cookies and prevent new ones from being set.
              </p>
            </div>
          </div>
        </div>

        <div class="bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-lg border border-slate-100/50">
          <div class="flex items-start space-x-4">
            <div class="w-12 h-12 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-2xl flex items-center justify-center flex-shrink-0">
              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div>
              <h3 class="text-xl font-semibold text-slate-900 mb-3">Third-Party Services</h3>
              <p class="text-slate-600 leading-relaxed">
                Some cookies are set by third-party services we use. You can opt out of these services directly through their own privacy settings.
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Browser Instructions -->
      <div class="bg-gradient-to-br from-purple-50 to-indigo-100 rounded-3xl p-8 shadow-2xl">
        <div class="text-center mb-8">
          <h3 class="text-2xl font-bold text-slate-900 mb-4">Browser Instructions</h3>
          <p class="text-slate-600">Quick guides for popular browsers</p>
        </div>
        
        <!-- Browser Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <!-- Chrome -->
          <div class="bg-white/70 backdrop-blur-sm rounded-xl p-6 border border-white/50">
            <div class="flex items-center space-x-3 mb-4">
              <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-green-500 rounded-xl flex items-center justify-center">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm0 2.4c5.302 0 9.6 4.298 9.6 9.6s-4.298 9.6-9.6 9.6S2.4 17.302 2.4 12 6.698 2.4 12 2.4z"/>
                </svg>
              </div>
              <h4 class="font-bold text-slate-900">Chrome</h4>
            </div>
            <ol class="text-sm text-slate-600 space-y-1">
              <li>1. Click the three dots menu</li>
              <li>2. Go to Settings → Privacy</li>
              <li>3. Click "Cookies and site data"</li>
              <li>4. Manage your preferences</li>
            </ol>
          </div>

          <!-- Firefox -->
          <div class="bg-white/70 backdrop-blur-sm rounded-xl p-6 border border-white/50">
            <div class="flex items-center space-x-3 mb-4">
              <div class="w-10 h-10 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl flex items-center justify-center">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm0 2.4c5.302 0 9.6 4.298 9.6 9.6s-4.298 9.6-9.6 9.6S2.4 17.302 2.4 12 6.698 2.4 12 2.4z"/>
                </svg>
              </div>
              <h4 class="font-bold text-slate-900">Firefox</h4>
            </div>
            <ol class="text-sm text-slate-600 space-y-1">
              <li>1. Click the hamburger menu</li>
              <li>2. Go to Settings → Privacy</li>
              <li>3. Scroll to "Cookies and Site Data"</li>
              <li>4. Click "Manage Data"</li>
            </ol>
          </div>

          <!-- Safari -->
          <div class="bg-white/70 backdrop-blur-sm rounded-xl p-6 border border-white/50">
            <div class="flex items-center space-x-3 mb-4">
              <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm0 2.4c5.302 0 9.6 4.298 9.6 9.6s-4.298 9.6-9.6 9.6S2.4 17.302 2.4 12 6.698 2.4 12 2.4z"/>
                </svg>
              </div>
              <h4 class="font-bold text-slate-900">Safari</h4>
            </div>
            <ol class="text-sm text-slate-600 space-y-1">
              <li>1. Click Safari → Preferences</li>
              <li>2. Go to Privacy tab</li>
              <li>3. Click "Manage Website Data"</li>
              <li>4. Remove or block as needed</li>
            </ol>
          </div>

          <!-- Edge -->
          <div class="bg-white/70 backdrop-blur-sm rounded-xl p-6 border border-white/50">
            <div class="flex items-center space-x-3 mb-4">
              <div class="w-10 h-10 bg-gradient-to-r from-blue-600 to-cyan-500 rounded-xl flex items-center justify-center">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 0C5.373 0 0 5.373 0 12s5.373 12 12 12 12-5.373 12-12S18.627 0 12 0zm0 2.4c5.302 0 9.6 4.298 9.6 9.6s-4.298 9.6-9.6 9.6S2.4 17.302 2.4 12 6.698 2.4 12 2.4z"/>
                </svg>
              </div>
              <h4 class="font-bold text-slate-900">Edge</h4>
            </div>
            <ol class="text-sm text-slate-600 space-y-1">
              <li>1. Click the three dots menu</li>
              <li>2. Go to Settings → Site permissions</li>
              <li>3. Click "Cookies and site data"</li>
              <li>4. Configure your settings</li>
            </ol>
          </div>
        </div>

        <!-- Additional Controls -->
        <div class="mt-8 p-6 bg-white/50 backdrop-blur-sm rounded-2xl border border-white/50">
          <h4 class="font-bold text-slate-900 mb-3">Additional Control Options</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-slate-600">
            <div class="flex items-center space-x-2">
              <svg class="w-4 h-4 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              <span>Do Not Track browser setting</span>
            </div>
            <div class="flex items-center space-x-2">
              <svg class="w-4 h-4 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              <span>Private/Incognito browsing</span>
            </div>
            <div class="flex items-center space-x-2">
              <svg class="w-4 h-4 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              <span>Third-party cookie blocking</span>
            </div>
            <div class="flex items-center space-x-2">
              <svg class="w-4 h-4 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              <span>Ad blocker extensions</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Contact & Updates Section -->
    <div class="bg-gradient-to-r from-purple-600 to-indigo-600 rounded-3xl shadow-2xl shadow-purple-500/25 p-12 text-white text-center">
      <div class="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mx-auto mb-6">
        <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
          <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
          <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
        </svg>
      </div>
      <h2 class="text-4xl font-bold mb-6">Questions About Our Cookie Policy?</h2>
      <p class="text-xl text-purple-100 mb-8 max-w-3xl mx-auto">
        We're committed to transparency and are here to help you understand how we use cookies to improve your experience
      </p>
      <div class="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6">
        <a href="<%= contact_path %>" class="bg-white text-purple-600 hover:bg-purple-50 px-8 py-4 rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
          Contact Our Privacy Team
        </a>
        <a href="<%= privacy_policy_path %>" class="border-2 border-white text-white hover:bg-white hover:text-purple-600 px-8 py-4 rounded-2xl font-bold transition-all duration-300 transform hover:-translate-y-0.5">
          View Privacy Policy
        </a>
      </div>
      <div class="mt-8 pt-8 border-t border-white/20">
        <p class="text-purple-200 text-sm">
          Last updated: <%= Date.current.strftime('%B %d, %Y') %> | 
          This policy is reviewed and updated regularly to ensure compliance with current regulations
        </p>
      </div>
    </div>
  </div>
</section>

<!-- Enhanced CSS for animations -->
<style>
  @keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
  }
  
  @keyframes float-delayed {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-15px) rotate(-180deg); }
  }
  
  @keyframes gradient {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }
  
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }
  
  .animate-float-delayed {
    animation: float-delayed 8s ease-in-out infinite;
  }
  
  .animate-gradient {
    background-size: 200% 200%;
    animation: gradient 3s ease infinite;
  }
  
  /* Backdrop blur fallback */
  @supports not (backdrop-filter: blur()) {
    .backdrop-blur-xl, .backdrop-blur-md, .backdrop-blur-sm {
      background-color: rgba(255, 255, 255, 0.1);
    }
  }
</style>
