<% content_for :title, "Help Center - Ofie Knowledge Base" %>
<% content_for :description, "Comprehensive help center with guides, tutorials, and answers for landlords and tenants using the Ofie rental platform." %>

<!-- Hero Section -->
<div class="relative min-h-screen bg-gradient-to-br from-slate-900 via-emerald-900 to-teal-900 overflow-hidden">
  <!-- Background Effects -->
  <div class="absolute inset-0 bg-gradient-to-br from-black/30 via-transparent to-black/20"></div>
  <div class="absolute top-0 right-1/4 w-96 h-96 bg-gradient-to-l from-emerald-400/20 to-teal-500/20 rounded-full blur-3xl"></div>
  <div class="absolute bottom-0 left-1/4 w-96 h-96 bg-gradient-to-r from-green-400/20 to-emerald-500/20 rounded-full blur-3xl"></div>
  
  <!-- Floating Elements -->
  <div class="absolute top-20 left-10 w-32 h-32 bg-white/5 rounded-full blur-2xl animate-pulse"></div>
  <div class="absolute bottom-40 right-20 w-48 h-48 bg-emerald-400/10 rounded-full blur-3xl animate-pulse" style="animation-delay: 2s;"></div>
  
  <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-32">
    <div class="text-center text-white mb-16">
      <div class="mb-8">
        <div class="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-3xl mb-8 shadow-2xl shadow-emerald-500/25">
          <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2C13.1 2 14 2.9 14 4V8C14 9.1 13.1 10 12 10S10 9.1 10 8V4C10 2.9 10.9 2 12 2ZM21 9V7L20 8L19 7V9C19 10.1 19.9 11 21 11S23 10.1 23 9ZM3 9V7L2 8L1 7V9C1 10.1 1.9 11 3 11S5 10.1 5 9ZM12 12C15.87 12 19 15.13 19 19V21C19 21.55 18.55 22 18 22H6C5.45 22 5 21.55 5 21V19C5 15.13 8.13 12 12 12ZM12 13.5C9.24 13.5 7 15.74 7 18.5V20H17V18.5C17 15.74 14.76 13.5 12 13.5ZM12 15C13.38 15 14.5 16.12 14.5 17.5S13.38 20 12 20 9.5 18.88 9.5 17.5 10.62 15 12 15Z"/>
            <circle cx="12" cy="6" r="1.5" fill="white" opacity="0.8"/>
            <circle cx="21" cy="8" r="1" fill="white" opacity="0.6"/>
            <circle cx="3" cy="8" r="1" fill="white" opacity="0.6"/>
          </svg>
        </div>
        <h1 class="text-6xl md:text-7xl font-bold mb-6 leading-tight">
          Knowledge <span class="bg-gradient-to-r from-emerald-400 via-teal-400 to-green-400 bg-clip-text text-transparent">Center</span>
        </h1>
        <p class="text-2xl md:text-3xl text-emerald-100 mb-8 max-w-4xl mx-auto leading-relaxed">
          Everything you need to know about using Ofie effectively
        </p>
      </div>
      
      <!-- Search Bar -->
      <div class="bg-white/10 backdrop-blur-xl rounded-3xl border border-white/20 p-8 max-w-4xl mx-auto mb-12">
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-6 flex items-center pointer-events-none">
            <svg class="h-6 w-6 text-white/70" fill="currentColor" viewBox="0 0 24 24">
              <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
              <circle cx="9.5" cy="9.5" r="2" fill="white" opacity="0.3"/>
            </svg>
          </div>
          <input type="text" 
                 placeholder="Search for help articles, guides, and tutorials..."
                 class="w-full pl-16 pr-6 py-5 text-lg font-medium border-0 rounded-2xl bg-white/20 backdrop-blur-sm text-white placeholder-white/70 focus:ring-2 focus:ring-emerald-500 focus:bg-white/30 transition-all duration-300"
                 data-controller="search"
                 data-search-target="input"
                 data-action="input->search#search">
          <div class="absolute inset-y-0 right-0 pr-6 flex items-center">
            <kbd class="hidden sm:inline-flex items-center px-3 py-1 border border-white/30 rounded-lg text-sm text-white/70">⌘K</kbd>
          </div>
        </div>
      </div>
      
      <!-- Quick Stats -->
      <div class="flex flex-wrap items-center justify-center gap-8 text-emerald-200">
        <div class="flex items-center space-x-3 bg-white/10 backdrop-blur-sm rounded-2xl px-6 py-3 border border-white/20">
          <div class="w-10 h-10 bg-gradient-to-r from-green-400 to-emerald-500 rounded-xl flex items-center justify-center">
            <svg class="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
              <path d="M8,12V14H16V12H8M8,16V18H13V16H8Z" fill="white" opacity="0.7"/>
              <circle cx="15" cy="6" r="1" fill="white" opacity="0.5"/>
            </svg>
          </div>
          <span class="font-bold text-lg">150+ Articles</span>
        </div>
        <div class="flex items-center space-x-3 bg-white/10 backdrop-blur-sm rounded-2xl px-6 py-3 border border-white/20">
          <div class="w-10 h-10 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center">
            <svg class="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M17,10.5V7A1,1 0 0,0 16,6H4A1,1 0 0,0 3,7V17A1,1 0 0,0 4,18H16A1,1 0 0,0 17,17V13.5L21,17.5V6.5L17,10.5Z"/>
              <path d="M5,8V16H15V8H5Z" fill="white" opacity="0.3"/>
              <polygon points="8,10 8,14 12,12" fill="white" opacity="0.8"/>
              <circle cx="19" cy="8" r="1" fill="white" opacity="0.6"/>
            </svg>
          </div>
          <span class="font-bold text-lg">50+ Videos</span>
        </div>
        <div class="flex items-center space-x-3 bg-white/10 backdrop-blur-sm rounded-2xl px-6 py-3 border border-white/20">
          <div class="w-10 h-10 bg-gradient-to-r from-purple-400 to-indigo-500 rounded-xl flex items-center justify-center">
            <svg class="h-5 w-5 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M16.2,16.2L11,13V7H12.5V12.2L17,14.9L16.2,16.2Z"/>
              <circle cx="12" cy="12" r="8" fill="none" stroke="white" stroke-width="0.5" opacity="0.4"/>
              <circle cx="12" cy="12" r="1" fill="white" opacity="0.8"/>
              <path d="M12,6V8M18,12H16M12,18V16M6,12H8" stroke="white" stroke-width="0.5" opacity="0.6"/>
            </svg>
          </div>
          <span class="font-bold text-lg">24/7 Updated</span>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Main Content -->
<div class="bg-gradient-to-br from-slate-50 via-emerald-50 to-teal-50 relative">
  <div class="absolute inset-0 bg-grid-slate-100/50 [mask-image:linear-gradient(0deg,transparent,black)] pointer-events-none"></div>
  
  <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
    
    <!-- Popular Topics -->
    <div class="mb-20">
      <div class="text-center mb-16">
        <h2 class="text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-6">
          Popular Topics
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Quick access to the most searched help topics and frequently needed guides
        </p>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- Getting Started -->
        <div class="group bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-8 hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
          <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-600 rounded-2xl flex items-center justify-center mb-6 shadow-lg shadow-blue-500/25 group-hover:scale-110 transition-transform duration-300">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M11,4H13V16L18.5,10.5L19.92,11.92L12,19.84L4.08,11.92L5.5,10.5L11,16V4Z"/>
              <path d="M7,2V4H17V2H7M7,6V8H17V6H7Z" fill="white" opacity="0.6"/>
              <circle cx="12" cy="12" r="2" fill="white" opacity="0.4"/>
              <path d="M9,14L12,17L15,14" stroke="white" stroke-width="1" fill="none" opacity="0.8"/>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-gray-900 mb-4">Getting Started</h3>
          <p class="text-gray-600 mb-6 leading-relaxed">
            Learn the basics of using Ofie, from account setup to your first rental transaction.
          </p>
          <div class="space-y-3">
            <a href="#" class="block text-emerald-600 hover:text-emerald-700 font-semibold transition-colors duration-200">
              → Creating your account
            </a>
            <a href="#" class="block text-emerald-600 hover:text-emerald-700 font-semibold transition-colors duration-200">
              → Profile setup guide
            </a>
            <a href="#" class="block text-emerald-600 hover:text-emerald-700 font-semibold transition-colors duration-200">
              → Platform overview
            </a>
          </div>
        </div>

        <!-- For Landlords -->
        <div class="group bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-8 hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
          <div class="w-16 h-16 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center mb-6 shadow-lg shadow-emerald-500/25 group-hover:scale-110 transition-transform duration-300">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12,3L2,12H5V20H19V12H22L12,3M12,8.75A1.25,1.25 0 0,1 13.25,10A1.25,1.25 0 0,1 12,11.25A1.25,1.25 0 0,1 10.75,10A1.25,1.25 0 0,1 12,8.75M12,6.5L6,12V18H10V14H14V18H18V12L12,6.5Z"/>
              <rect x="8" y="14" width="2" height="4" fill="white" opacity="0.6"/>
              <rect x="14" y="14" width="2" height="4" fill="white" opacity="0.6"/>
              <rect x="10" y="10" width="4" height="2" fill="white" opacity="0.4"/>
              <circle cx="12" cy="9" r="0.5" fill="white" opacity="0.8"/>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-gray-900 mb-4">For Landlords</h3>
          <p class="text-gray-600 mb-6 leading-relaxed">
            Everything landlords need to know about property management and tenant relations.
          </p>
          <div class="space-y-3">
            <a href="#" class="block text-emerald-600 hover:text-emerald-700 font-semibold transition-colors duration-200">
              → Listing your property
            </a>
            <a href="#" class="block text-emerald-600 hover:text-emerald-700 font-semibold transition-colors duration-200">
              → Tenant screening
            </a>
            <a href="#" class="block text-emerald-600 hover:text-emerald-700 font-semibold transition-colors duration-200">
              → Lease management
            </a>
          </div>
        </div>

        <!-- For Tenants -->
        <div class="group bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-8 hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
          <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-2xl flex items-center justify-center mb-6 shadow-lg shadow-purple-500/25 group-hover:scale-110 transition-transform duration-300">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z"/>
              <circle cx="12" cy="8" r="2.5" fill="white" opacity="0.3"/>
              <path d="M6,18H18V19H6V18Z" fill="white" opacity="0.6"/>
              <circle cx="9" cy="16" r="0.5" fill="white" opacity="0.4"/>
              <circle cx="15" cy="16" r="0.5" fill="white" opacity="0.4"/>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-gray-900 mb-4">For Tenants</h3>
          <p class="text-gray-600 mb-6 leading-relaxed">
            Guides for tenants on finding properties, applications, and rental processes.
          </p>
          <div class="space-y-3">
            <a href="#" class="block text-emerald-600 hover:text-emerald-700 font-semibold transition-colors duration-200">
              → Searching for properties
            </a>
            <a href="#" class="block text-emerald-600 hover:text-emerald-700 font-semibold transition-colors duration-200">
              → Rental applications
            </a>
            <a href="#" class="block text-emerald-600 hover:text-emerald-700 font-semibold transition-colors duration-200">
              → Making payments
            </a>
          </div>
        </div>

        <!-- Payments & Billing -->
        <div class="group bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-8 hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
          <div class="w-16 h-16 bg-gradient-to-r from-amber-500 to-orange-600 rounded-2xl flex items-center justify-center mb-6 shadow-lg shadow-amber-500/25 group-hover:scale-110 transition-transform duration-300">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M20,8H4V6H20M20,18H4V12H20M22,4H2A2,2 0 0,0 0,6V18A2,2 0 0,0 2,20H22A2,2 0 0,0 24,18V6A2,2 0 0,0 22,4Z"/>
              <rect x="2" y="8" width="20" height="2" fill="white" opacity="0.6"/>
              <rect x="4" y="14" width="3" height="1" fill="white" opacity="0.8"/>
              <rect x="8" y="14" width="2" height="1" fill="white" opacity="0.8"/>
              <circle cx="18" cy="15" r="1" fill="white" opacity="0.4"/>
              <circle cx="20" cy="15" r="0.5" fill="white" opacity="0.6"/>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-gray-900 mb-4">Payments & Billing</h3>
          <p class="text-gray-600 mb-6 leading-relaxed">
            Understanding payment processing, fees, and financial management features.
          </p>
          <div class="space-y-3">
            <a href="#" class="block text-emerald-600 hover:text-emerald-700 font-semibold transition-colors duration-200">
              → Payment methods
            </a>
            <a href="#" class="block text-emerald-600 hover:text-emerald-700 font-semibold transition-colors duration-200">
              → Setting up auto-pay
            </a>
            <a href="#" class="block text-emerald-600 hover:text-emerald-700 font-semibold transition-colors duration-200">
              → Understanding fees
            </a>
          </div>
        </div>

        <!-- Technical Support -->
        <div class="group bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-8 hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
          <div class="w-16 h-16 bg-gradient-to-r from-red-500 to-pink-600 rounded-2xl flex items-center justify-center mb-6 shadow-lg shadow-red-500/25 group-hover:scale-110 transition-transform duration-300">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.98C19.47,12.66 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.65 15.48,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.52,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.21,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.52,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.48,18.67 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.98Z"/>
              <circle cx="12" cy="12" r="2" fill="white" opacity="0.6"/>
              <circle cx="12" cy="6" r="0.8" fill="white" opacity="0.4"/>
              <circle cx="12" cy="18" r="0.8" fill="white" opacity="0.4"/>
              <circle cx="6" cy="12" r="0.8" fill="white" opacity="0.4"/>
              <circle cx="18" cy="12" r="0.8" fill="white" opacity="0.4"/>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-gray-900 mb-4">Technical Support</h3>
          <p class="text-gray-600 mb-6 leading-relaxed">
            Troubleshooting common issues and technical questions about the platform.
          </p>
          <div class="space-y-3">
            <a href="#" class="block text-emerald-600 hover:text-emerald-700 font-semibold transition-colors duration-200">
              → Login issues
            </a>
            <a href="#" class="block text-emerald-600 hover:text-emerald-700 font-semibold transition-colors duration-200">
              → Browser compatibility
            </a>
            <a href="#" class="block text-emerald-600 hover:text-emerald-700 font-semibold transition-colors duration-200">
              → Mobile app help
            </a>
          </div>
        </div>

        <!-- Legal & Compliance -->
        <div class="group bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-8 hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
          <div class="w-16 h-16 bg-gradient-to-r from-gray-500 to-slate-600 rounded-2xl flex items-center justify-center mb-6 shadow-lg shadow-gray-500/25 group-hover:scale-110 transition-transform duration-300">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,7C13.4,7 14.8,8.6 14.8,10V11H16V16H8V11H9.2V10C9.2,8.6 10.6,7 12,7M12,8.2C11.2,8.2 10.4,8.7 10.4,10V11H13.6V10C13.6,8.7 12.8,8.2 12,8.2Z"/>
              <rect x="9" y="12" width="6" height="3" fill="white" opacity="0.3"/>
              <circle cx="12" cy="13.5" r="0.8" fill="white" opacity="0.8"/>
              <path d="M12,3L5,6V11C5,15.5 8,19.5 12,21C16,19.5 19,15.5 19,11V6L12,3Z" fill="none" stroke="white" stroke-width="0.5" opacity="0.4"/>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-gray-900 mb-4">Legal & Compliance</h3>
          <p class="text-gray-600 mb-6 leading-relaxed">
            Information about legal requirements, compliance, and platform policies.
          </p>
          <div class="space-y-3">
            <a href="#" class="block text-emerald-600 hover:text-emerald-700 font-semibold transition-colors duration-200">
              → Terms of service
            </a>
            <a href="#" class="block text-emerald-600 hover:text-emerald-700 font-semibold transition-colors duration-200">
              → Privacy policy
            </a>
            <a href="#" class="block text-emerald-600 hover:text-emerald-700 font-semibold transition-colors duration-200">
              → Fair housing compliance
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Featured Guides -->
    <div class="mb-20">
      <div class="text-center mb-16">
        <h2 class="text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-6">
          Featured Guides
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Step-by-step tutorials to help you master the Ofie platform
        </p>
      </div>
      
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Guide 1 -->
        <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 overflow-hidden hover:shadow-2xl transition-all duration-500">
          <div class="relative h-48 bg-gradient-to-br from-blue-100 to-cyan-100">
            <div class="absolute inset-0 flex items-center justify-center">
              <div class="w-24 h-24 bg-gradient-to-r from-blue-500 to-cyan-600 rounded-3xl flex items-center justify-center shadow-2xl shadow-blue-500/25">
                <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M19,13H13V19H11V13H5V11H11V5H13V11H19V13Z"/>
                  <circle cx="12" cy="12" r="8" fill="none" stroke="white" stroke-width="0.5" opacity="0.3"/>
                  <circle cx="12" cy="12" r="1" fill="white" opacity="0.6"/>
                  <path d="M8,8L16,16M16,8L8,16" stroke="white" stroke-width="0.3" opacity="0.2"/>
                </svg>
              </div>
            </div>
            <div class="absolute top-4 right-4">
              <span class="bg-blue-500 text-white px-3 py-1 rounded-xl text-sm font-bold">NEW</span>
            </div>
          </div>
          <div class="p-8">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Complete Landlord Setup Guide</h3>
            <p class="text-gray-600 mb-6 leading-relaxed">
              A comprehensive walkthrough for new landlords, covering everything from account creation 
              to your first successful rental.
            </p>
            <div class="flex items-center justify-between">
              <div class="flex items-center text-sm text-gray-500">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                15 min read
              </div>
              <a href="#" class="bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl">
                Start Guide
              </a>
            </div>
          </div>
        </div>

        <!-- Guide 2 -->
        <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 overflow-hidden hover:shadow-2xl transition-all duration-500">
          <div class="relative h-48 bg-gradient-to-br from-emerald-100 to-teal-100">
            <div class="absolute inset-0 flex items-center justify-center">
              <div class="w-24 h-24 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-3xl flex items-center justify-center shadow-2xl shadow-emerald-500/25">
                <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z"/>
                  <circle cx="9.5" cy="9.5" r="3" fill="white" opacity="0.3"/>
                  <path d="M7,9.5H12M9.5,7V12" stroke="white" stroke-width="0.5" opacity="0.6"/>
                  <circle cx="17" cy="17" r="1.5" fill="white" opacity="0.4"/>
                </svg>
              </div>
            </div>
            <div class="absolute top-4 right-4">
              <span class="bg-emerald-500 text-white px-3 py-1 rounded-xl text-sm font-bold">POPULAR</span>
            </div>
          </div>
          <div class="p-8">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Tenant's Property Search Guide</h3>
            <p class="text-gray-600 mb-6 leading-relaxed">
              Learn how to effectively search for properties, use filters, and submit winning 
              rental applications that get noticed.
            </p>
            <div class="flex items-center justify-between">
              <div class="flex items-center text-sm text-gray-500">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                10 min read
              </div>
              <a href="#" class="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl">
                Start Guide
              </a>
            </div>
          </div>
        </div>

        <!-- Guide 3 -->
        <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 overflow-hidden hover:shadow-2xl transition-all duration-500">
          <div class="relative h-48 bg-gradient-to-br from-purple-100 to-indigo-100">
            <div class="absolute inset-0 flex items-center justify-center">
              <div class="w-24 h-24 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-3xl flex items-center justify-center shadow-2xl shadow-purple-500/25">
                <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M20,4H4C2.89,4 2,4.89 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6C22,4.89 21.1,4 20,4M20,8H4V6H20M20,18H4V12H20M11,14V16H9V14H11M15,14V16H13V14H15Z"/>
                  <rect x="2" y="8" width="20" height="2" fill="white" opacity="0.4"/>
                  <circle cx="18" cy="15" r="1.5" fill="white" opacity="0.6"/>
                  <rect x="4" y="14" width="4" height="1" fill="white" opacity="0.5"/>
                  <path d="M6,6L18,6M6,18L18,18" stroke="white" stroke-width="0.3" opacity="0.3"/>
                </svg>
              </div>
            </div>
          </div>
          <div class="p-8">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Payment & Billing Master Class</h3>
            <p class="text-gray-600 mb-6 leading-relaxed">
              Everything about payments: setting up auto-pay, understanding fees, handling disputes, 
              and maximizing financial efficiency.
            </p>
            <div class="flex items-center justify-between">
              <div class="flex items-center text-sm text-gray-500">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                20 min read
              </div>
              <a href="#" class="bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl">
                Start Guide
              </a>
            </div>
          </div>
        </div>

        <!-- Guide 4 -->
        <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 overflow-hidden hover:shadow-2xl transition-all duration-500">
          <div class="relative h-48 bg-gradient-to-br from-orange-100 to-red-100">
            <div class="absolute inset-0 flex items-center justify-center">
              <div class="w-24 h-24 bg-gradient-to-r from-orange-500 to-red-600 rounded-3xl flex items-center justify-center shadow-2xl shadow-orange-500/25">
                <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M22,21H2V3H4V19H6V17H10V19H12V16H16V19H18V17H22V21M16,8H18V15H16V8M12,2H14V15H12V2M8,13H10V15H8V13M4,15H6V15H4V15Z"/>
                  <rect x="4" y="17" width="2" height="2" fill="white" opacity="0.6"/>
                  <rect x="8" y="13" width="2" height="4" fill="white" opacity="0.5"/>
                  <rect x="12" y="2" width="2" height="15" fill="white" opacity="0.4"/>
                  <rect x="16" y="8" width="2" height="9" fill="white" opacity="0.5"/>
                  <circle cx="5" cy="16" r="0.5" fill="white" opacity="0.8"/>
                  <circle cx="9" cy="12" r="0.5" fill="white" opacity="0.8"/>
                  <circle cx="13" cy="8" r="0.5" fill="white" opacity="0.8"/>
                  <circle cx="17" cy="10" r="0.5" fill="white" opacity="0.8"/>
                </svg>
              </div>
            </div>
          </div>
          <div class="p-8">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Analytics & Reporting Deep Dive</h3>
            <p class="text-gray-600 mb-6 leading-relaxed">
              Unlock the power of data with comprehensive guides on using analytics, generating reports, 
              and making data-driven decisions.
            </p>
            <div class="flex items-center justify-between">
              <div class="flex items-center text-sm text-gray-500">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                25 min read
              </div>
              <a href="#" class="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl">
                Start Guide
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Video Tutorials -->
    <div class="mb-20">
      <div class="text-center mb-16">
        <h2 class="text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-6">
          Video Tutorials
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Watch and learn with our comprehensive video library
        </p>
      </div>
      
      <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-12">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <!-- Featured Video -->
          <div class="lg:col-span-2">
            <div class="relative bg-gradient-to-br from-gray-900 to-gray-700 rounded-2xl overflow-hidden shadow-2xl">
              <div class="aspect-video flex items-center justify-center">
                <button class="group w-20 h-20 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center backdrop-blur-sm transition-all duration-300 transform hover:scale-110">
                  <svg class="w-8 h-8 text-white ml-1 group-hover:scale-110 transition-transform duration-200" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                  </svg>
                </button>
              </div>
              <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-6">
                <h3 class="text-xl font-bold text-white mb-2">Ofie Platform Overview</h3>
                <p class="text-gray-300">Complete walkthrough of all platform features</p>
                <div class="flex items-center mt-3 text-sm text-gray-400">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  12:34
                </div>
              </div>
            </div>
          </div>
          
          <!-- Video List -->
          <div class="space-y-4">
            <h4 class="text-xl font-bold text-gray-900 mb-4">Recently Added</h4>
            
            <div class="flex items-center space-x-4 p-4 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors duration-200 cursor-pointer">
              <div class="relative flex-shrink-0">
                <div class="w-16 h-12 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-lg flex items-center justify-center">
                  <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                  </svg>
                </div>
                <span class="absolute -bottom-1 -right-1 bg-white text-xs font-bold px-2 py-1 rounded text-gray-600">5:23</span>
              </div>
              <div class="flex-1 min-w-0">
                <h5 class="font-semibold text-gray-900 truncate">Property Listing Best Practices</h5>
                <p class="text-sm text-gray-600">How to create listings that attract quality tenants</p>
              </div>
            </div>

            <div class="flex items-center space-x-4 p-4 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors duration-200 cursor-pointer">
              <div class="relative flex-shrink-0">
                <div class="w-16 h-12 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-lg flex items-center justify-center">
                  <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                  </svg>
                </div>
                <span class="absolute -bottom-1 -right-1 bg-white text-xs font-bold px-2 py-1 rounded text-gray-600">8:17</span>
              </div>
              <div class="flex-1 min-w-0">
                <h5 class="font-semibold text-gray-900 truncate">Tenant Screening Process</h5>
                <p class="text-sm text-gray-600">Step-by-step guide to screening applicants</p>
              </div>
            </div>

            <div class="flex items-center space-x-4 p-4 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors duration-200 cursor-pointer">
              <div class="relative flex-shrink-0">
                <div class="w-16 h-12 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg flex items-center justify-center">
                  <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                  </svg>
                </div>
                <span class="absolute -bottom-1 -right-1 bg-white text-xs font-bold px-2 py-1 rounded text-gray-600">6:45</span>
              </div>
              <div class="flex-1 min-w-0">
                <h5 class="font-semibold text-gray-900 truncate">Mobile App Tour</h5>
                <p class="text-sm text-gray-600">Managing your rentals on the go</p>
              </div>
            </div>

            <div class="pt-4">
              <a href="#" class="block text-center bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl">
                View All Videos
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Still Need Help -->
    <div class="text-center">
      <div class="bg-gradient-to-r from-emerald-600 to-teal-600 rounded-3xl shadow-2xl shadow-emerald-500/25 p-12 text-white">
        <div class="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mx-auto mb-6">
          <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12,2A3,3 0 0,1 15,5V11A3,3 0 0,1 12,14A3,3 0 0,1 9,11V5A3,3 0 0,1 12,2M19,11C19,14.53 16.39,17.44 13,17.93V21H11V17.93C7.61,17.44 5,14.53 5,11H7A5,5 0 0,0 12,16A5,5 0 0,0 17,11H19Z"/>
            <circle cx="12" cy="8" r="2" fill="white" opacity="0.3"/>
            <path d="M9,11C9,12.66 10.34,14 12,14C13.66,14 15,12.66 15,11V5C15,3.34 13.66,2 12,2C10.34,2 9,3.34 9,5V11Z" fill="white" opacity="0.2"/>
            <rect x="11" y="18" width="2" height="3" fill="white" opacity="0.6"/>
            <circle cx="7" cy="11" r="0.5" fill="white" opacity="0.5"/>
            <circle cx="17" cy="11" r="0.5" fill="white" opacity="0.5"/>
          </svg>
        </div>
        <h2 class="text-4xl font-bold mb-6">Still Need Help?</h2>
        <p class="text-xl text-emerald-100 mb-8 max-w-3xl mx-auto">
          Can't find what you're looking for? Our support team is here to help with personalized assistance.
        </p>
        <div class="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6">
          <%= link_to "Contact Support", contact_support_path, 
                      class: "bg-white text-emerald-600 hover:bg-emerald-50 px-8 py-4 rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" %>
          <button class="border-2 border-white text-white hover:bg-white hover:text-emerald-600 px-8 py-4 rounded-2xl font-bold transition-all duration-300 transform hover:-translate-y-0.5"
                  data-action="click->chat#open">
            Start Live Chat
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Stimulus Controllers -->
<script>
// Search Controller
class SearchController extends Controller {
  static targets = ["input"]
  
  connect() {
    // Initialize search functionality
    this.setupKeyboardShortcuts()
  }
  
  search(event) {
    const query = event.target.value
    if (query.length > 2) {
      this.performSearch(query)
    }
  }
  
  setupKeyboardShortcuts() {
    document.addEventListener('keydown', (event) => {
      if ((event.metaKey || event.ctrlKey) && event.key === 'k') {
        event.preventDefault()
        this.inputTarget.focus()
      }
    })
  }
  
  performSearch(query) {
    // Implement search functionality
    console.log('Searching for:', query)
    // This would integrate with your search backend
  }
}

// Chat Controller (reused from contact page)
class ChatController extends Controller {
  open() {
    console.log('Opening live chat...')
    // Integration with chat service
  }
}
</script>

<style>
  .bg-grid-slate-100\/50 {
    background-image: url("data:image/svg+xml,%3csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3e%3cg fill='%23f1f5f9' fill-opacity='0.3' fill-rule='evenodd'%3e%3cpath d='m0 40 40-40h-40v40zm40 0v-40h-40z'/%3e%3c/g%3e%3c/svg%3e");
  }
</style>