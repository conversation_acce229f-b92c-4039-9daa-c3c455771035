<% content_for :title, "Contact Ofie - Get Support & Connect With Our Team" %>
<% content_for :description, "Contact Ofie for support, partnerships, or inquiries. Multiple ways to reach our team including live chat, phone, email, and office locations." %>
<% content_for :keywords, "contact ofie, customer support, real estate help, property platform support, ofie contact information" %>

<!-- Hero Section -->
<section class="relative min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-indigo-950 text-white overflow-hidden">
  <!-- Advanced Background Layers -->
  <div class="absolute inset-0">
    <div class="absolute inset-0 bg-gradient-to-r from-slate-900/90 via-indigo-900/80 to-slate-900/90"></div>
    <div class="absolute inset-0 opacity-30">
      <div class="absolute inset-0 bg-gradient-to-r from-blue-600/20 via-purple-600/30 to-pink-600/20 animate-pulse"></div>
      <div class="absolute inset-0" style="background: radial-gradient(circle at 20% 30%, rgba(59, 130, 246, 0.15) 0%, transparent 40%), radial-gradient(circle at 80% 70%, rgba(147, 51, 234, 0.15) 0%, transparent 40%), radial-gradient(circle at 40% 80%, rgba(236, 72, 153, 0.10) 0%, transparent 40%);"></div>
    </div>
  </div>
  
  <!-- Floating Glass Elements -->
  <div class="absolute inset-0 overflow-hidden pointer-events-none">
    <div class="absolute top-1/4 left-1/6 w-72 h-72 bg-gradient-to-br from-blue-400/8 to-cyan-400/8 rounded-full blur-3xl animate-float"></div>
    <div class="absolute bottom-1/3 right-1/6 w-96 h-96 bg-gradient-to-br from-purple-400/8 to-pink-400/8 rounded-full blur-3xl animate-float-delayed"></div>
  </div>
  
  <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-32 flex items-center min-h-screen">
    <div class="w-full">
      <!-- Support Badge -->
      <div class="text-center mb-12">
        <div class="inline-flex items-center space-x-3 bg-white/5 backdrop-blur-md rounded-full px-8 py-4 mb-8 border border-white/10">
          <div class="w-12 h-12 bg-gradient-to-r from-emerald-400 to-cyan-500 rounded-full flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
            </svg>
          </div>
          <div class="text-left">
            <div class="text-sm font-medium text-white/90">24/7 Support</div>
            <div class="text-xs text-white/60">Average response: < 2 hours</div>
          </div>
        </div>
      </div>
      
      <!-- Hero Title -->
      <div class="text-center mb-16">
        <h1 class="text-6xl md:text-8xl font-light mb-8 leading-[0.9] tracking-tight">
          <span class="block font-extralight text-white/95">We're Here</span>
          <span class="block text-transparent bg-clip-text bg-gradient-to-r from-cyan-300 via-blue-300 to-indigo-300 font-medium animate-gradient">
            To Help
          </span>
          <span class="block text-2xl md:text-3xl mt-6 text-white/70 font-light tracking-wide">
            Get Support When You Need It
          </span>
        </h1>
        
        <p class="text-lg md:text-xl mb-12 text-white/70 max-w-4xl mx-auto leading-relaxed font-light">
          Our dedicated support team is available around the clock to help you with any questions, technical issues, or guidance you need. Choose the contact method that works best for you.
        </p>
        
        <!-- Quick Contact Options -->
        <div class="flex flex-wrap justify-center items-center gap-8 mb-16">
          <button class="group flex items-center space-x-3 bg-white/10 backdrop-blur-md rounded-2xl px-6 py-4 border border-white/20 hover:bg-white/20 transition-all duration-300"
                  data-action="click->contact#startLiveChat">
            <div class="w-12 h-12 bg-gradient-to-r from-emerald-400 to-cyan-400 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div class="text-left">
              <div class="text-sm font-semibold text-white">Live Chat</div>
              <div class="text-xs text-white/60">Available now</div>
            </div>
          </button>
          
          <a href="tel:******-0123" class="group flex items-center space-x-3 bg-white/10 backdrop-blur-md rounded-2xl px-6 py-4 border border-white/20 hover:bg-white/20 transition-all duration-300">
            <div class="w-12 h-12 bg-gradient-to-r from-blue-400 to-indigo-400 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
              </svg>
            </div>
            <div class="text-left">
              <div class="text-sm font-semibold text-white">Call Now</div>
              <div class="text-xs text-white/60">+****************</div>
            </div>
          </a>
          
          <a href="mailto:<EMAIL>" class="group flex items-center space-x-3 bg-white/10 backdrop-blur-md rounded-2xl px-6 py-4 border border-white/20 hover:bg-white/20 transition-all duration-300">
            <div class="w-12 h-12 bg-gradient-to-r from-purple-400 to-pink-400 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
              </svg>
            </div>
            <div class="text-left">
              <div class="text-sm font-semibold text-white">Email Us</div>
              <div class="text-xs text-white/60"><EMAIL></div>
            </div>
          </a>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Main Contact Section -->
<section class="py-24 bg-gradient-to-br from-slate-50 via-white to-blue-50/30 relative overflow-hidden">
  <div class="absolute inset-0 opacity-[0.02]">
    <div class="absolute inset-0" style="background-image: radial-gradient(circle at 25% 25%, #3b82f6 2px, transparent 2px), radial-gradient(circle at 75% 75%, #8b5cf6 2px, transparent 2px); background-size: 60px 60px;"></div>
  </div>
  
  <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-16">
      <!-- Contact Form -->
      <div>
        <div class="bg-white/80 backdrop-blur-sm rounded-3xl p-8 lg:p-10 shadow-xl border border-slate-100/50">
          <div class="mb-8">
            <div class="inline-flex items-center space-x-2 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-full px-6 py-2 mb-6 border border-blue-100">
              <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
              </svg>
              <span class="text-sm font-medium text-blue-800">Send us a message</span>
            </div>
            <h2 class="text-3xl lg:text-4xl font-light text-slate-900 mb-4 tracking-tight">
              Get in 
              <span class="font-medium text-transparent bg-clip-text bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600">
                Touch
              </span>
            </h2>
            <p class="text-lg text-slate-600 font-light leading-relaxed">
              Fill out the form below and we'll get back to you within 24 hours. For urgent matters, please use our live chat or phone support.
            </p>
          </div>

          <%= form_with url: contact_path, method: :post, local: true, class: "space-y-6", data: { controller: "contact-form" } do |form| %>
            <!-- Name and Email Row -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <%= form.label :name, "Full Name", class: "block text-sm font-medium text-slate-700 mb-2" %>
                <%= form.text_field :name, 
                    placeholder: "Your full name",
                    required: true,
                    class: "w-full px-4 py-3 rounded-xl border border-slate-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 hover:border-slate-300" %>
              </div>
              <div>
                <%= form.label :email, "Email Address", class: "block text-sm font-medium text-slate-700 mb-2" %>
                <%= form.email_field :email, 
                    placeholder: "<EMAIL>",
                    required: true,
                    class: "w-full px-4 py-3 rounded-xl border border-slate-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 hover:border-slate-300" %>
              </div>
            </div>

            <!-- Phone and Subject Row -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <%= form.label :phone, "Phone Number", class: "block text-sm font-medium text-slate-700 mb-2" %>
                <%= form.telephone_field :phone,
                    placeholder: "+****************",
                    class: "w-full px-4 py-3 rounded-xl border border-slate-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 hover:border-slate-300" %>
              </div>
              <div>
                <%= form.label :inquiry_type, "Inquiry Type", class: "block text-sm font-medium text-slate-700 mb-2" %>
                <%= form.select :inquiry_type,
                    options_for_select([
                      ['General Support', 'general'],
                      ['Technical Issue', 'technical'],
                      ['Billing Question', 'billing'],
                      ['Property Listing', 'listing'],
                      ['Partnership', 'partnership'],
                      ['Press/Media', 'press'],
                      ['Other', 'other']
                    ]),
                    { prompt: 'Select inquiry type' },
                    { class: "w-full px-4 py-3 rounded-xl border border-slate-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 hover:border-slate-300" } %>
              </div>
            </div>

            <!-- Subject -->
            <div>
              <%= form.label :subject, "Subject", class: "block text-sm font-medium text-slate-700 mb-2" %>
              <%= form.text_field :subject, 
                  placeholder: "Brief description of your inquiry",
                  required: true,
                  class: "w-full px-4 py-3 rounded-xl border border-slate-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 hover:border-slate-300" %>
            </div>

            <!-- Message -->
            <div>
              <%= form.label :message, "Message", class: "block text-sm font-medium text-slate-700 mb-2" %>
              <%= form.text_area :message, 
                  placeholder: "Please provide details about your inquiry...",
                  required: true,
                  rows: 5,
                  class: "w-full px-4 py-3 rounded-xl border border-slate-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 hover:border-slate-300 resize-none" %>
            </div>

            <!-- Priority Level -->
            <div>
              <label class="block text-sm font-medium text-slate-700 mb-3">Priority Level</label>
              <div class="flex space-x-4">
                <label class="flex items-center">
                  <%= form.radio_button :priority, "low", class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300" %>
                  <span class="ml-2 text-sm text-slate-600">Low</span>
                </label>
                <label class="flex items-center">
                  <%= form.radio_button :priority, "medium", checked: true, class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300" %>
                  <span class="ml-2 text-sm text-slate-600">Medium</span>
                </label>
                <label class="flex items-center">
                  <%= form.radio_button :priority, "high", class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300" %>
                  <span class="ml-2 text-sm text-slate-600">High</span>
                </label>
                <label class="flex items-center">
                  <%= form.radio_button :priority, "urgent", class: "h-4 w-4 text-red-600 focus:ring-red-500 border-slate-300" %>
                  <span class="ml-2 text-sm text-red-600">Urgent</span>
                </label>
              </div>
            </div>

            <!-- Preferred Contact Method -->
            <div>
              <label class="block text-sm font-medium text-slate-700 mb-3">Preferred Response Method</label>
              <div class="flex flex-wrap gap-4">
                <label class="flex items-center">
                  <%= form.check_box :contact_email, class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded" %>
                  <span class="ml-2 text-sm text-slate-600">Email</span>
                </label>
                <label class="flex items-center">
                  <%= form.check_box :contact_phone, class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded" %>
                  <span class="ml-2 text-sm text-slate-600">Phone</span>
                </label>
                <label class="flex items-center">
                  <%= form.check_box :contact_sms, class: "h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-300 rounded" %>
                  <span class="ml-2 text-sm text-slate-600">SMS</span>
                </label>
              </div>
            </div>

            <!-- Submit Button -->
            <div class="pt-4">
              <%= form.submit "Send Message", 
                  class: "w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold py-4 px-8 rounded-xl transition-all duration-300 transform hover:scale-[1.02] shadow-lg hover:shadow-xl",
                  data: { action: "click->contact-form#submit" } %>
              <p class="text-sm text-slate-500 mt-3 text-center">
                We typically respond within 2-4 hours during business hours
              </p>
            </div>
          <% end %>
        </div>
      </div>

      <!-- Contact Information & Support Options -->
      <div class="space-y-8">
        <!-- Contact Methods -->
        <div class="bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-lg border border-slate-100/50">
          <h3 class="text-2xl font-semibold text-slate-900 mb-6">Multiple Ways to Connect</h3>
          
          <div class="space-y-6">
            <!-- Live Chat -->
            <div class="flex items-start space-x-4 p-4 bg-emerald-50 rounded-2xl border border-emerald-100">
              <div class="w-12 h-12 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center flex-shrink-0">
                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clip-rule="evenodd"></path>
                </svg>
              </div>
              <div class="flex-1">
                <h4 class="font-semibold text-slate-900 mb-1">Live Chat Support</h4>
                <p class="text-slate-600 text-sm mb-3">Get instant help from our support team</p>
                <div class="flex items-center space-x-4">
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-emerald-100 text-emerald-800">
                    <div class="w-2 h-2 bg-emerald-400 rounded-full mr-2 animate-pulse"></div>
                    Available Now
                  </span>
                  <button class="text-emerald-600 hover:text-emerald-700 text-sm font-medium" data-action="click->contact#startLiveChat">
                    Start Chat →
                  </button>
                </div>
              </div>
            </div>

            <!-- Phone Support -->
            <div class="flex items-start space-x-4 p-4 bg-blue-50 rounded-2xl border border-blue-100">
              <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center flex-shrink-0">
                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"></path>
                </svg>
              </div>
              <div class="flex-1">
                <h4 class="font-semibold text-slate-900 mb-1">Phone Support</h4>
                <p class="text-slate-600 text-sm mb-3">Speak directly with our expert team</p>
                <div class="space-y-2">
                  <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-slate-700">General Support:</span>
                    <a href="tel:******-0123" class="text-blue-600 hover:text-blue-700 font-medium">+****************</a>
                  </div>
                  <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-slate-700">Technical Issues:</span>
                    <a href="tel:******-0124" class="text-blue-600 hover:text-blue-700 font-medium">+****************</a>
                  </div>
                  <div class="text-xs text-slate-500 mt-2">
                    24/7 availability • Average wait time: < 2 minutes
                  </div>
                </div>
              </div>
            </div>

            <!-- Email Support -->
            <div class="flex items-start space-x-4 p-4 bg-purple-50 rounded-2xl border border-purple-100">
              <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-600 rounded-xl flex items-center justify-center flex-shrink-0">
                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                  <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                </svg>
              </div>
              <div class="flex-1">
                <h4 class="font-semibold text-slate-900 mb-1">Email Support</h4>
                <p class="text-slate-600 text-sm mb-3">Detailed assistance for complex issues</p>
                <div class="space-y-2">
                  <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-slate-700">General:</span>
                    <a href="mailto:<EMAIL>" class="text-purple-600 hover:text-purple-700 font-medium"><EMAIL></a>
                  </div>
                  <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-slate-700">Technical:</span>
                    <a href="mailto:<EMAIL>" class="text-purple-600 hover:text-purple-700 font-medium"><EMAIL></a>
                  </div>
                  <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-slate-700">Business:</span>
                    <a href="mailto:<EMAIL>" class="text-purple-600 hover:text-purple-700 font-medium"><EMAIL></a>
                  </div>
                  <div class="text-xs text-slate-500 mt-2">
                    Response time: < 4 hours during business hours
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Office Locations -->
        <div class="bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-lg border border-slate-100/50">
          <div class="flex items-center space-x-3 mb-6">
            <div class="w-10 h-10 bg-gradient-to-r from-orange-500 to-red-600 rounded-xl flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <h3 class="text-2xl font-semibold text-slate-900">Our Locations</h3>
          </div>
          
          <div class="space-y-6">
            <!-- Headquarters -->
            <div class="border-l-4 border-blue-500 pl-4">
              <h4 class="font-semibold text-slate-900">San Francisco HQ</h4>
              <p class="text-slate-600 text-sm mt-1">
                123 Market Street, Suite 500<br>
                San Francisco, CA 94103<br>
                United States
              </p>
              <div class="mt-2 flex items-center space-x-4">
                <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">Headquarters</span>
                <a href="#" class="text-blue-600 hover:text-blue-700 text-sm font-medium">Get Directions →</a>
              </div>
            </div>

            <!-- Regional Offices -->
            <div class="border-l-4 border-emerald-500 pl-4">
              <h4 class="font-semibold text-slate-900">New York Office</h4>
              <p class="text-slate-600 text-sm mt-1">
                456 Broadway, Floor 12<br>
                New York, NY 10013<br>
                United States
              </p>
              <div class="mt-2 flex items-center space-x-4">
                <span class="text-xs bg-emerald-100 text-emerald-800 px-2 py-1 rounded-full">Regional</span>
                <a href="#" class="text-emerald-600 hover:text-emerald-700 text-sm font-medium">Get Directions →</a>
              </div>
            </div>

            <div class="border-l-4 border-purple-500 pl-4">
              <h4 class="font-semibold text-slate-900">Austin Office</h4>
              <p class="text-slate-600 text-sm mt-1">
                789 Congress Avenue, Suite 200<br>
                Austin, TX 78701<br>
                United States
              </p>
              <div class="mt-2 flex items-center space-x-4">
                <span class="text-xs bg-purple-100 text-purple-800 px-2 py-1 rounded-full">Regional</span>
                <a href="#" class="text-purple-600 hover:text-purple-700 text-sm font-medium">Get Directions →</a>
              </div>
            </div>
          </div>
        </div>

        <!-- Support Hours -->
        <div class="bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-lg border border-slate-100/50">
          <div class="flex items-center space-x-3 mb-6">
            <div class="w-10 h-10 bg-gradient-to-r from-amber-500 to-orange-600 rounded-xl flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <h3 class="text-2xl font-semibold text-slate-900">Support Hours</h3>
          </div>
          
          <div class="space-y-4">
            <div class="flex justify-between items-center py-2 border-b border-slate-100">
              <span class="font-medium text-slate-700">Live Chat</span>
              <span class="text-emerald-600 font-semibold">24/7</span>
            </div>
            <div class="flex justify-between items-center py-2 border-b border-slate-100">
              <span class="font-medium text-slate-700">Phone Support</span>
              <span class="text-blue-600 font-semibold">24/7</span>
            </div>
            <div class="flex justify-between items-center py-2 border-b border-slate-100">
              <span class="font-medium text-slate-700">Email Response</span>
              <span class="text-purple-600 font-semibold">Within 4 hours</span>
            </div>
            <div class="flex justify-between items-center py-2">
              <span class="font-medium text-slate-700">Office Visits</span>
              <span class="text-orange-600 font-semibold">Mon-Fri 9AM-6PM</span>
            </div>
          </div>
          
          <div class="mt-6 p-4 bg-gradient-to-r from-amber-50 to-orange-50 rounded-xl border border-amber-200">
            <div class="flex items-center space-x-2">
              <svg class="w-5 h-5 text-amber-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
              </svg>
              <span class="text-sm font-medium text-amber-800">All times in PST/PDT</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- FAQ Section -->
<section class="py-24 bg-gradient-to-br from-slate-900 via-slate-800 to-indigo-900 relative overflow-hidden">
  <div class="absolute inset-0">
    <div class="absolute inset-0 bg-gradient-to-r from-blue-900/20 via-purple-900/20 to-indigo-900/20"></div>
    <div class="absolute top-1/4 left-1/6 w-96 h-96 bg-gradient-to-br from-blue-400/5 to-cyan-400/5 rounded-full blur-3xl"></div>
    <div class="absolute bottom-1/4 right-1/6 w-80 h-80 bg-gradient-to-br from-purple-400/5 to-pink-400/5 rounded-full blur-3xl"></div>
  </div>
  
  <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-20">
      <div class="inline-flex items-center space-x-2 bg-white/5 backdrop-blur-md rounded-full px-6 py-3 mb-8 border border-white/10">
        <svg class="w-5 h-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path>
        </svg>
        <span class="text-sm font-medium text-white/90">Frequently Asked Questions</span>
      </div>
      
      <h2 class="text-5xl lg:text-6xl font-light text-white mb-8 tracking-tight">
        Quick
        <span class="block text-transparent bg-clip-text bg-gradient-to-r from-cyan-300 via-blue-300 to-indigo-300 font-medium">
          Answers
        </span>
      </h2>
      <p class="text-xl text-white/70 max-w-3xl mx-auto font-light leading-relaxed">
        Find answers to common questions before reaching out to our support team
      </p>
    </div>
    
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- FAQ Item 1 -->
      <div class="bg-white/[0.08] backdrop-blur-xl rounded-3xl p-8 border border-white/10">
        <h3 class="text-xl font-semibold text-white mb-4">How quickly do you respond to support requests?</h3>
        <p class="text-white/70 leading-relaxed">
          We aim to respond to all inquiries within 2-4 hours during business hours. For urgent issues, our live chat and phone support are available 24/7 with typical response times under 2 minutes.
        </p>
      </div>

      <!-- FAQ Item 2 -->
      <div class="bg-white/[0.08] backdrop-blur-xl rounded-3xl p-8 border border-white/10">
        <h3 class="text-xl font-semibold text-white mb-4">What information should I include in my support request?</h3>
        <p class="text-white/70 leading-relaxed">
          Please include your account details, a detailed description of the issue, any error messages, and steps you've already tried. Screenshots or screen recordings are also helpful for technical issues.
        </p>
      </div>

      <!-- FAQ Item 3 -->
      <div class="bg-white/[0.08] backdrop-blur-xl rounded-3xl p-8 border border-white/10">
        <h3 class="text-xl font-semibold text-white mb-4">Can I schedule a call with your team?</h3>
        <p class="text-white/70 leading-relaxed">
          Yes! For complex issues or business inquiries, we offer scheduled calls. Contact us through any channel to arrange a convenient time with one of our specialists.
        </p>
      </div>

      <!-- FAQ Item 4 -->
      <div class="bg-white/[0.08] backdrop-blur-xl rounded-3xl p-8 border border-white/10">
        <h3 class="text-xl font-semibold text-white mb-4">Do you offer phone support in multiple languages?</h3>
        <p class="text-white/70 leading-relaxed">
          Currently, our phone support is available in English and Spanish. For other languages, we recommend using our chat support which offers real-time translation capabilities.
        </p>
      </div>
    </div>

    <!-- Still Need Help CTA -->
    <div class="mt-20 text-center">
      <div class="bg-gradient-to-r from-emerald-600 to-teal-600 rounded-3xl shadow-2xl shadow-emerald-500/25 p-12 text-white">
        <div class="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mx-auto mb-6">
          <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z"></path>
          </svg>
        </div>
        <h2 class="text-4xl font-bold mb-6">Still Need Help?</h2>
        <p class="text-xl text-emerald-100 mb-8 max-w-3xl mx-auto">
          Our support team is standing by to provide personalized assistance for your specific needs
        </p>
        <div class="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6">
          <button class="bg-white text-emerald-600 hover:bg-emerald-50 px-8 py-4 rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                  data-action="click->contact#startLiveChat">
            Start Live Chat
          </button>
          <a href="tel:******-0123" class="border-2 border-white text-white hover:bg-white hover:text-emerald-600 px-8 py-4 rounded-2xl font-bold transition-all duration-300 transform hover:-translate-y-0.5">
            Call Now: +****************
          </a>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Enhanced CSS for animations -->
<style>
  @keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
  }
  
  @keyframes float-delayed {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-15px) rotate(-180deg); }
  }
  
  @keyframes gradient {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }
  
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }
  
  .animate-float-delayed {
    animation: float-delayed 8s ease-in-out infinite;
  }
  
  .animate-gradient {
    background-size: 200% 200%;
    animation: gradient 3s ease infinite;
  }
  
  /* Backdrop blur fallback */
  @supports not (backdrop-filter: blur()) {
    .backdrop-blur-xl, .backdrop-blur-md, .backdrop-blur-sm {
      background-color: rgba(255, 255, 255, 0.1);
    }
  }
</style>

<!-- Stimulus Controllers -->
<script>
// Contact Controller
class ContactController extends Controller {
  startLiveChat() {
    // Integration with live chat service (e.g., Intercom, Zendesk Chat, etc.)
    console.log('Starting live chat...')
    // Example integration:
    // if (window.Intercom) {
    //   window.Intercom('show')
    // }
  }
}

// Contact Form Controller
class ContactFormController extends Controller {
  submit(event) {
    event.preventDefault()
    
    // Add form validation and submission logic
    const form = event.target.closest('form')
    const formData = new FormData(form)
    
    // Show loading state
    event.target.disabled = true
    event.target.textContent = 'Sending...'
    
    // Submit form (implement actual submission logic)
    setTimeout(() => {
      event.target.disabled = false
      event.target.textContent = 'Message Sent!'
      
      // Reset after success
      setTimeout(() => {
        event.target.textContent = 'Send Message'
        form.reset()
      }, 2000)
    }, 1500)
  }
}
</script>
