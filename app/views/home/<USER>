<% content_for :title, "About Ofie - Leading Real Estate Platform | Our Story & Mission" %>
<% content_for :description, "Learn about <PERSON><PERSON>'s mission to revolutionize real estate through technology. Discover our story, values, and commitment to connecting homeowners with their perfect properties." %>
<% content_for :keywords, "about ofie, real estate platform, company mission, property technology, rental platform story" %>

<!-- Hero Section -->
<section class="relative min-h-screen bg-gradient-to-br from-slate-950 via-slate-900 to-indigo-950 text-white overflow-hidden">
  <!-- Advanced Background Layers -->
  <div class="absolute inset-0">
    <div class="absolute inset-0 bg-gradient-to-r from-slate-900/90 via-indigo-900/80 to-slate-900/90"></div>
    <div class="absolute inset-0 opacity-30">
      <div class="absolute inset-0 bg-gradient-to-r from-blue-600/20 via-purple-600/30 to-pink-600/20 animate-pulse"></div>
      <div class="absolute inset-0" style="background: radial-gradient(circle at 20% 30%, rgba(59, 130, 246, 0.15) 0%, transparent 40%), radial-gradient(circle at 80% 70%, rgba(147, 51, 234, 0.15) 0%, transparent 40%), radial-gradient(circle at 40% 80%, rgba(236, 72, 153, 0.10) 0%, transparent 40%);"></div>
    </div>
  </div>
  
  <!-- Floating Glass Elements -->
  <div class="absolute inset-0 overflow-hidden pointer-events-none">
    <div class="absolute top-1/4 left-1/6 w-72 h-72 bg-gradient-to-br from-blue-400/8 to-cyan-400/8 rounded-full blur-3xl animate-float"></div>
    <div class="absolute bottom-1/3 right-1/6 w-96 h-96 bg-gradient-to-br from-purple-400/8 to-pink-400/8 rounded-full blur-3xl animate-float-delayed"></div>
  </div>
  
  <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-32 flex items-center min-h-screen">
    <div class="w-full">
      <!-- Company Badge -->
      <div class="text-center mb-12">
        <div class="inline-flex items-center space-x-3 bg-white/5 backdrop-blur-md rounded-full px-8 py-4 mb-8 border border-white/10">
          <div class="w-12 h-12 bg-gradient-to-r from-emerald-400 to-cyan-500 rounded-full flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
            </svg>
          </div>
          <div class="text-left">
            <div class="text-sm font-medium text-white/90">Founded 2020</div>
            <div class="text-xs text-white/60">Trusted by 100K+ families</div>
          </div>
        </div>
      </div>
      
      <!-- Hero Title -->
      <div class="text-center mb-16">
        <h1 class="text-6xl md:text-8xl font-light mb-8 leading-[0.9] tracking-tight">
          <span class="block font-extralight text-white/95">Revolutionizing</span>
          <span class="block text-transparent bg-clip-text bg-gradient-to-r from-cyan-300 via-blue-300 to-indigo-300 font-medium animate-gradient">
            Real Estate
          </span>
          <span class="block text-2xl md:text-3xl mt-6 text-white/70 font-light tracking-wide">
            Through Technology & Trust
          </span>
        </h1>
        
        <p class="text-lg md:text-xl mb-12 text-white/70 max-w-4xl mx-auto leading-relaxed font-light">
          At Ofie, we believe finding the perfect home shouldn't be complicated. Our mission is to create seamless connections between property seekers and their ideal living spaces through innovative technology and unwavering commitment to excellence.
        </p>
        
        <!-- Mission Stats -->
        <div class="flex flex-wrap justify-center items-center gap-8 mb-16">
          <div class="flex items-center space-x-3">
            <div class="w-12 h-12 bg-gradient-to-r from-emerald-400 to-cyan-400 rounded-2xl flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="text-left">
              <div class="text-lg font-semibold text-white">100K+ Families</div>
              <div class="text-sm text-white/60">Successfully housed</div>
            </div>
          </div>
          <div class="flex items-center space-x-3">
            <div class="w-12 h-12 bg-gradient-to-r from-blue-400 to-indigo-400 rounded-2xl flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
              </svg>
            </div>
            <div class="text-left">
              <div class="text-lg font-semibold text-white">500K+ Properties</div>
              <div class="text-sm text-white/60">Verified listings</div>
            </div>
          </div>
          <div class="flex items-center space-x-3">
            <div class="w-12 h-12 bg-gradient-to-r from-purple-400 to-pink-400 rounded-2xl flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div class="text-left">
              <div class="text-lg font-semibold text-white">350+ Cities</div>
              <div class="text-sm text-white/60">Nationwide coverage</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Our Story Section -->
<section class="py-24 bg-gradient-to-br from-slate-50 via-white to-blue-50/30 relative overflow-hidden">
  <div class="absolute inset-0 opacity-[0.02]">
    <div class="absolute inset-0" style="background-image: radial-gradient(circle at 25% 25%, #3b82f6 2px, transparent 2px), radial-gradient(circle at 75% 75%, #8b5cf6 2px, transparent 2px); background-size: 60px 60px;"></div>
  </div>
  
  <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Section Header -->
    <div class="text-center mb-20">
      <div class="inline-flex items-center space-x-2 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-full px-6 py-2 mb-8 border border-blue-100">
        <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
          <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <span class="text-sm font-medium text-blue-800">Our Journey</span>
      </div>
      <h2 class="text-4xl lg:text-6xl font-light text-slate-900 mb-6 tracking-tight">
        The 
        <span class="font-medium text-transparent bg-clip-text bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600">
          Ofie Story
        </span>
      </h2>
      <p class="text-xl text-slate-600 max-w-3xl mx-auto font-light leading-relaxed">
        Born from a vision to transform how people find and connect with their perfect homes
      </p>
    </div>

    <!-- Story Timeline -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-20">
      <!-- Story Content -->
      <div class="space-y-8">
        <div class="bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-lg border border-slate-100/50">
          <div class="flex items-start space-x-4">
            <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center flex-shrink-0">
              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
              </svg>
            </div>
            <div>
              <h3 class="text-xl font-semibold text-slate-900 mb-3">The Beginning (2020)</h3>
              <p class="text-slate-600 leading-relaxed">
                Founded by a team of technology veterans and real estate professionals who experienced firsthand the frustrations of traditional property search. We recognized that the industry needed a platform that truly understood both sides of the equation.
              </p>
            </div>
          </div>
        </div>

        <div class="bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-lg border border-slate-100/50">
          <div class="flex items-start space-x-4">
            <div class="w-12 h-12 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-2xl flex items-center justify-center flex-shrink-0">
              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div>
              <h3 class="text-xl font-semibold text-slate-900 mb-3">Rapid Growth (2021-2022)</h3>
              <p class="text-slate-600 leading-relaxed">
                Our user-centric approach resonated immediately. Within two years, we facilitated over 50,000 successful property connections and expanded to 200+ cities. Our AI-powered matching system revolutionized how people discover their ideal homes.
              </p>
            </div>
          </div>
        </div>

        <div class="bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-lg border border-slate-100/50">
          <div class="flex items-start space-x-4">
            <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-2xl flex items-center justify-center flex-shrink-0">
              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
              </svg>
            </div>
            <div>
              <h3 class="text-xl font-semibold text-slate-900 mb-3">Innovation & Scale (2023-Present)</h3>
              <p class="text-slate-600 leading-relaxed">
                Today, we're proud to serve over 100,000 families with cutting-edge features like virtual tours, AI-driven recommendations, and comprehensive tenant screening. Our platform continues to evolve, always putting user experience first.
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Visual Element with Office Activities -->
      <div class="relative space-y-8">
        <!-- Main Metrics Card -->
        <div class="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-3xl p-8 shadow-2xl">
          <div class="text-center mb-8">
            <div class="w-24 h-24 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-xl shadow-blue-500/25">
              <svg class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
              </svg>
            </div>
            <h3 class="text-2xl font-bold text-slate-900 mb-4">Mission-Driven Growth</h3>
            <p class="text-slate-600 mb-6">Every milestone represents thousands of families finding their perfect home</p>
            
            <!-- Growth Metrics -->
            <div class="grid grid-cols-2 gap-6">
              <div class="text-center">
                <div class="text-3xl font-bold text-blue-600 mb-2">4.9★</div>
                <div class="text-sm text-slate-600">User Rating</div>
              </div>
              <div class="text-center">
                <div class="text-3xl font-bold text-emerald-600 mb-2">99.5%</div>
                <div class="text-sm text-slate-600">Success Rate</div>
              </div>
              <div class="text-center">
                <div class="text-3xl font-bold text-purple-600 mb-2">24/7</div>
                <div class="text-sm text-slate-600">Support</div>
              </div>
              <div class="text-center">
                <div class="text-3xl font-bold text-orange-600 mb-2">< 48hr</div>
                <div class="text-sm text-slate-600">Response Time</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Office Activities Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Team Collaboration -->
          <div class="group bg-white/90 backdrop-blur-sm rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-500 border border-slate-100/50">
            <div class="relative h-48 overflow-hidden">
              <img src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80" 
                   alt="Team collaboration meeting" 
                   class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                   loading="lazy">
              <div class="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent"></div>
              <div class="absolute bottom-4 left-4 right-4">
                <div class="bg-white/95 backdrop-blur-sm rounded-xl p-3 shadow-lg">
                  <h4 class="font-semibold text-slate-900 text-sm mb-1">Daily Standups</h4>
                  <p class="text-slate-600 text-xs">Cross-functional collaboration drives innovation</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Innovation Workshop -->
          <div class="group bg-white/90 backdrop-blur-sm rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-500 border border-slate-100/50">
            <div class="relative h-48 overflow-hidden">
              <img src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80" 
                   alt="Innovation workshop with whiteboards" 
                   class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                   loading="lazy">
              <div class="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent"></div>
              <div class="absolute bottom-4 left-4 right-4">
                <div class="bg-white/95 backdrop-blur-sm rounded-xl p-3 shadow-lg">
                  <h4 class="font-semibold text-slate-900 text-sm mb-1">Innovation Sessions</h4>
                  <p class="text-slate-600 text-xs">Weekly brainstorming drives product evolution</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Remote Collaboration -->
          <div class="group bg-white/90 backdrop-blur-sm rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-500 border border-slate-100/50">
            <div class="relative h-48 overflow-hidden">
              <img src="https://images.unsplash.com/photo-1587825140708-dfaf72ae4b04?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80" 
                   alt="Remote video conference meeting" 
                   class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                   loading="lazy">
              <div class="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent"></div>
              <div class="absolute bottom-4 left-4 right-4">
                <div class="bg-white/95 backdrop-blur-sm rounded-xl p-3 shadow-lg">
                  <h4 class="font-semibold text-slate-900 text-sm mb-1">Global Team Sync</h4>
                  <p class="text-slate-600 text-xs">Connecting talent across time zones seamlessly</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Team Building -->
          <div class="group bg-white/90 backdrop-blur-sm rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-500 border border-slate-100/50">
            <div class="relative h-48 overflow-hidden">
              <img src="https://images.unsplash.com/photo-1511632765486-a01980e01a18?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80" 
                   alt="Team celebration and bonding" 
                   class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                   loading="lazy">
              <div class="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent"></div>
              <div class="absolute bottom-4 left-4 right-4">
                <div class="bg-white/95 backdrop-blur-sm rounded-xl p-3 shadow-lg">
                  <h4 class="font-semibold text-slate-900 text-sm mb-1">Team Celebrations</h4>
                  <p class="text-slate-600 text-xs">Recognizing achievements and building culture</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Office Culture Highlights -->
        <div class="bg-gradient-to-r from-emerald-50 via-blue-50 to-purple-50 rounded-2xl p-6 border border-slate-200/50">
          <div class="text-center mb-6">
            <h4 class="text-lg font-bold text-slate-900 mb-2">Our Work Culture</h4>
            <p class="text-slate-600 text-sm">Where innovation meets collaboration</p>
          </div>
          
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <!-- Culture Stat 1 -->
            <div class="text-center p-4 bg-white/70 backdrop-blur-sm rounded-xl border border-white/50">
              <div class="w-10 h-10 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-lg flex items-center justify-center mx-auto mb-3">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <div class="text-lg font-bold text-emerald-600 mb-1">98%</div>
              <div class="text-xs text-slate-600">Employee Satisfaction</div>
            </div>

            <!-- Culture Stat 2 -->
            <div class="text-center p-4 bg-white/70 backdrop-blur-sm rounded-xl border border-white/50">
              <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center mx-auto mb-3">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                </svg>
              </div>
              <div class="text-lg font-bold text-blue-600 mb-1">85</div>
              <div class="text-xs text-slate-600">Team Members</div>
            </div>

            <!-- Culture Stat 3 -->
            <div class="text-center p-4 bg-white/70 backdrop-blur-sm rounded-xl border border-white/50">
              <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center mx-auto mb-3">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                </svg>
              </div>
              <div class="text-lg font-bold text-purple-600 mb-1">12</div>
              <div class="text-xs text-slate-600">Office Locations</div>
            </div>

            <!-- Culture Stat 4 -->
            <div class="text-center p-4 bg-white/70 backdrop-blur-sm rounded-xl border border-white/50">
              <div class="w-10 h-10 bg-gradient-to-r from-orange-500 to-red-600 rounded-lg flex items-center justify-center mx-auto mb-3">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                </svg>
              </div>
              <div class="text-lg font-bold text-orange-600 mb-1">4.8★</div>
              <div class="text-xs text-slate-600">Glassdoor Rating</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Our Values Section -->
<section class="py-24 bg-gradient-to-br from-slate-900 via-slate-800 to-indigo-900 relative overflow-hidden">
  <div class="absolute inset-0">
    <div class="absolute inset-0 bg-gradient-to-r from-blue-900/20 via-purple-900/20 to-indigo-900/20"></div>
    <div class="absolute top-1/4 left-1/6 w-96 h-96 bg-gradient-to-br from-blue-400/5 to-cyan-400/5 rounded-full blur-3xl"></div>
    <div class="absolute bottom-1/4 right-1/6 w-80 h-80 bg-gradient-to-br from-purple-400/5 to-pink-400/5 rounded-full blur-3xl"></div>
  </div>
  
  <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-20">
      <div class="inline-flex items-center space-x-2 bg-white/5 backdrop-blur-md rounded-full px-6 py-3 mb-8 border border-white/10">
        <svg class="w-5 h-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
          <path d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z"></path>
        </svg>
        <span class="text-sm font-medium text-white/90">Our Core Values</span>
      </div>
      
      <h2 class="text-5xl lg:text-6xl font-light text-white mb-8 tracking-tight">
        What Drives
        <span class="block text-transparent bg-clip-text bg-gradient-to-r from-cyan-300 via-blue-300 to-indigo-300 font-medium">
          Everything We Do
        </span>
      </h2>
      <p class="text-xl text-white/70 max-w-3xl mx-auto font-light leading-relaxed">
        Our values aren't just words on a wall—they're the foundation of every decision, feature, and interaction
      </p>
    </div>
    
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-10">
      <!-- Trust & Transparency -->
      <div class="group">
        <div class="bg-white/[0.08] backdrop-blur-xl rounded-3xl p-8 lg:p-10 border border-white/10 hover:bg-white/[0.12] transition-all duration-500 transform hover:-translate-y-2">
          <div class="w-16 h-16 bg-gradient-to-br from-emerald-400 to-cyan-500 rounded-2xl flex items-center justify-center mb-8 group-hover:scale-110 transition-transform duration-300 shadow-xl">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-semibold text-white mb-4">Trust & Transparency</h3>
          <p class="text-white/70 leading-relaxed mb-6">Every property is verified, every process is transparent, and every promise is kept. We believe trust is earned through consistent, honest actions.</p>
          <div class="space-y-2">
            <div class="flex items-center space-x-2">
              <svg class="w-4 h-4 text-emerald-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              <span class="text-sm text-white/80">100% verified listings</span>
            </div>
            <div class="flex items-center space-x-2">
              <svg class="w-4 h-4 text-emerald-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              <span class="text-sm text-white/80">No hidden fees</span>
            </div>
            <div class="flex items-center space-x-2">
              <svg class="w-4 h-4 text-emerald-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              <span class="text-sm text-white/80">Clear communication</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Innovation & Excellence -->
      <div class="group">
        <div class="bg-white/[0.08] backdrop-blur-xl rounded-3xl p-8 lg:p-10 border border-white/10 hover:bg-white/[0.12] transition-all duration-500 transform hover:-translate-y-2">
          <div class="w-16 h-16 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-2xl flex items-center justify-center mb-8 group-hover:scale-110 transition-transform duration-300 shadow-xl">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-semibold text-white mb-4">Innovation & Excellence</h3>
          <p class="text-white/70 leading-relaxed mb-6">We continuously push boundaries to deliver exceptional experiences. Our technology evolves with our users' needs, always staying ahead of the curve.</p>
          <div class="bg-white/10 rounded-xl p-4 border border-white/20">
            <div class="flex items-center justify-between text-sm mb-2">
              <span class="text-white/80">AI-powered matching</span>
              <span class="text-white font-semibold">95% accuracy</span>
            </div>
            <div class="flex items-center justify-between text-sm">
              <span class="text-white/80">Platform updates</span>
              <span class="text-white font-semibold">Weekly</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Community & Impact -->
      <div class="group">
        <div class="bg-white/[0.08] backdrop-blur-xl rounded-3xl p-8 lg:p-10 border border-white/10 hover:bg-white/[0.12] transition-all duration-500 transform hover:-translate-y-2">
          <div class="w-16 h-16 bg-gradient-to-br from-purple-400 to-pink-500 rounded-2xl flex items-center justify-center mb-8 group-hover:scale-110 transition-transform duration-300 shadow-xl">
            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-semibold text-white mb-4">Community & Impact</h3>
          <p class="text-white/70 leading-relaxed mb-6">We're more than a platform—we're a community. Every connection we facilitate strengthens neighborhoods and changes lives for the better.</p>
          <div class="space-y-2">
            <div class="flex items-center space-x-2">
              <svg class="w-4 h-4 text-emerald-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              <span class="text-sm text-white/80">100K+ families housed</span>
            </div>
            <div class="flex items-center space-x-2">
              <svg class="w-4 h-4 text-emerald-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              <span class="text-sm text-white/80">Local community support</span>
            </div>
            <div class="flex items-center space-x-2">
              <svg class="w-4 h-4 text-emerald-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              <span class="text-sm text-white/80">Sustainable practices</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Our Team Section -->
<section class="py-24 bg-gradient-to-br from-white via-slate-50/50 to-indigo-50/30 relative overflow-hidden">
  <div class="absolute inset-0 overflow-hidden">
    <div class="absolute -top-48 -right-48 w-96 h-96 bg-gradient-to-br from-blue-100/40 to-indigo-100/40 rounded-full blur-3xl"></div>
    <div class="absolute -bottom-48 -left-48 w-96 h-96 bg-gradient-to-br from-purple-100/40 to-pink-100/40 rounded-full blur-3xl"></div>
  </div>
  
  <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-20">
      <div class="inline-flex items-center space-x-3 bg-gradient-to-r from-slate-50 to-white rounded-full px-6 py-3 mb-8 border border-slate-200 shadow-sm">
        <div class="w-2 h-2 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full animate-pulse"></div>
        <span class="text-sm font-medium text-slate-700">Leadership Team</span>
      </div>
      
      <h2 class="text-5xl lg:text-7xl font-light text-slate-900 mb-8 tracking-tight">
        <span class="block">Meet the</span>
        <span class="text-transparent bg-clip-text bg-gradient-to-r from-slate-700 via-blue-600 to-indigo-600 font-medium">
          Visionaries
        </span>
      </h2>
      <p class="text-xl text-slate-600 max-w-4xl mx-auto leading-relaxed font-light">
        The passionate leaders driving innovation and excellence in real estate technology
      </p>
    </div>

    <!-- Team Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10">
      <!-- CEO -->
      <div class="group bg-white/80 backdrop-blur-sm rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-700 transform hover:-translate-y-3 border border-slate-100/50 overflow-hidden">
        <div class="relative h-80 overflow-hidden">
          <img src="https://images.unsplash.com/photo-1494790108755-2616b332e234?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80" 
               alt="Sarah Johnson - CEO" 
               class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
               loading="lazy">
          <!-- Professional overlay -->
          <div class="absolute inset-0 bg-gradient-to-t from-blue-900/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
          <!-- Role badge -->
          <div class="absolute top-4 right-4">
            <span class="bg-blue-500/90 backdrop-blur-sm text-white px-3 py-1 rounded-xl text-xs font-bold border border-white/20">CEO</span>
          </div>
          <!-- Quick contact on hover -->
          <div class="absolute bottom-4 left-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
            <div class="bg-white/95 backdrop-blur-sm rounded-xl p-3 shadow-lg">
              <p class="text-slate-800 text-sm font-medium">"Building the future of real estate technology"</p>
            </div>
          </div>
        </div>
        <div class="p-8">
          <h3 class="text-xl font-bold text-slate-900 mb-2">Sarah Johnson</h3>
          <p class="text-blue-600 font-semibold mb-4">Chief Executive Officer</p>
          <p class="text-slate-600 leading-relaxed text-sm mb-4">
            Former VP of Product at Zillow with 15+ years in PropTech. Passionate about using technology to solve real-world housing challenges.
          </p>
          <div class="flex items-center justify-between">
            <div class="flex space-x-3">
              <a href="#" class="w-8 h-8 bg-blue-100 hover:bg-blue-200 rounded-lg flex items-center justify-center transition-colors duration-200">
                <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.338 16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.39 0-1.601 1.086-1.601 2.207v4.248H8.014v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.778 3.203 4.092v4.711zM5.005 6.575a1.548 1.548 0 11-.003-3.096 1.548 1.548 0 01.003 3.096zm-1.337 9.763H6.34v-8.59H3.667v8.59zM17.668 1H2.328C1.595 1 1 1.581 1 2.298v15.403C1 18.418 1.595 19 2.328 19h15.34c.734 0 1.332-.582 1.332-1.299V2.298C19 1.581 18.402 1 17.668 1z" clip-rule="evenodd"></path>
                </svg>
              </a>
              <a href="#" class="w-8 h-8 bg-blue-100 hover:bg-blue-200 rounded-lg flex items-center justify-center transition-colors duration-200">
                <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M6.29 18.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0020 3.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.073 4.073 0 01.8 7.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 010 16.407a11.616 11.616 0 006.29 1.84"></path>
                </svg>
              </a>
            </div>
            <div class="text-right">
              <div class="text-xs text-slate-500">Stanford MBA</div>
              <div class="text-xs text-slate-500">15+ years exp</div>
            </div>
          </div>
        </div>
      </div>

      <!-- CTO -->
      <div class="group bg-white/80 backdrop-blur-sm rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-700 transform hover:-translate-y-3 border border-slate-100/50 overflow-hidden">
        <div class="relative h-80 overflow-hidden">
          <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80" 
               alt="Michael Chen - CTO" 
               class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
               loading="lazy">
          <!-- Professional overlay -->
          <div class="absolute inset-0 bg-gradient-to-t from-emerald-900/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
          <!-- Role badge -->
          <div class="absolute top-4 right-4">
            <span class="bg-emerald-500/90 backdrop-blur-sm text-white px-3 py-1 rounded-xl text-xs font-bold border border-white/20">CTO</span>
          </div>
          <!-- Quick contact on hover -->
          <div class="absolute bottom-4 left-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
            <div class="bg-white/95 backdrop-blur-sm rounded-xl p-3 shadow-lg">
              <p class="text-slate-800 text-sm font-medium">"AI-driven solutions for smarter real estate"</p>
            </div>
          </div>
        </div>
        <div class="p-8">
          <h3 class="text-xl font-bold text-slate-900 mb-2">Michael Chen</h3>
          <p class="text-emerald-600 font-semibold mb-4">Chief Technology Officer</p>
          <p class="text-slate-600 leading-relaxed text-sm mb-4">
            AI/ML expert and former Google engineer. Leads our technical innovation with expertise in scalable systems and machine learning algorithms.
          </p>
          <div class="flex items-center justify-between">
            <div class="flex space-x-3">
              <a href="#" class="w-8 h-8 bg-emerald-100 hover:bg-emerald-200 rounded-lg flex items-center justify-center transition-colors duration-200">
                <svg class="w-4 h-4 text-emerald-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.338 16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.39 0-1.601 1.086-1.601 2.207v4.248H8.014v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.778 3.203 4.092v4.711zM5.005 6.575a1.548 1.548 0 11-.003-3.096 1.548 1.548 0 01.003 3.096zm-1.337 9.763H6.34v-8.59H3.667v8.59zM17.668 1H2.328C1.595 1 1 1.581 1 2.298v15.403C1 18.418 1.595 19 2.328 19h15.34c.734 0 1.332-.582 1.332-1.299V2.298C19 1.581 18.402 1 17.668 1z" clip-rule="evenodd"></path>
                </svg>
              </a>
              <a href="#" class="w-8 h-8 bg-emerald-100 hover:bg-emerald-200 rounded-lg flex items-center justify-center transition-colors duration-200">
                <svg class="w-4 h-4 text-emerald-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
              </a>
            </div>
            <div class="text-right">
              <div class="text-xs text-slate-500">Ex-Google</div>
              <div class="text-xs text-slate-500">AI/ML Expert</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Head of Design -->
      <div class="group bg-white/80 backdrop-blur-sm rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-700 transform hover:-translate-y-3 border border-slate-100/50 overflow-hidden">
        <div class="relative h-80 overflow-hidden">
          <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80" 
               alt="Emily Rodriguez - Head of Design" 
               class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
               loading="lazy">
          <!-- Professional overlay -->
          <div class="absolute inset-0 bg-gradient-to-t from-purple-900/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
          <!-- Role badge -->
          <div class="absolute top-4 right-4">
            <span class="bg-purple-500/90 backdrop-blur-sm text-white px-3 py-1 rounded-xl text-xs font-bold border border-white/20">Design</span>
          </div>
          <!-- Quick contact on hover -->
          <div class="absolute bottom-4 left-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
            <div class="bg-white/95 backdrop-blur-sm rounded-xl p-3 shadow-lg">
              <p class="text-slate-800 text-sm font-medium">"Designing experiences that delight users"</p>
            </div>
          </div>
        </div>
        <div class="p-8">
          <h3 class="text-xl font-bold text-slate-900 mb-2">Emily Rodriguez</h3>
          <p class="text-purple-600 font-semibold mb-4">Head of Design</p>
          <p class="text-slate-600 leading-relaxed text-sm mb-4">
            Award-winning UX designer from Airbnb. Creates intuitive, beautiful experiences that make property search feel effortless and enjoyable.
          </p>
          <div class="flex items-center justify-between">
            <div class="flex space-x-3">
              <a href="#" class="w-8 h-8 bg-purple-100 hover:bg-purple-200 rounded-lg flex items-center justify-center transition-colors duration-200">
                <svg class="w-4 h-4 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.338 16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.39 0-1.601 1.086-1.601 2.207v4.248H8.014v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.778 3.203 4.092v4.711zM5.005 6.575a1.548 1.548 0 11-.003-3.096 1.548 1.548 0 01.003 3.096zm-1.337 9.763H6.34v-8.59H3.667v8.59zM17.668 1H2.328C1.595 1 1 1.581 1 2.298v15.403C1 18.418 1.595 19 2.328 19h15.34c.734 0 1.332-.582 1.332-1.299V2.298C19 1.581 18.402 1 17.668 1z" clip-rule="evenodd"></path>
                </svg>
              </a>
              <a href="#" class="w-8 h-8 bg-purple-100 hover:bg-purple-200 rounded-lg flex items-center justify-center transition-colors duration-200">
                <svg class="w-4 h-4 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8 0a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1v-2z" clip-rule="evenodd"></path>
                </svg>
              </a>
            </div>
            <div class="text-right">
              <div class="text-xs text-slate-500">Ex-Airbnb</div>
              <div class="text-xs text-slate-500">Award Winner</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Additional Team Members Row -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-6 mt-12">
      <!-- VP of Engineering -->
      <div class="group bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 border border-slate-100/50 overflow-hidden">
        <div class="relative h-48 overflow-hidden">
          <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80" 
               alt="David Park - VP Engineering" 
               class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
               loading="lazy">
          <div class="absolute top-3 right-3">
            <span class="bg-indigo-500/90 backdrop-blur-sm text-white px-2 py-1 rounded-lg text-xs font-bold border border-white/20">Engineering</span>
          </div>
        </div>
        <div class="p-6">
          <h4 class="text-lg font-bold text-slate-900 mb-1">David Park</h4>
          <p class="text-indigo-600 font-semibold text-sm mb-2">VP of Engineering</p>
          <p class="text-slate-600 text-xs mb-3">
            Infrastructure expert ensuring platform scalability and reliability.
          </p>
          <div class="flex items-center justify-between">
            <div class="flex space-x-2">
              <a href="#" class="w-6 h-6 bg-indigo-100 hover:bg-indigo-200 rounded-md flex items-center justify-center transition-colors duration-200">
                <svg class="w-3 h-3 text-indigo-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.338 16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.39 0-1.601 1.086-1.601 2.207v4.248H8.014v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.778 3.203 4.092v4.711z"></path>
                </svg>
              </a>
            </div>
            <div class="text-xs text-slate-500">Ex-Netflix</div>
          </div>
        </div>
      </div>

      <!-- Head of Marketing -->
      <div class="group bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 border border-slate-100/50 overflow-hidden">
        <div class="relative h-48 overflow-hidden">
          <img src="https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80" 
               alt="Rachel Kim - Head of Marketing" 
               class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
               loading="lazy">
          <div class="absolute top-3 right-3">
            <span class="bg-pink-500/90 backdrop-blur-sm text-white px-2 py-1 rounded-lg text-xs font-bold border border-white/20">Marketing</span>
          </div>
        </div>
        <div class="p-6">
          <h4 class="text-lg font-bold text-slate-900 mb-1">Rachel Kim</h4>
          <p class="text-pink-600 font-semibold text-sm mb-2">Head of Marketing</p>
          <p class="text-slate-600 text-xs mb-3">
            Growth strategist driving user acquisition and brand recognition.
          </p>
          <div class="flex items-center justify-between">
            <div class="flex space-x-2">
              <a href="#" class="w-6 h-6 bg-pink-100 hover:bg-pink-200 rounded-md flex items-center justify-center transition-colors duration-200">
                <svg class="w-3 h-3 text-pink-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.338 16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.39 0-1.601 1.086-1.601 2.207v4.248H8.014v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.778 3.203 4.092v4.711z"></path>
                </svg>
              </a>
            </div>
            <div class="text-xs text-slate-500">Ex-Stripe</div>
          </div>
        </div>
      </div>

      <!-- Head of Operations -->
      <div class="group bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 border border-slate-100/50 overflow-hidden">
        <div class="relative h-48 overflow-hidden">
          <img src="https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80" 
               alt="James Wilson - Head of Operations" 
               class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
               loading="lazy">
          <div class="absolute top-3 right-3">
            <span class="bg-orange-500/90 backdrop-blur-sm text-white px-2 py-1 rounded-lg text-xs font-bold border border-white/20">Operations</span>
          </div>
        </div>
        <div class="p-6">
          <h4 class="text-lg font-bold text-slate-900 mb-1">James Wilson</h4>
          <p class="text-orange-600 font-semibold text-sm mb-2">Head of Operations</p>
          <p class="text-slate-600 text-xs mb-3">
            Operations veteran ensuring smooth platform performance daily.
          </p>
          <div class="flex items-center justify-between">
            <div class="flex space-x-2">
              <a href="#" class="w-6 h-6 bg-orange-100 hover:bg-orange-200 rounded-md flex items-center justify-center transition-colors duration-200">
                <svg class="w-3 h-3 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.338 16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.39 0-1.601 1.086-1.601 2.207v4.248H8.014v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.778 3.203 4.092v4.711z"></path>
                </svg>
              </a>
            </div>
            <div class="text-xs text-slate-500">Ex-Uber</div>
          </div>
        </div>
      </div>

      <!-- Head of Customer Success -->
      <div class="group bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 border border-slate-100/50 overflow-hidden">
        <div class="relative h-48 overflow-hidden">
          <img src="https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80" 
               alt="Lisa Thompson - Head of Customer Success" 
               class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
               loading="lazy">
          <div class="absolute top-3 right-3">
            <span class="bg-teal-500/90 backdrop-blur-sm text-white px-2 py-1 rounded-lg text-xs font-bold border border-white/20">Success</span>
          </div>
        </div>
        <div class="p-6">
          <h4 class="text-lg font-bold text-slate-900 mb-1">Lisa Thompson</h4>
          <p class="text-teal-600 font-semibold text-sm mb-2">Head of Customer Success</p>
          <p class="text-slate-600 text-xs mb-3">
            Dedicated to ensuring every customer achieves their real estate goals.
          </p>
          <div class="flex items-center justify-between">
            <div class="flex space-x-2">
              <a href="#" class="w-6 h-6 bg-teal-100 hover:bg-teal-200 rounded-md flex items-center justify-center transition-colors duration-200">
                <svg class="w-3 h-3 text-teal-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.338 16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.39 0-1.601 1.086-1.601 2.207v4.248H8.014v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.778 3.203 4.092v4.711z"></path>
                </svg>
              </a>
            </div>
            <div class="text-xs text-slate-500">Ex-Salesforce</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Join Our Team CTA -->
    <div class="mt-20 text-center">
      <div class="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-3xl shadow-2xl shadow-blue-500/25 p-12 text-white">
        <div class="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mx-auto mb-6">
          <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
          </svg>
        </div>
        <h2 class="text-4xl font-bold mb-6">Join Our Mission</h2>
        <p class="text-xl text-blue-100 mb-8 max-w-3xl mx-auto">
          We're always looking for passionate individuals who want to help reshape the future of real estate
        </p>
        <div class="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6">
          <a href="#" class="bg-white text-blue-600 hover:bg-blue-50 px-8 py-4 rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
            View Open Positions
          </a>
          <a href="#" class="border-2 border-white text-white hover:bg-white hover:text-blue-600 px-8 py-4 rounded-2xl font-bold transition-all duration-300 transform hover:-translate-y-0.5">
            Learn About Our Culture
          </a>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Enhanced CSS for animations -->
<style>
  @keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
  }
  
  @keyframes float-delayed {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-15px) rotate(-180deg); }
  }
  
  @keyframes gradient {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }
  
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }
  
  .animate-float-delayed {
    animation: float-delayed 8s ease-in-out infinite;
  }
  
  .animate-gradient {
    background-size: 200% 200%;
    animation: gradient 3s ease infinite;
  }
  
  /* Backdrop blur fallback */
  @supports not (backdrop-filter: blur()) {
    .backdrop-blur-xl, .backdrop-blur-md, .backdrop-blur-sm {
      background-color: rgba(255, 255, 255, 0.1);
    }
  }
</style>
