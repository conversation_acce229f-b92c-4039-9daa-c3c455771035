<% content_for :title, current_user.landlord? ? "Payment Management" : "My Payments" %>
<% content_for :description, current_user.landlord? ? "Manage payments for your properties" : "View and manage your payments" %>

<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 py-6 sm:py-12">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Enhanced Header -->
    <div class="mb-12">
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div class="flex items-start space-x-6 mb-6 lg:mb-0">
          <div class="relative">
            <div class="w-16 h-16 bg-gradient-to-br from-emerald-500 via-green-500 to-teal-600 rounded-3xl flex items-center justify-center shadow-2xl">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
              </svg>
            </div>
            <!-- Pulse animation -->
            <div class="absolute inset-0 bg-emerald-500 rounded-3xl animate-ping opacity-20"></div>
          </div>
          <div>
            <h1 class="text-4xl font-bold text-slate-800 mb-3">
              <%= current_user.landlord? ? "Payment Management" : "My Payments" %>
            </h1>
            <p class="text-lg text-slate-600 max-w-2xl">
              <%= current_user.landlord? ? 
                  "Comprehensive payment tracking and management for all your properties" : 
                  "View your payment history, upcoming payments, and manage transactions" %>
            </p>
          </div>
        </div>
        
        <div class="flex items-center space-x-4">
          <% unless current_user.landlord? %>
            <%= link_to lease_agreements_path, 
                        class: "group inline-flex items-center px-6 py-4 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white rounded-2xl font-semibold transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-1" do %>
              <svg class="w-5 h-5 mr-2 group-hover:rotate-12 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
              </svg>
              View Leases
            <% end %>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Enhanced Quick Stats -->
    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 lg:gap-6 mb-12">
      <div class="group bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-white/20 p-6 hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
        <div class="flex items-center">
          <div class="w-14 h-14 bg-gradient-to-br from-slate-500 to-gray-600 rounded-2xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-200 shadow-lg">
            <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
            </svg>
          </div>
          <div>
            <p class="text-3xl font-bold text-slate-800 group-hover:text-slate-900 transition-colors duration-200"><%= @stats[:total] %></p>
            <p class="text-sm font-semibold text-slate-500 uppercase tracking-wider">Total</p>
          </div>
        </div>
      </div>

      <div class="group bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-white/20 p-6 hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
        <div class="flex items-center">
          <div class="w-14 h-14 bg-gradient-to-br from-amber-500 to-orange-600 rounded-2xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-200 shadow-lg">
            <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <div>
            <p class="text-3xl font-bold text-amber-600 group-hover:text-amber-700 transition-colors duration-200"><%= @stats[:pending] %></p>
            <p class="text-sm font-semibold text-slate-500 uppercase tracking-wider">Pending</p>
          </div>
        </div>
      </div>

      <div class="group bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-white/20 p-6 hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
        <div class="flex items-center">
          <div class="w-14 h-14 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-200 shadow-lg">
            <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
          </div>
          <div>
            <p class="text-3xl font-bold text-blue-600 group-hover:text-blue-700 transition-colors duration-200"><%= @stats[:processing] %></p>
            <p class="text-sm font-semibold text-slate-500 uppercase tracking-wider">Processing</p>
          </div>
        </div>
      </div>

      <div class="group bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-white/20 p-6 hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
        <div class="flex items-center">
          <div class="w-14 h-14 bg-gradient-to-br from-emerald-500 to-green-600 rounded-2xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-200 shadow-lg">
            <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <div>
            <p class="text-3xl font-bold text-emerald-600 group-hover:text-emerald-700 transition-colors duration-200"><%= @stats[:succeeded] %></p>
            <p class="text-sm font-semibold text-slate-500 uppercase tracking-wider">Paid</p>
          </div>
        </div>
      </div>

      <div class="group bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-white/20 p-6 hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
        <div class="flex items-center">
          <div class="w-14 h-14 bg-gradient-to-br from-rose-500 to-red-600 rounded-2xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-200 shadow-lg">
            <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <div>
            <p class="text-3xl font-bold text-rose-600 group-hover:text-rose-700 transition-colors duration-200"><%= @stats[:failed] %></p>
            <p class="text-sm font-semibold text-slate-500 uppercase tracking-wider">Failed</p>
          </div>
        </div>
      </div>

      <div class="group bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-white/20 p-6 hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 md:col-span-3 lg:col-span-1">
        <div class="flex items-center">
          <div class="w-14 h-14 bg-gradient-to-br from-teal-500 to-cyan-600 rounded-2xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-200 shadow-lg">
            <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
            </svg>
          </div>
          <div>
            <p class="text-2xl font-bold text-teal-600 group-hover:text-teal-700 transition-colors duration-200"><%= number_to_currency(@stats[:total_amount]) %></p>
            <p class="text-sm font-semibold text-slate-500 uppercase tracking-wider">Total Paid</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Enhanced Overdue & Upcoming Alerts -->
    <% if @overdue_payments.any? || @upcoming_payments.any? %>
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
        <!-- Enhanced Overdue Payments -->
        <% if @overdue_payments.any? %>
          <div class="bg-gradient-to-br from-red-50 via-rose-50 to-pink-50 backdrop-blur-sm border-2 border-red-200/50 rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300">
            <div class="flex items-center justify-between mb-6">
              <h3 class="text-xl font-bold text-red-900 flex items-center">
                <div class="w-8 h-8 bg-gradient-to-br from-red-500 to-rose-600 rounded-xl flex items-center justify-center mr-3 shadow-lg">
                  <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
                Overdue Payments
              </h3>
              <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-bold bg-red-200 text-red-800">
                <%= @stats[:overdue_count] %> overdue
              </span>
            </div>
            <div class="space-y-4">
              <% @overdue_payments.each do |payment| %>
                <div class="flex items-center justify-between bg-white/70 backdrop-blur-sm rounded-2xl p-4 shadow-lg border border-white/50 hover:shadow-xl transition-all duration-200">
                  <div>
                    <p class="font-bold text-slate-800"><%= payment.payment_type.humanize %></p>
                    <p class="text-sm text-slate-600">Due: <%= payment.due_date.strftime("%m/%d/%Y") %></p>
                  </div>
                  <div class="text-right">
                    <p class="font-bold text-red-600 text-lg"><%= number_to_currency(payment.amount) %></p>
                    <%= link_to "Pay Now", payment_path(payment), 
                                class: "inline-flex items-center text-sm bg-gradient-to-r from-red-600 to-rose-600 text-white px-4 py-2 rounded-xl font-semibold hover:from-red-700 hover:to-rose-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" %>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        <% end %>

        <!-- Enhanced Upcoming Payments -->
        <% if @upcoming_payments.any? %>
          <div class="bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 backdrop-blur-sm border-2 border-blue-200/50 rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300">
            <div class="flex items-center justify-between mb-6">
              <h3 class="text-xl font-bold text-blue-900 flex items-center">
                <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mr-3 shadow-lg">
                  <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
                Upcoming Payments
              </h3>
              <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-bold bg-blue-200 text-blue-800">
                <%= @stats[:upcoming_count] %> upcoming
              </span>
            </div>
            <div class="space-y-4">
              <% @upcoming_payments.each do |payment| %>
                <div class="flex items-center justify-between bg-white/70 backdrop-blur-sm rounded-2xl p-4 shadow-lg border border-white/50 hover:shadow-xl transition-all duration-200">
                  <div>
                    <p class="font-bold text-slate-800"><%= payment.payment_type.humanize %></p>
                    <p class="text-sm text-slate-600">Due: <%= payment.due_date.strftime("%m/%d/%Y") %></p>
                  </div>
                  <div class="text-right">
                    <p class="font-bold text-blue-600 text-lg"><%= number_to_currency(payment.amount) %></p>
                    <%= link_to "View", payment_path(payment), 
                                class: "inline-flex items-center text-sm bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-4 py-2 rounded-xl font-semibold hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" %>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        <% end %>
      </div>
    <% end %>

    <!-- Enhanced Filter Tabs -->
    <div class="mb-8">
      <div class="bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-white/20 p-2">
        <div class="flex flex-wrap gap-2">
          <%= link_to payments_path,
                      class: "group px-6 py-3 rounded-2xl font-semibold transition-all duration-300 transform hover:-translate-y-0.5 #{params[:status].blank? && params[:type].blank? ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg' : 'text-slate-700 hover:bg-slate-50 hover:shadow-lg'}" do %>
            <span class="flex items-center">
              <svg class="w-5 h-5 mr-2 group-hover:rotate-12 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
              </svg>
              All Payments
            </span>
          <% end %>

          <%= link_to payments_path(status: 'pending'),
                      class: "group px-6 py-3 rounded-2xl font-semibold transition-all duration-300 transform hover:-translate-y-0.5 #{params[:status] == 'pending' ? 'bg-gradient-to-r from-amber-600 to-orange-600 text-white shadow-lg' : 'text-slate-700 hover:bg-amber-50 hover:shadow-lg'}" do %>
            <span class="flex items-center">
              <svg class="w-5 h-5 mr-2 group-hover:rotate-12 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              Pending
            </span>
          <% end %>

          <%= link_to payments_path(status: 'succeeded'),
                      class: "group px-6 py-3 rounded-2xl font-semibold transition-all duration-300 transform hover:-translate-y-0.5 #{params[:status] == 'succeeded' ? 'bg-gradient-to-r from-emerald-600 to-green-600 text-white shadow-lg' : 'text-slate-700 hover:bg-emerald-50 hover:shadow-lg'}" do %>
            <span class="flex items-center">
              <svg class="w-5 h-5 mr-2 group-hover:rotate-12 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              Paid
            </span>
          <% end %>

          <%= link_to payments_path(status: 'failed'),
                      class: "group px-6 py-3 rounded-2xl font-semibold transition-all duration-300 transform hover:-translate-y-0.5 #{params[:status] == 'failed' ? 'bg-gradient-to-r from-rose-600 to-red-600 text-white shadow-lg' : 'text-slate-700 hover:bg-rose-50 hover:shadow-lg'}" do %>
            <span class="flex items-center">
              <svg class="w-5 h-5 mr-2 group-hover:rotate-12 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              Failed
            </span>
          <% end %>

          <%= link_to payments_path(type: 'rent'),
                      class: "group px-6 py-3 rounded-2xl font-semibold transition-all duration-300 transform hover:-translate-y-0.5 #{params[:type] == 'rent' ? 'bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg' : 'text-slate-700 hover:bg-blue-50 hover:shadow-lg'}" do %>
            <span class="flex items-center">
              <svg class="w-5 h-5 mr-2 group-hover:rotate-12 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"></path>
              </svg>
              Rent
            </span>
          <% end %>

          <%= link_to payments_path(type: 'security_deposit'),
                      class: "group px-6 py-3 rounded-2xl font-semibold transition-all duration-300 transform hover:-translate-y-0.5 #{params[:type] == 'security_deposit' ? 'bg-gradient-to-r from-purple-600 to-pink-600 text-white shadow-lg' : 'text-slate-700 hover:bg-purple-50 hover:shadow-lg'}" do %>
            <span class="flex items-center">
              <svg class="w-5 h-5 mr-2 group-hover:rotate-12 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
              </svg>
              Security Deposit
            </span>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Enhanced Payments List -->
    <% if @payments.any? %>
      <div class="space-y-6">
        <% @payments.each do |payment| %>
          <div class="group bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-white/20 p-8 hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 hover:border-slate-300/50">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
              <!-- Left: Payment Info -->
              <div class="flex items-center space-x-6 flex-1">
                <!-- Enhanced Payment Type Icon -->
                <div class="relative flex-shrink-0">
                  <div class="w-16 h-16 rounded-2xl flex items-center justify-center shadow-xl group-hover:scale-110 transition-transform duration-200 <%= 
                    case payment.payment_type
                    when 'rent' then 'bg-gradient-to-br from-blue-500 to-indigo-600'
                    when 'security_deposit' then 'bg-gradient-to-br from-purple-500 to-pink-600'
                    when 'late_fee' then 'bg-gradient-to-br from-red-500 to-rose-600'
                    else 'bg-gradient-to-br from-slate-500 to-gray-600'
                    end %>">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                  </div>
                  <!-- Status indicator -->
                  <div class="absolute -top-1 -right-1 w-6 h-6 rounded-full border-2 border-white shadow-lg <%= 
                    case payment.status
                    when 'succeeded' then 'bg-emerald-500'
                    when 'pending' then 'bg-amber-500'
                    when 'failed' then 'bg-red-500'
                    when 'processing' then 'bg-blue-500'
                    else 'bg-slate-500'
                    end %>"></div>
                </div>
                
                <!-- Enhanced Payment Details -->
                <div class="flex-1">
                  <h3 class="text-xl font-bold text-slate-800 mb-2">
                    <%= payment.payment_type.humanize %>
                    <% if payment.payment_number.present? %>
                      <span class="text-base text-slate-500 font-medium">#<%= payment.payment_number %></span>
                    <% end %>
                  </h3>
                  <div class="flex flex-wrap items-center gap-4 text-sm text-slate-600">
                    <span class="inline-flex items-center px-3 py-1 bg-slate-100 rounded-full font-medium">
                      <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                      </svg>
                      Due: <%= payment.due_date.strftime("%m/%d/%Y") %>
                    </span>
                    <% if payment.paid_at.present? %>
                      <span class="inline-flex items-center px-3 py-1 bg-emerald-100 text-emerald-700 rounded-full font-medium">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        Paid: <%= payment.paid_at.strftime("%m/%d/%Y") %>
                      </span>
                    <% end %>
                    <% if current_user.landlord? %>
                      <span class="inline-flex items-center px-3 py-1 bg-indigo-100 text-indigo-700 rounded-full font-medium">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        <%= payment.user.name || payment.user.email %>
                      </span>
                    <% end %>
                  </div>
                </div>
              </div>
              
              <!-- Center: Status & Amount -->
              <div class="flex items-center justify-between lg:justify-center lg:flex-col gap-4">
                <span class="inline-flex items-center px-4 py-2 rounded-2xl text-sm font-bold shadow-lg <%= 
                  case payment.status
                  when 'pending' then 'bg-gradient-to-r from-amber-500 to-orange-500 text-white'
                  when 'processing' then 'bg-gradient-to-r from-blue-500 to-indigo-500 text-white'
                  when 'succeeded' then 'bg-gradient-to-r from-emerald-500 to-green-500 text-white'
                  when 'failed' then 'bg-gradient-to-r from-red-500 to-rose-500 text-white'
                  when 'canceled' then 'bg-gradient-to-r from-slate-500 to-gray-500 text-white'
                  when 'refunded' then 'bg-gradient-to-r from-purple-500 to-pink-500 text-white'
                  else 'bg-gradient-to-r from-slate-500 to-gray-500 text-white'
                  end %>">
                  <%= payment.status.humanize %>
                </span>
                
                <span class="text-2xl font-bold text-emerald-600">
                  <%= number_to_currency(payment.amount) %>
                </span>
                
                <% if payment.overdue? %>
                  <span class="inline-flex items-center px-3 py-1 rounded-xl text-xs font-bold bg-gradient-to-r from-red-500 to-rose-500 text-white shadow-lg animate-pulse">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <%= pluralize(payment.days_overdue, 'day') %> overdue
                  </span>
                <% end %>
              </div>
              
              <!-- Right: Action Buttons -->
              <div class="flex items-center space-x-3">
                <%= link_to payment_path(payment), 
                            class: "group inline-flex items-center px-6 py-3 bg-gradient-to-r from-slate-600 to-gray-600 hover:from-slate-700 hover:to-gray-700 text-white rounded-2xl font-semibold transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" do %>
                  <svg class="w-4 h-4 mr-2 group-hover:rotate-12 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                  </svg>
                  View
                <% end %>
                
                <% if payment.user == current_user && ['pending', 'failed'].include?(payment.status) %>
                  <%= link_to payment_path(payment), 
                              class: "group inline-flex items-center px-6 py-3 bg-gradient-to-r from-emerald-600 to-green-600 hover:from-emerald-700 hover:to-green-700 text-white rounded-2xl font-semibold transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" do %>
                    <svg class="w-4 h-4 mr-2 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                    Pay Now
                  <% end %>
                <% end %>
              </div>
            </div>
          </div>
        <% end %>
      </div>
      
      <!-- Enhanced Pagination -->
      <div class="mt-12 flex justify-center">
        <div class="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-2">
          <%= paginate @payments if respond_to?(:paginate) %>
        </div>
      </div>
    <% else %>
      <!-- Enhanced Empty State -->
      <div class="text-center py-20">
        <div class="relative inline-flex items-center justify-center w-32 h-32 mb-8">
          <div class="absolute inset-0 bg-gradient-to-r from-emerald-500 to-green-600 rounded-full opacity-20 animate-ping"></div>
          <div class="relative w-24 h-24 bg-gradient-to-br from-emerald-500 to-green-600 rounded-3xl flex items-center justify-center shadow-2xl">
            <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
            </svg>
          </div>
        </div>
        
        <h3 class="text-3xl font-bold text-slate-800 mb-4">
          <%= current_user.landlord? ? "No payments yet" : "No payments found" %>
        </h3>
        <p class="text-lg text-slate-600 mb-12 max-w-2xl mx-auto">
          <%= current_user.landlord? ? 
              "Payments from your tenants will appear here once they start making payments. You'll be able to track all transactions in real-time." : 
              "Your payment history will appear here once you start making payments. All your transactions will be organized and easy to manage." %>
        </p>
        
        <% unless current_user.landlord? %>
          <%= link_to lease_agreements_path, 
                      class: "group inline-flex items-center px-10 py-5 bg-gradient-to-r from-emerald-600 to-green-600 hover:from-emerald-700 hover:to-green-700 text-white rounded-3xl font-bold text-lg transition-all duration-300 shadow-2xl hover:shadow-3xl transform hover:-translate-y-1" do %>
            <svg class="w-6 h-6 mr-3 group-hover:rotate-12 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
            </svg>
            View My Leases
          <% end %>
        <% end %>
      </div>
    <% end %>
  </div>
</div>