<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <style>
      body {
        font-family: Arial, sans-serif;
        line-height: 1.6;
        color: #333;
        max-width: 600px;
        margin: 0 auto;
        padding: 20px;
      }
      .header {
        background-color: #059669;
        color: white;
        padding: 20px;
        text-align: center;
        border-radius: 8px 8px 0 0;
      }
      .content {
        background-color: #f8fafc;
        padding: 30px;
        border: 1px solid #e2e8f0;
      }
      .property-info {
        background-color: #ecfdf5;
        padding: 20px;
        border-radius: 8px;
        margin: 20px 0;
        border-left: 4px solid #059669;
      }
      .participant-info {
        background-color: white;
        padding: 15px;
        border-radius: 8px;
        margin: 15px 0;
        border: 1px solid #e2e8f0;
      }
      .button {
        display: inline-block;
        background-color: #059669;
        color: white;
        padding: 12px 24px;
        text-decoration: none;
        border-radius: 6px;
        margin: 20px 0;
      }
      .footer {
        background-color: #64748b;
        color: white;
        padding: 20px;
        text-align: center;
        border-radius: 0 0 8px 8px;
        font-size: 14px;
      }
    </style>
  </head>
  <body>
    <div class="header">
      <h1>New Conversation Started</h1>
    </div>
    
    <div class="content">
      <p>Hello <%= @landlord.name || @landlord.email %>,</p>
      
      <p>A new conversation has been started regarding your property:</p>
      
      <div class="property-info">
        <h3><%= @property.title %></h3>
        <p><strong>Location:</strong> <%= @property.address %></p>
        <p><strong>Price:</strong> $<%= number_with_delimiter(@property.price) %></p>
        <% if @conversation.subject.present? %>
          <p><strong>Subject:</strong> <%= @conversation.subject %></p>
        <% end %>
      </div>
      
      <div class="participant-info">
        <h4>Conversation Participants:</h4>
        <p><strong>Tenant:</strong> <%= @tenant.name || @tenant.email %></p>
        <p><strong>Landlord:</strong> <%= @landlord.name || @landlord.email %></p>
      </div>
      
      <p>To view and respond to this conversation, please log in to your account:</p>
      
      <a href="<%= root_url %>conversations/<%= @conversation.id %>" class="button">
        View Conversation
      </a>
      
      <p>Best regards,<br>The Ofie Team</p>
    </div>
    
    <div class="footer">
      <p>This email was sent from Ofie Property Management Platform</p>
      <p>If you no longer wish to receive these notifications, please update your preferences in your account settings.</p>
    </div>
  </body>
</html>