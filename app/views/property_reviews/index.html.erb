<% content_for :title, "Reviews for #{@property.title}" %>

<div class="min-h-screen bg-gray-50">
  <!-- Header -->
  <div class="bg-white shadow-sm">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="py-6">
        <!-- Breadcrumbs -->
        <nav class="flex" aria-label="Breadcrumb">
          <ol class="flex items-center space-x-4">
            <li>
              <%= link_to "Properties", properties_path, class: "text-gray-500 hover:text-gray-700" %>
            </li>
            <li>
              <div class="flex items-center">
                <svg class="flex-shrink-0 h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                </svg>
                <%= link_to @property.title, @property, class: "ml-4 text-gray-500 hover:text-gray-700" %>
              </div>
            </li>
            <li>
              <div class="flex items-center">
                <svg class="flex-shrink-0 h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                </svg>
                <span class="ml-4 text-gray-900 font-medium">Reviews</span>
              </div>
            </li>
          </ol>
        </nav>

        <!-- Property Info -->
        <div class="mt-6">
          <h1 class="text-3xl font-bold text-gray-900">Reviews for <%= @property.title %></h1>
          <p class="mt-2 text-gray-600"><%= @property.address %></p>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Reviews List -->
      <div class="lg:col-span-2">
        <!-- Reviews Header -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div class="flex items-center justify-between">
            <div>
              <h2 class="text-xl font-semibold text-gray-900">All Reviews</h2>
              <p class="text-gray-600"><%= pluralize(@property.reviews_count, 'review') %></p>
            </div>
            <% if user_signed_in? && current_user.tenant? && !current_user.property_reviews.exists?(property: @property) %>
              <button onclick="toggleReviewForm()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                Write a Review
              </button>
            <% end %>
          </div>

          <!-- Overall Rating -->
          <div class="mt-4 flex items-center space-x-4">
            <div class="flex items-center">
              <% (1..5).each do |i| %>
                <svg class="h-5 w-5 <%= i <= @property.average_rating ? 'text-yellow-400' : 'text-gray-300' %>" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              <% end %>
              <span class="ml-2 text-lg font-medium text-gray-900"><%= @property.average_rating %></span>
            </div>
            <span class="text-gray-500">•</span>
            <span class="text-gray-600"><%= pluralize(@property.reviews_count, 'review') %></span>
          </div>
        </div>

        <!-- Write Review Form (Hidden by default) -->
        <% if @new_review %>
          <div id="review-form" class="bg-white rounded-lg shadow-sm p-6 mb-6 hidden">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Write Your Review</h3>
            
            <%= form_with model: [@property, @new_review], local: true, class: "space-y-4" do |form| %>
              <% if @new_review.errors.any? %>
                <div class="bg-red-50 border border-red-200 rounded-md p-4">
                  <div class="flex">
                    <div class="flex-shrink-0">
                      <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                      </svg>
                    </div>
                    <div class="ml-3">
                      <h3 class="text-sm font-medium text-red-800">Please fix the following errors:</h3>
                      <ul class="mt-2 text-sm text-red-700 list-disc list-inside">
                        <% @new_review.errors.full_messages.each do |message| %>
                          <li><%= message %></li>
                        <% end %>
                      </ul>
                    </div>
                  </div>
                </div>
              <% end %>

              <!-- Rating -->
              <div>
                <%= form.label :rating, "Rating", class: "block text-sm font-medium text-gray-700 mb-2" %>
                <div class="flex items-center space-x-1">
                  <% (1..5).each do |i| %>
                    <button type="button" onclick="setRating(<%= i %>)" class="star-button focus:outline-none">
                      <svg class="h-8 w-8 text-gray-300 hover:text-yellow-400 transition-colors" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                    </button>
                  <% end %>
                </div>
                <%= form.hidden_field :rating, id: "rating-input" %>
              </div>

              <!-- Title -->
              <div>
                <%= form.label :title, "Review Title", class: "block text-sm font-medium text-gray-700 mb-2" %>
                <%= form.text_field :title, class: "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent", placeholder: "Summarize your experience" %>
              </div>

              <!-- Content -->
              <div>
                <%= form.label :content, "Your Review", class: "block text-sm font-medium text-gray-700 mb-2" %>
                <%= form.text_area :content, rows: 4, class: "w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent", placeholder: "Share your experience with this property..." %>
                <p class="mt-1 text-sm text-gray-500">Minimum 50 characters</p>
              </div>

              <!-- Actions -->
              <div class="flex items-center space-x-4">
                <%= form.submit "Submit Review", class: "bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors" %>
                <button type="button" onclick="toggleReviewForm()" class="text-gray-600 hover:text-gray-800">
                  Cancel
                </button>
              </div>
            <% end %>
          </div>
        <% end %>

        <!-- Reviews List -->
        <div class="space-y-6">
          <% if @reviews.any? %>
            <% @reviews.each do |review| %>
              <div class="bg-white rounded-lg shadow-sm p-6">
                <!-- Review Header -->
                <div class="flex items-start justify-between">
                  <div class="flex items-center space-x-3">
                    <div class="flex-shrink-0">
                      <div class="h-10 w-10 bg-gray-300 rounded-full flex items-center justify-center">
                        <span class="text-sm font-medium text-gray-700">
                          <%= review.user.name&.first || review.user.email.first.upcase %>
                        </span>
                      </div>
                    </div>
                    <div>
                      <h4 class="font-medium text-gray-900">
                        <%= link_to user_reviews_path(review.user), class: "hover:text-blue-600" do %>
                          <%= review.user.name || review.user.email.split('@').first %>
                        <% end %>
                        <% if review.verified? %>
                          <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 ml-2">
                            Verified
                          </span>
                        <% end %>
                      </h4>
                      <div class="flex items-center space-x-2">
                        <div class="flex items-center">
                          <% (1..5).each do |i| %>
                            <svg class="h-4 w-4 <%= i <= review.rating ? 'text-yellow-400' : 'text-gray-300' %>" fill="currentColor" viewBox="0 0 20 20">
                              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                          <% end %>
                        </div>
                        <span class="text-sm text-gray-500"><%= time_ago_in_words(review.created_at) %> ago</span>
                      </div>
                    </div>
                  </div>
                  
                  <!-- Review Actions -->
                  <div class="flex items-center space-x-2">
                    <% if user_signed_in? && current_user == review.user %>
                      <%= link_to "Edit", edit_property_review_path(review), class: "text-blue-600 hover:text-blue-800 text-sm" %>
                    <% end %>
                    <%= link_to "View", property_review_path(review), class: "text-gray-600 hover:text-gray-800 text-sm" %>
                  </div>
                </div>

                <!-- Review Content -->
                <div class="mt-4">
                  <% if review.title.present? %>
                    <h5 class="font-medium text-gray-900 mb-2"><%= review.title %></h5>
                  <% end %>
                  <p class="text-gray-700 leading-relaxed"><%= simple_format(review.content) %></p>
                </div>

                <!-- Review Footer -->
                <div class="mt-4 flex items-center justify-between">
                  <div class="flex items-center space-x-4">
                    <% if user_signed_in? && current_user != review.user %>
                      <%= link_to property_review_mark_helpful_path(review), method: :patch, class: "flex items-center space-x-1 text-gray-500 hover:text-gray-700" do %>
                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5" />
                        </svg>
                        <span class="text-sm">Helpful (<%= review.helpful_count %>)</span>
                      <% end %>
                    <% else %>
                      <span class="flex items-center space-x-1 text-gray-500">
                        <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5" />
                        </svg>
                        <span class="text-sm">Helpful (<%= review.helpful_count %>)</span>
                      </span>
                    <% end %>
                  </div>
                </div>
              </div>
            <% end %>

            <!-- Pagination -->
            <div class="mt-8">
              <%= paginate @reviews if respond_to?(:paginate) %>
            </div>
          <% else %>
            <div class="bg-white rounded-lg shadow-sm p-12 text-center">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.456l-3.815 1.456A2 2 0 013.156 18.44l1.456-3.815A8.959 8.959 0 013 12a8 8 0 018-8 8 8 0 018 8z" />
              </svg>
              <h3 class="mt-4 text-lg font-medium text-gray-900">No reviews yet</h3>
              <p class="mt-2 text-gray-500">Be the first to share your experience with this property.</p>
              <% if user_signed_in? && current_user.tenant? && !current_user.property_reviews.exists?(property: @property) %>
                <button onclick="toggleReviewForm()" class="mt-4 bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                  Write the First Review
                </button>
              <% end %>
            </div>
          <% end %>
        </div>
      </div>

      <!-- Sidebar -->
      <div class="lg:col-span-1">
        <!-- Property Summary -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Property Summary</h3>
          <div class="space-y-3">
            <div class="flex justify-between">
              <span class="text-gray-600">Type:</span>
              <span class="font-medium"><%= @property.property_type&.humanize || 'N/A' %></span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">Bedrooms:</span>
              <span class="font-medium"><%= @property.bedrooms || 'N/A' %></span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">Bathrooms:</span>
              <span class="font-medium"><%= @property.bathrooms || 'N/A' %></span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">Square Feet:</span>
              <span class="font-medium"><%= number_with_delimiter(@property.square_footage) if @property.square_footage %></span>
            </div>
          </div>
          <div class="mt-4 pt-4 border-t">
            <%= link_to "View Property", @property, class: "w-full bg-gray-100 text-gray-900 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors text-center block" %>
          </div>
        </div>

        <!-- Rating Distribution -->
        <% if @property.reviews_count > 0 %>
          <div class="bg-white rounded-lg shadow-sm p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Rating Breakdown</h3>
            <div class="space-y-3">
              <% (1..5).reverse_each do |rating| %>
                <% count = @property.property_reviews.where(rating: rating).count %>
                <% percentage = @property.reviews_count > 0 ? (count.to_f / @property.reviews_count * 100).round : 0 %>
                <div class="flex items-center space-x-3">
                  <span class="text-sm font-medium text-gray-900 w-8"><%= rating %> ★</span>
                  <div class="flex-1 bg-gray-200 rounded-full h-2">
                    <div class="bg-yellow-400 h-2 rounded-full" style="width: <%= percentage %>%"></div>
                  </div>
                  <span class="text-sm text-gray-600 w-8"><%= count %></span>
                </div>
              <% end %>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>

<script>
  function toggleReviewForm() {
    const form = document.getElementById('review-form');
    if (form) {
      form.classList.toggle('hidden');
    }
  }

  function setRating(rating) {
    document.getElementById('rating-input').value = rating;
    
    // Update star display
    const stars = document.querySelectorAll('.star-button svg');
    stars.forEach((star, index) => {
      if (index < rating) {
        star.classList.remove('text-gray-300');
        star.classList.add('text-yellow-400');
      } else {
        star.classList.remove('text-yellow-400');
        star.classList.add('text-gray-300');
      }
    });
  }
</script>