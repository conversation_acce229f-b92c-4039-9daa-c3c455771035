<% content_for :title, "Review by #{@review.user.name || @review.user.email.split('@').first}" %>

<div class="min-h-screen bg-gray-50">
  <!-- Header -->
  <div class="bg-white shadow-sm">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="py-6">
        <!-- Breadcrumbs -->
        <nav class="flex" aria-label="Breadcrumb">
          <ol class="flex items-center space-x-4">
            <li>
              <%= link_to "Properties", properties_path, class: "text-gray-500 hover:text-gray-700" %>
            </li>
            <li>
              <div class="flex items-center">
                <svg class="flex-shrink-0 h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                </svg>
                <%= link_to @property.title, @property, class: "ml-4 text-gray-500 hover:text-gray-700" %>
              </div>
            </li>
            <li>
              <div class="flex items-center">
                <svg class="flex-shrink-0 h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                </svg>
                <%= link_to "Reviews", property_reviews_path(@property), class: "ml-4 text-gray-500 hover:text-gray-700" %>
              </div>
            </li>
            <li>
              <div class="flex items-center">
                <svg class="flex-shrink-0 h-5 w-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                </svg>
                <span class="ml-4 text-gray-900 font-medium">Review Details</span>
              </div>
            </li>
          </ol>
        </nav>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Review Content -->
      <div class="lg:col-span-2">
        <!-- Review Card -->
        <div class="bg-white rounded-lg shadow-sm p-8">
          <!-- Review Header -->
          <div class="flex items-start justify-between mb-6">
            <div class="flex items-center space-x-4">
              <div class="flex-shrink-0">
                <div class="h-12 w-12 bg-gray-300 rounded-full flex items-center justify-center">
                  <span class="text-lg font-medium text-gray-700">
                    <%= @review.user.name&.first || @review.user.email.first.upcase %>
                  </span>
                </div>
              </div>
              <div>
                <h2 class="text-xl font-semibold text-gray-900">
                  <%= link_to user_reviews_path(@review.user), class: "hover:text-blue-600" do %>
                    <%= @review.user.name || @review.user.email.split('@').first %>
                  <% end %>
                  <% if @review.verified? %>
                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 ml-2">
                      Verified Review
                    </span>
                  <% end %>
                </h2>
                <div class="flex items-center space-x-3 mt-1">
                  <div class="flex items-center">
                    <% (1..5).each do |i| %>
                      <svg class="h-5 w-5 <%= i <= @review.rating ? 'text-yellow-400' : 'text-gray-300' %>" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                    <% end %>
                    <span class="ml-2 text-lg font-medium text-gray-900"><%= @review.rating %>/5</span>
                  </div>
                  <span class="text-gray-400">•</span>
                  <span class="text-gray-600"><%= @review.created_at.strftime("%B %d, %Y") %></span>
                </div>
              </div>
            </div>
            
            <!-- Actions -->
            <div class="flex items-center space-x-3">
              <% if user_signed_in? && current_user == @review.user %>
                <%= link_to "Edit Review", edit_property_review_path(@review), class: "inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" %>
                <%= link_to "Delete", property_review_path(@review), method: :delete, 
                    confirm: "Are you sure you want to delete this review?", 
                    class: "inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500" %>
              <% end %>
            </div>
          </div>

          <!-- Review Title -->
          <% if @review.title.present? %>
            <h3 class="text-2xl font-bold text-gray-900 mb-4"><%= @review.title %></h3>
          <% end %>

          <!-- Review Content -->
          <div class="prose prose-gray max-w-none mb-6">
            <p class="text-gray-700 leading-relaxed text-lg"><%= simple_format(@review.content) %></p>
          </div>

          <!-- Review Footer -->
          <div class="border-t pt-6">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-6">
                <% if user_signed_in? && current_user != @review.user %>
                  <%= link_to property_review_mark_helpful_path(@review), method: :patch, class: "flex items-center space-x-2 text-gray-600 hover:text-gray-800 transition-colors" do %>
                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5" />
                    </svg>
                    <span>Mark as Helpful</span>
                    <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-sm"><%= @review.helpful_count %></span>
                  <% end %>
                <% else %>
                  <div class="flex items-center space-x-2 text-gray-600">
                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5" />
                    </svg>
                    <span>Helpful</span>
                    <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-sm"><%= @review.helpful_count %></span>
                  </div>
                <% end %>
                
                <div class="flex items-center space-x-2 text-gray-600">
                  <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span>Posted <%= time_ago_in_words(@review.created_at) %> ago</span>
                </div>
              </div>
              
              <div class="flex items-center space-x-3">
                <%= link_to "All Reviews", property_reviews_path(@property), class: "text-blue-600 hover:text-blue-800 font-medium" %>
                <%= link_to "View Property", @property, class: "text-blue-600 hover:text-blue-800 font-medium" %>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Sidebar -->
      <div class="lg:col-span-1">
        <!-- Property Info -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">About This Property</h3>
          <div class="space-y-3">
            <h4 class="font-medium text-gray-900"><%= @property.title %></h4>
            <p class="text-gray-600 text-sm"><%= @property.address %></p>
            
            <div class="grid grid-cols-2 gap-3 text-sm">
              <div>
                <span class="text-gray-500">Type:</span>
                <div class="font-medium"><%= @property.property_type&.humanize || 'N/A' %></div>
              </div>
              <div>
                <span class="text-gray-500">Bedrooms:</span>
                <div class="font-medium"><%= @property.bedrooms || 'N/A' %></div>
              </div>
              <div>
                <span class="text-gray-500">Bathrooms:</span>
                <div class="font-medium"><%= @property.bathrooms || 'N/A' %></div>
              </div>
              <div>
                <span class="text-gray-500">Sq Ft:</span>
                <div class="font-medium"><%= number_with_delimiter(@property.square_footage) if @property.square_footage %></div>
              </div>
            </div>
          </div>
          
          <div class="mt-4 pt-4 border-t">
            <%= link_to "View Property Details", @property, class: "w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-center block" %>
          </div>
        </div>

        <!-- Property Rating Summary -->
        <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">Property Rating</h3>
          <div class="text-center">
            <div class="text-3xl font-bold text-gray-900 mb-2"><%= @property.average_rating %></div>
            <div class="flex items-center justify-center mb-2">
              <% (1..5).each do |i| %>
                <svg class="h-5 w-5 <%= i <= @property.average_rating ? 'text-yellow-400' : 'text-gray-300' %>" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              <% end %>
            </div>
            <p class="text-gray-600 text-sm">Based on <%= pluralize(@property.reviews_count, 'review') %></p>
          </div>
          
          <div class="mt-4 pt-4 border-t">
            <%= link_to "View All Reviews", property_reviews_path(@property), class: "w-full bg-gray-100 text-gray-900 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors text-center block" %>
          </div>
        </div>

        <!-- Reviewer Info -->
        <div class="bg-white rounded-lg shadow-sm p-6">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">About the Reviewer</h3>
          <div class="flex items-center space-x-3 mb-4">
            <div class="h-10 w-10 bg-gray-300 rounded-full flex items-center justify-center">
              <span class="text-sm font-medium text-gray-700">
                <%= @review.user.name&.first || @review.user.email.first.upcase %>
              </span>
            </div>
            <div>
              <h4 class="font-medium text-gray-900">
                <%= @review.user.name || @review.user.email.split('@').first %>
              </h4>
              <% if @review.user.tenant? %>
                <span class="text-sm text-gray-600">Tenant</span>
              <% end %>
            </div>
          </div>
          
          <div class="space-y-2 text-sm">
            <div class="flex justify-between">
              <span class="text-gray-600">Total Reviews:</span>
              <span class="font-medium"><%= @review.user.property_reviews.count %></span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">Verified Reviews:</span>
              <span class="font-medium"><%= @review.user.property_reviews.verified.count %></span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">Average Rating:</span>
              <span class="font-medium"><%= (@review.user.property_reviews.average(:rating) || 0).round(1) %>/5</span>
            </div>
          </div>
          
          <div class="mt-4 pt-4 border-t">
            <%= link_to "View All Reviews by #{@review.user.name || @review.user.email.split('@').first}", user_reviews_path(@review.user), class: "w-full bg-gray-100 text-gray-900 px-4 py-2 rounded-lg hover:bg-gray-200 transition-colors text-center block" %>
          </div>
        </div>
      </div>
    </div>

    <!-- Related Reviews -->
    <% if @related_reviews.any? %>
      <div class="mt-12">
        <h3 class="text-xl font-semibold text-gray-900 mb-6">Other Reviews for This Property</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <% @related_reviews.each do |review| %>
            <div class="bg-white rounded-lg shadow-sm p-6">
              <div class="flex items-center space-x-3 mb-3">
                <div class="h-8 w-8 bg-gray-300 rounded-full flex items-center justify-center">
                  <span class="text-xs font-medium text-gray-700">
                    <%= review.user.name&.first || review.user.email.first.upcase %>
                  </span>
                </div>
                <div>
                  <h4 class="font-medium text-gray-900 text-sm">
                    <%= review.user.name || review.user.email.split('@').first %>
                  </h4>
                  <div class="flex items-center">
                    <% (1..5).each do |i| %>
                      <svg class="h-3 w-3 <%= i <= review.rating ? 'text-yellow-400' : 'text-gray-300' %>" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                    <% end %>
                  </div>
                </div>
              </div>
              
              <% if review.title.present? %>
                <h5 class="font-medium text-gray-900 text-sm mb-2"><%= truncate(review.title, length: 50) %></h5>
              <% end %>
              <p class="text-gray-600 text-sm mb-3"><%= truncate(review.content, length: 100) %></p>
              
              <div class="flex items-center justify-between">
                <span class="text-xs text-gray-500"><%= time_ago_in_words(review.created_at) %> ago</span>
                <%= link_to "Read More", property_review_path(review), class: "text-blue-600 hover:text-blue-800 text-xs font-medium" %>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    <% end %>
  </div>
</div>