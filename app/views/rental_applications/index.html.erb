<% content_for :title, current_user.landlord? ? "Rental Applications" : "My Applications" %>
<% content_for :description, current_user.landlord? ? "Manage rental applications for your properties" : "Track your rental applications" %>

<div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
  <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold text-gray-900 mb-2 flex items-center">
            <svg class="w-8 h-8 mr-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
            </svg>
            <%= current_user.landlord? ? "Rental Applications" : "My Applications" %>
          </h1>
          <p class="text-gray-600">
            <%= current_user.landlord? ? "Review and manage applications for your properties" : "Track the status of your rental applications" %>
          </p>
        </div>

        <div class="flex items-center space-x-4">
          <% unless current_user.landlord? %>
            <%= link_to properties_path,
                        class: "inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" do %>
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
              Browse Properties
            <% end %>
          <% end %>
        </div>
      </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
      <div class="bg-white/80 backdrop-blur-xl rounded-2xl shadow-lg border border-white/20 p-4">
        <div class="text-center">
          <p class="text-2xl font-bold text-gray-900"><%= @stats[:total] %></p>
          <p class="text-sm text-gray-600">Total</p>
        </div>
      </div>
      <div class="bg-white/80 backdrop-blur-xl rounded-2xl shadow-lg border border-white/20 p-4">
        <div class="text-center">
          <p class="text-2xl font-bold text-amber-600"><%= @stats[:pending] %></p>
          <p class="text-sm text-gray-600">Pending</p>
        </div>
      </div>
      <div class="bg-white/80 backdrop-blur-xl rounded-2xl shadow-lg border border-white/20 p-4">
        <div class="text-center">
          <p class="text-2xl font-bold text-green-600"><%= @stats[:approved] %></p>
          <p class="text-sm text-gray-600">Approved</p>
        </div>
      </div>
      <div class="bg-white/80 backdrop-blur-xl rounded-2xl shadow-lg border border-white/20 p-4">
        <div class="text-center">
          <p class="text-2xl font-bold text-red-600"><%= @stats[:rejected] %></p>
          <p class="text-sm text-gray-600">Rejected</p>
        </div>
      </div>
    </div>

    <!-- Filter Tabs -->
    <div class="mb-6">
      <div class="flex flex-wrap gap-2">
        <%= link_to rental_applications_path,
                    class: "px-4 py-2 rounded-xl font-medium transition-all duration-200 #{params[:status].blank? ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-blue-50'}" do %>
          All
        <% end %>
        <%= link_to rental_applications_path(status: 'pending'),
                    class: "px-4 py-2 rounded-xl font-medium transition-all duration-200 #{params[:status] == 'pending' ? 'bg-amber-600 text-white' : 'bg-white text-gray-700 hover:bg-amber-50'}" do %>
          Pending
        <% end %>
        <%= link_to rental_applications_path(status: 'approved'),
                    class: "px-4 py-2 rounded-xl font-medium transition-all duration-200 #{params[:status] == 'approved' ? 'bg-green-600 text-white' : 'bg-white text-gray-700 hover:bg-green-50'}" do %>
          Approved
        <% end %>
        <%= link_to rental_applications_path(status: 'rejected'),
                    class: "px-4 py-2 rounded-xl font-medium transition-all duration-200 #{params[:status] == 'rejected' ? 'bg-red-600 text-white' : 'bg-white text-gray-700 hover:bg-red-50'}" do %>
          Rejected
        <% end %>
      </div>
    </div>

    <!-- Applications List -->
    <% if @rental_applications.any? %>
      <div class="space-y-4">
        <% @rental_applications.each do |application| %>
          <div class="bg-white/80 backdrop-blur-xl rounded-2xl shadow-lg border border-white/20 p-6 hover:shadow-xl transition-all duration-300">
            <div class="flex items-center justify-between">
              <!-- Left: Property & Applicant Info -->
              <div class="flex items-center space-x-4 flex-1">
                <!-- Property Image -->
                <div class="w-12 h-12 rounded-xl overflow-hidden flex-shrink-0">
                  <% if application.property.photos.attached? && application.property.photos.any? %>
                    <%= image_tag application.property.photos.first,
                                  class: "w-full h-full object-cover",
                                  alt: application.property.title %>
                  <% else %>
                    <div class="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                      <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"></path>
                      </svg>
                    </div>
                  <% end %>
                </div>

                <!-- Property & Application Details -->
                <div class="flex-1">
                  <h3 class="font-bold text-gray-900 mb-1">
                    <%= link_to application.property.title, property_path(application.property), class: "hover:text-blue-600 transition-colors" %>
                  </h3>
                  <div class="flex items-center space-x-4 text-sm text-gray-500">
                    <span>Applied: <%= application.application_date.strftime("%m/%d/%Y") %></span>
                    <% if current_user.landlord? %>
                      <span>•</span>
                      <span>Applicant: <%= application.tenant.name || application.tenant.email %></span>
                    <% end %>
                  </div>
                </div>
              </div>

              <!-- Center: Status & Price -->
              <div class="flex items-center space-x-4">
                <span class="inline-flex items-center px-3 py-1 rounded-xl text-sm font-bold <%=
                  case application.status
                  when 'pending' then 'bg-amber-100 text-amber-800'
                  when 'under_review' then 'bg-purple-100 text-purple-800'
                  when 'approved' then 'bg-green-100 text-green-800'
                  when 'rejected' then 'bg-red-100 text-red-800'
                  else 'bg-gray-100 text-gray-800'
                  end %>">
                  <%= application.status.humanize %>
                </span>

                <span class="font-bold text-blue-600">
                  <%= number_to_currency(application.property.price) %>/mo
                </span>
              </div>

              <!-- Right: Action Buttons -->
              <div class="flex items-center space-x-2">
                <%= link_to rental_application_path(application),
                            class: "inline-flex items-center px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-xl font-medium transition-all duration-200" do %>
                  View
                <% end %>

                <% if current_user.landlord? && ['pending', 'under_review'].include?(application.status) %>
                  <%= link_to approve_rental_application_path(application),
                              method: :post,
                              class: "inline-flex items-center px-3 py-2 bg-green-600 hover:bg-green-700 text-white rounded-xl font-medium transition-all duration-200",
                              data: { confirm: "Approve this application?" } do %>
                    Approve
                  <% end %>

                  <%= link_to reject_rental_application_path(application),
                              method: :post,
                              class: "inline-flex items-center px-3 py-2 bg-red-600 hover:bg-red-700 text-white rounded-xl font-medium transition-all duration-200",
                              data: { confirm: "Reject this application?" } do %>
                    Reject
                  <% end %>
                <% end %>
              </div>
            </div>
          </div>
        <% end %>
      </div>
      
      <!-- Pagination -->
      <div class="mt-8 flex justify-center">
        <%= paginate @rental_applications if respond_to?(:paginate) %>
      </div>
    <% else %>
      <!-- Empty State -->
      <div class="text-center py-16">
        <div class="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-r from-blue-100 to-indigo-100 rounded-3xl shadow-lg shadow-blue-500/25 mb-6">
          <svg class="w-12 h-12 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
          </svg>
        </div>
        
        <h3 class="text-2xl font-bold text-gray-900 mb-4">
          <%= current_user.landlord? ? "No applications yet" : "No applications submitted" %>
        </h3>
        <p class="text-gray-600 mb-8 max-w-md mx-auto">
          <%= current_user.landlord? ? 
              "When tenants apply for your properties, their applications will appear here." : 
              "Start browsing properties and submit applications to track them here." %>
        </p>
        
        <% unless current_user.landlord? %>
          <%= link_to properties_path, 
                      class: "inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" do %>
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
            Browse Properties
          <% end %>
        <% end %>
      </div>
    <% end %>
  </div>
</div>
