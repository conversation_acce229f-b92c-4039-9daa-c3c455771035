<!-- Status Update Modal -->
<div id="statusUpdateModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50" data-controller="modal">
  <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-3xl bg-white">
    <div class="mt-3">
      <!-- Modal Header -->
      <div class="flex items-center justify-between mb-6">
        <h3 class="text-lg font-bold text-gray-900" id="modalTitle">Update Application Status</h3>
        <button type="button" 
                class="text-gray-400 hover:text-gray-600 transition-colors"
                onclick="closeStatusModal()">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- Modal Content -->
      <div id="modalContent">
        <!-- Approve Form -->
        <div id="approveForm" class="hidden">
          <%= form_with url: "", method: :post, local: true, id: "approveApplicationForm", class: "space-y-4" do |form| %>
            <div class="text-center mb-4">
              <div class="w-16 h-16 bg-gradient-to-r from-green-100 to-emerald-100 rounded-2xl flex items-center justify-center mx-auto mb-3">
                <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <h4 class="text-lg font-bold text-gray-900">Approve Application</h4>
              <p class="text-gray-600">This will approve the rental application and notify the tenant.</p>
            </div>

            <div>
              <label class="block text-sm font-bold text-gray-700 mb-2">Review Notes (Optional)</label>
              <textarea name="review_notes" 
                        rows="3"
                        class="w-full px-4 py-3 border border-gray-300 rounded-2xl focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none"
                        placeholder="Add any notes for the tenant..."></textarea>
            </div>

            <div class="flex items-center justify-end space-x-3 pt-4">
              <button type="button" 
                      onclick="closeStatusModal()"
                      class="px-4 py-2 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-colors">
                Cancel
              </button>
              <button type="submit" 
                      class="px-6 py-2 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white rounded-xl font-bold transition-all duration-300">
                Approve Application
              </button>
            </div>
          <% end %>
        </div>

        <!-- Reject Form -->
        <div id="rejectForm" class="hidden">
          <%= form_with url: "", method: :post, local: true, id: "rejectApplicationForm", class: "space-y-4" do |form| %>
            <div class="text-center mb-4">
              <div class="w-16 h-16 bg-gradient-to-r from-red-100 to-pink-100 rounded-2xl flex items-center justify-center mx-auto mb-3">
                <svg class="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <h4 class="text-lg font-bold text-gray-900">Reject Application</h4>
              <p class="text-gray-600">This will reject the rental application and notify the tenant.</p>
            </div>

            <div>
              <label class="block text-sm font-bold text-gray-700 mb-2">Reason for Rejection (Optional)</label>
              <textarea name="review_notes" 
                        rows="3"
                        class="w-full px-4 py-3 border border-gray-300 rounded-2xl focus:ring-2 focus:ring-red-500 focus:border-transparent resize-none"
                        placeholder="Provide feedback to help the tenant improve future applications..."></textarea>
            </div>

            <div class="flex items-center justify-end space-x-3 pt-4">
              <button type="button" 
                      onclick="closeStatusModal()"
                      class="px-4 py-2 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-colors">
                Cancel
              </button>
              <button type="submit" 
                      class="px-6 py-2 bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-700 hover:to-pink-700 text-white rounded-xl font-bold transition-all duration-300">
                Reject Application
              </button>
            </div>
          <% end %>
        </div>

        <!-- Under Review Form -->
        <div id="underReviewForm" class="hidden">
          <%= form_with url: "", method: :post, local: true, id: "underReviewApplicationForm", class: "space-y-4" do |form| %>
            <div class="text-center mb-4">
              <div class="w-16 h-16 bg-gradient-to-r from-purple-100 to-indigo-100 rounded-2xl flex items-center justify-center mx-auto mb-3">
                <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
              </div>
              <h4 class="text-lg font-bold text-gray-900">Mark Under Review</h4>
              <p class="text-gray-600">This will mark the application as under review and notify the tenant.</p>
            </div>

            <div>
              <label class="block text-sm font-bold text-gray-700 mb-2">Review Notes (Optional)</label>
              <textarea name="review_notes" 
                        rows="3"
                        class="w-full px-4 py-3 border border-gray-300 rounded-2xl focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none"
                        placeholder="Let the tenant know what you're reviewing..."></textarea>
            </div>

            <div class="flex items-center justify-end space-x-3 pt-4">
              <button type="button" 
                      onclick="closeStatusModal()"
                      class="px-4 py-2 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-colors">
                Cancel
              </button>
              <button type="submit" 
                      class="px-6 py-2 bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white rounded-xl font-bold transition-all duration-300">
                Mark Under Review
              </button>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  function openStatusModal(action, applicationId) {
    const modal = document.getElementById('statusUpdateModal');
    const approveForm = document.getElementById('approveForm');
    const rejectForm = document.getElementById('rejectForm');
    const underReviewForm = document.getElementById('underReviewForm');
    
    // Hide all forms
    approveForm.classList.add('hidden');
    rejectForm.classList.add('hidden');
    underReviewForm.classList.add('hidden');
    
    // Show the appropriate form and set the action URL
    if (action === 'approve') {
      approveForm.classList.remove('hidden');
      document.getElementById('approveApplicationForm').action = `/rental_applications/${applicationId}/approve`;
    } else if (action === 'reject') {
      rejectForm.classList.remove('hidden');
      document.getElementById('rejectApplicationForm').action = `/rental_applications/${applicationId}/reject`;
    } else if (action === 'under_review') {
      underReviewForm.classList.remove('hidden');
      document.getElementById('underReviewApplicationForm').action = `/rental_applications/${applicationId}/under_review`;
    }
    
    // Show the modal
    modal.classList.remove('hidden');
    document.body.style.overflow = 'hidden';
  }
  
  function closeStatusModal() {
    const modal = document.getElementById('statusUpdateModal');
    modal.classList.add('hidden');
    document.body.style.overflow = 'auto';
    
    // Clear all textareas
    modal.querySelectorAll('textarea').forEach(textarea => {
      textarea.value = '';
    });
  }
  
  // Close modal when clicking outside
  document.getElementById('statusUpdateModal').addEventListener('click', function(e) {
    if (e.target === this) {
      closeStatusModal();
    }
  });
  
  // Close modal with Escape key
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
      closeStatusModal();
    }
  });
</script>
