<% content_for :title, "Apply for #{@property.title}" %>
<% content_for :description, "Submit your rental application" %>

<div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
  <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="text-center mb-8">
      <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl shadow-lg shadow-blue-500/25 mb-4">
        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
        </svg>
      </div>
      <h1 class="text-3xl font-bold text-gray-900 mb-2">Apply for Rental</h1>
      <p class="text-gray-600">Submit your application for this property</p>
    </div>

    <!-- Property Info Card -->
    <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-6 mb-8">
      <div class="flex items-center space-x-4">
        <% if @property.photos.attached? && @property.photos.any? %>
          <div class="w-20 h-20 rounded-2xl overflow-hidden flex-shrink-0">
            <%= image_tag @property.photos.first, 
                          class: "w-full h-full object-cover",
                          alt: @property.title %>
          </div>
        <% else %>
          <div class="w-20 h-20 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-2xl flex items-center justify-center flex-shrink-0">
            <svg class="w-10 h-10 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"></path>
            </svg>
          </div>
        <% end %>
        
        <div class="flex-1">
          <h3 class="text-xl font-bold text-gray-900"><%= @property.title %></h3>
          <p class="text-gray-600"><%= @property.address %></p>
          <p class="text-lg font-bold text-blue-600">
            <%= number_to_currency(@property.price) %>/month
          </p>
        </div>
      </div>
    </div>

    <!-- Application Form -->
    <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-8">
      <%= form_with model: [@property, @rental_application], local: true, class: "space-y-6" do |form| %>
        <% if @rental_application.errors.any? %>
          <div class="bg-red-50 border border-red-200 rounded-2xl p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">Please fix the following errors:</h3>
                <div class="mt-2 text-sm text-red-700">
                  <ul class="list-disc pl-5 space-y-1">
                    <% @rental_application.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <!-- Basic Information -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Move-in Date -->
          <div>
            <%= form.label :move_in_date, class: "block text-sm font-bold text-gray-700 mb-2" do %>
              <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
              Desired Move-in Date *
            <% end %>
            <%= form.date_field :move_in_date, 
                                class: "w-full px-4 py-3 border border-gray-300 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                                min: Date.current,
                                required: true %>
          </div>

          <!-- Monthly Income -->
          <div>
            <%= form.label :monthly_income, class: "block text-sm font-bold text-gray-700 mb-2" do %>
              <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
              </svg>
              Monthly Income *
            <% end %>
            <%= form.number_field :monthly_income, 
                                  class: "w-full px-4 py-3 border border-gray-300 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                                  placeholder: "Enter your monthly income",
                                  min: 0,
                                  step: 100,
                                  required: true %>
            <p class="mt-1 text-sm text-gray-500">
              Minimum required: <%= number_to_currency(@property.price * 3) %> 
              (3x monthly rent)
            </p>
          </div>
        </div>

        <!-- Employment Information -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Employment Status -->
          <div>
            <%= form.label :employment_status, class: "block text-sm font-bold text-gray-700 mb-2" do %>
              <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V8a2 2 0 012-2V6"></path>
              </svg>
              Employment Status *
            <% end %>
            <%= form.select :employment_status, 
                            options_for_select([
                              ['Full-time Employee', 'full_time'],
                              ['Part-time Employee', 'part_time'],
                              ['Self-employed', 'self_employed'],
                              ['Contract Worker', 'contract'],
                              ['Student', 'student'],
                              ['Retired', 'retired'],
                              ['Unemployed', 'unemployed']
                            ], @rental_application.employment_status),
                            { prompt: 'Select employment status' },
                            { class: "w-full px-4 py-3 border border-gray-300 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-transparent", required: true } %>
          </div>

          <!-- Employer Name -->
          <div>
            <%= form.label :employer_name, class: "block text-sm font-bold text-gray-700 mb-2" do %>
              <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
              </svg>
              Employer/Company Name
            <% end %>
            <%= form.text_field :employer_name, 
                                class: "w-full px-4 py-3 border border-gray-300 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-transparent",
                                placeholder: "Enter employer or company name" %>
          </div>
        </div>

        <!-- Previous Address -->
        <div>
          <%= form.label :previous_address, class: "block text-sm font-bold text-gray-700 mb-2" do %>
            <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
            Previous Address *
          <% end %>
          <%= form.text_area :previous_address, 
                             rows: 3,
                             class: "w-full px-4 py-3 border border-gray-300 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none",
                             placeholder: "Enter your current or most recent address",
                             required: true %>
        </div>

        <!-- References -->
        <div>
          <%= form.label :references_contact, class: "block text-sm font-bold text-gray-700 mb-2" do %>
            <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
            </svg>
            References Contact Information *
          <% end %>
          <%= form.text_area :references_contact, 
                             rows: 4,
                             class: "w-full px-4 py-3 border border-gray-300 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none",
                             placeholder: "Please provide contact information for 2-3 references (name, relationship, phone number)",
                             required: true %>
        </div>

        <!-- Additional Notes -->
        <div>
          <%= form.label :additional_notes, class: "block text-sm font-bold text-gray-700 mb-2" do %>
            <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
            </svg>
            Additional Notes (Optional)
          <% end %>
          <%= form.text_area :additional_notes, 
                             rows: 4,
                             class: "w-full px-4 py-3 border border-gray-300 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none",
                             placeholder: "Any additional information you'd like to share with the landlord..." %>
        </div>

        <!-- Consent Checkboxes -->
        <div class="space-y-4 bg-gray-50 rounded-2xl p-6">
          <h3 class="text-lg font-bold text-gray-900 mb-4">Required Consents</h3>
          
          <div class="flex items-start">
            <%= form.check_box :background_check_consent, 
                               class: "mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded",
                               required: true %>
            <%= form.label :background_check_consent, class: "ml-3 text-sm text-gray-700" do %>
              I consent to a background check being performed as part of this rental application process. *
            <% end %>
          </div>
          
          <div class="flex items-start">
            <%= form.check_box :credit_check_consent, 
                               class: "mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded",
                               required: true %>
            <%= form.label :credit_check_consent, class: "ml-3 text-sm text-gray-700" do %>
              I consent to a credit check being performed as part of this rental application process. *
            <% end %>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex items-center justify-between pt-6">
          <%= link_to property_path(@property), 
                      class: "inline-flex items-center px-6 py-3 border-2 border-gray-200 text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-300 rounded-2xl font-bold transition-all duration-300 transform hover:-translate-y-0.5" do %>
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Property
          <% end %>
          
          <%= form.submit "Submit Application", 
                          class: "inline-flex items-center px-8 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed",
                          data: { disable_with: "Submitting..." } %>
        </div>
      <% end %>
    </div>

    <!-- Information Box -->
    <div class="mt-8 bg-blue-50 rounded-3xl p-6 border border-blue-100">
      <h3 class="text-lg font-bold text-blue-900 mb-4 flex items-center">
        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        What happens next?
      </h3>
      <ul class="space-y-2 text-blue-800">
        <li class="flex items-start">
          <svg class="w-4 h-4 mr-2 mt-0.5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
          Your application will be reviewed by the landlord
        </li>
        <li class="flex items-start">
          <svg class="w-4 h-4 mr-2 mt-0.5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
          You'll receive updates on your application status
        </li>
        <li class="flex items-start">
          <svg class="w-4 h-4 mr-2 mt-0.5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
          Background and credit checks will be processed
        </li>
        <li class="flex items-start">
          <svg class="w-4 h-4 mr-2 mt-0.5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
          If approved, you'll be contacted to proceed with the lease
        </li>
      </ul>
    </div>
  </div>
</div>
