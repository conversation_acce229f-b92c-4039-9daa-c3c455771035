<% content_for :title, "Rental Application" %>
<% content_for :description, "View rental application details" %>

<div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold text-gray-900 mb-2">Rental Application</h1>
          <p class="text-gray-600">Application details and status</p>
        </div>
        
        <div class="flex items-center space-x-4">
          <%= link_to rental_applications_path, 
                      class: "inline-flex items-center px-4 py-2 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Applications
          <% end %>
        </div>
      </div>
    </div>

    <!-- Status Banner -->
    <div class="mb-8">
      <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-6">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="w-16 h-16 bg-gradient-to-r <%= 
              case @rental_application.status
              when 'pending' then 'from-amber-100 to-orange-100'
              when 'under_review' then 'from-purple-100 to-indigo-100'
              when 'approved' then 'from-green-100 to-emerald-100'
              when 'rejected' then 'from-red-100 to-pink-100'
              else 'from-gray-100 to-gray-200'
              end %> rounded-2xl flex items-center justify-center">
              <svg class="w-8 h-8 <%= 
                case @rental_application.status
                when 'pending' then 'text-amber-600'
                when 'under_review' then 'text-purple-600'
                when 'approved' then 'text-green-600'
                when 'rejected' then 'text-red-600'
                else 'text-gray-600'
                end %>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <% case @rental_application.status %>
                <% when 'pending' %>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                <% when 'under_review' %>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                <% when 'approved' %>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                <% when 'rejected' %>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                <% else %>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                <% end %>
              </svg>
            </div>
            
            <div>
              <h2 class="text-2xl font-bold text-gray-900">
                Application <%= @rental_application.status.humanize %>
              </h2>
              <p class="text-gray-600">
                Submitted on <%= @rental_application.application_date.strftime("%B %d, %Y") %>
              </p>
            </div>
          </div>
          
          <div class="text-right">
            <span class="inline-flex items-center px-4 py-2 rounded-2xl text-sm font-bold <%= 
              case @rental_application.status
              when 'pending' then 'bg-amber-100 text-amber-800'
              when 'under_review' then 'bg-purple-100 text-purple-800'
              when 'approved' then 'bg-green-100 text-green-800'
              when 'rejected' then 'bg-red-100 text-red-800'
              else 'bg-gray-100 text-gray-800'
              end %>">
              <%= @rental_application.status.humanize %>
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Property Information -->
    <div class="mb-8">
      <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-6">
        <h3 class="text-xl font-bold text-gray-900 mb-4">Property Information</h3>
        
        <div class="flex items-center space-x-4">
          <% if @property.photos.attached? && @property.photos.any? %>
            <div class="w-20 h-20 rounded-2xl overflow-hidden flex-shrink-0">
              <%= image_tag @property.photos.first, 
                            class: "w-full h-full object-cover",
                            alt: @property.title %>
            </div>
          <% else %>
            <div class="w-20 h-20 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-2xl flex items-center justify-center flex-shrink-0">
              <svg class="w-10 h-10 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"></path>
              </svg>
            </div>
          <% end %>
          
          <div class="flex-1">
            <h4 class="text-lg font-bold text-gray-900">
              <%= link_to @property.title, property_path(@property), class: "hover:text-blue-600 transition-colors" %>
            </h4>
            <p class="text-gray-600"><%= @property.address %></p>
            <p class="text-lg font-bold text-blue-600">
              <%= number_to_currency(@property.price) %>/month
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Application Details -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
      <!-- Personal Information -->
      <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-6">
        <h3 class="text-xl font-bold text-gray-900 mb-4">Personal Information</h3>
        
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-bold text-gray-700 mb-1">Desired Move-in Date</label>
            <p class="text-gray-900"><%= @rental_application.move_in_date.strftime("%B %d, %Y") %></p>
          </div>
          
          <div>
            <label class="block text-sm font-bold text-gray-700 mb-1">Monthly Income</label>
            <p class="text-gray-900"><%= number_to_currency(@rental_application.monthly_income) %></p>
          </div>
          
          <div>
            <label class="block text-sm font-bold text-gray-700 mb-1">Employment Status</label>
            <p class="text-gray-900"><%= @rental_application.employment_status.humanize %></p>
          </div>
          
          <% if @rental_application.employer_name.present? %>
            <div>
              <label class="block text-sm font-bold text-gray-700 mb-1">Employer</label>
              <p class="text-gray-900"><%= @rental_application.employer_name %></p>
            </div>
          <% end %>
        </div>
      </div>

      <!-- Contact Information -->
      <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-6">
        <h3 class="text-xl font-bold text-gray-900 mb-4">Contact & References</h3>
        
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-bold text-gray-700 mb-1">Previous Address</label>
            <p class="text-gray-900 whitespace-pre-line"><%= @rental_application.previous_address %></p>
          </div>
          
          <div>
            <label class="block text-sm font-bold text-gray-700 mb-1">References</label>
            <p class="text-gray-900 whitespace-pre-line"><%= @rental_application.references_contact %></p>
          </div>
        </div>
      </div>
    </div>

    <!-- Additional Information -->
    <% if @rental_application.additional_notes.present? %>
      <div class="mb-8">
        <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-6">
          <h3 class="text-xl font-bold text-gray-900 mb-4">Additional Notes</h3>
          <p class="text-gray-900 whitespace-pre-line"><%= @rental_application.additional_notes %></p>
        </div>
      </div>
    <% end %>

    <!-- Review Notes (if any) -->
    <% if @rental_application.review_notes.present? %>
      <div class="mb-8">
        <div class="bg-blue-50 rounded-3xl p-6 border border-blue-100">
          <h3 class="text-xl font-bold text-blue-900 mb-4">Review Notes</h3>
          <p class="text-blue-800 whitespace-pre-line"><%= @rental_application.review_notes %></p>
          <% if @rental_application.reviewed_by.present? %>
            <p class="text-sm text-blue-600 mt-2">
              Reviewed by <%= @rental_application.reviewed_by.name || @rental_application.reviewed_by.email %>
              <% if @rental_application.reviewed_at.present? %>
                on <%= @rental_application.reviewed_at.strftime("%B %d, %Y at %I:%M %p") %>
              <% end %>
            </p>
          <% end %>
        </div>
      </div>
    <% end %>

    <!-- Action Buttons -->
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <% if @can_manage %>
          <!-- Landlord Actions -->
          <% if ['pending', 'under_review'].include?(@rental_application.status) %>
            <button onclick="openStatusModal('approve', <%= @rental_application.id %>)"
                    class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              Approve Application
            </button>

            <button onclick="openStatusModal('reject', <%= @rental_application.id %>)"
                    class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-700 hover:to-pink-700 text-white rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
              Reject Application
            </button>

            <% if @rental_application.status == 'pending' %>
              <button onclick="openStatusModal('under_review', <%= @rental_application.id %>)"
                      class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                Mark Under Review
              </button>
            <% end %>
          <% end %>

          <!-- Create Lease Button (for approved applications) -->
          <% if @rental_application.status == 'approved' && @rental_application.lease_agreement.blank? %>
            <%= link_to new_rental_application_lease_agreement_path(@rental_application),
                        class: "inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" do %>
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
              </svg>
              Create Lease Agreement
            <% end %>
          <% elsif @rental_application.status == 'approved' && @rental_application.lease_agreement.present? %>
            <%= link_to lease_agreement_path(@rental_application.lease_agreement),
                        class: "inline-flex items-center px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" do %>
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
              </svg>
              View Lease Agreement
            <% end %>
          <% end %>
        <% else %>
          <!-- Tenant Actions -->
          <% if ['pending', 'under_review'].include?(@rental_application.status) %>
            <%= link_to edit_rental_application_path(@rental_application), 
                        class: "inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" do %>
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
              </svg>
              Edit Application
            <% end %>
          <% end %>
        <% end %>
      </div>
      
      <div>
        <%= link_to property_path(@property), 
                    class: "inline-flex items-center px-6 py-3 border-2 border-gray-200 text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-300 rounded-2xl font-bold transition-all duration-300 transform hover:-translate-y-0.5" do %>
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"></path>
          </svg>
          View Property
        <% end %>
      </div>
    </div>
  </div>
</div>

<!-- Include Status Update Modal -->
<%= render 'status_update_modal' %>
