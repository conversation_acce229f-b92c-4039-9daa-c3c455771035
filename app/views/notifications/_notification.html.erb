<div class="notification-item <%= 'unread' unless notification.read? %>" data-notification-id="<%= notification.id %>">
  <div class="flex items-start space-x-3 p-4 hover:bg-gray-50 transition-colors duration-150">
    <!-- Notification Icon -->
    <div class="flex-shrink-0">
      <% case notification.notification_type %>
      <% when 'maintenance_request_new' %>
        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
          <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
          </svg>
        </div>
      <% when 'maintenance_request_status_change' %>
        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
          <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
      <% when 'maintenance_request_assigned' %>
        <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
          <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
          </svg>
        </div>
      <% when 'maintenance_request_completed' %>
        <div class="w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center">
          <svg class="w-4 h-4 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
        </div>
      <% when 'message' %>
        <div class="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
          <svg class="w-4 h-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
          </svg>
        </div>
      <% else %>
        <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
          <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM9 7H4l5-5v5zM12 12h.01"></path>
          </svg>
        </div>
      <% end %>
    </div>

    <!-- Notification Content -->
    <div class="flex-1 min-w-0">
      <div class="flex items-center justify-between">
        <p class="text-sm font-medium text-gray-900 truncate">
          <%= notification.title %>
        </p>
        <% unless notification.read? %>
          <span class="inline-block w-2 h-2 bg-blue-600 rounded-full"></span>
        <% end %>
      </div>
      
      <p class="text-sm text-gray-600 mt-1 line-clamp-2">
        <%= notification.message %>
      </p>
      
      <div class="flex items-center justify-between mt-2">
        <p class="text-xs text-gray-500">
          <%= notification.time_ago %>
        </p>
        
        <div class="flex space-x-2">
          <% if notification.url.present? %>
            <%= link_to "View", notification.url, 
                class: "text-xs text-blue-600 hover:text-blue-800 font-medium",
                data: { turbo_method: :get } %>
          <% end %>
          
          <% unless notification.read? %>
            <%= button_to "Mark Read", mark_read_notification_path(notification), 
                method: :patch,
                class: "text-xs text-gray-600 hover:text-gray-800 font-medium bg-transparent border-none p-0",
                data: { 
                  turbo_method: :patch,
                  turbo_stream: true
                } %>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>