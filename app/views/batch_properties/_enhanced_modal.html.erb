<%# Enhanced Property Actions Modal with Accessibility and Better UX %>

<!-- Modal Backdrop -->
<div data-batch-properties-target="modal" 
     class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50 hidden transition-opacity duration-300 ease-out"
     role="dialog" 
     aria-modal="true"
     aria-labelledby="modal-title"
     aria-describedby="modal-description">
  
  <!-- Modal Container -->
  <div class="flex items-center justify-center min-h-screen p-4">
    <div class="bg-white rounded-2xl shadow-2xl max-w-lg w-full mx-4 transform transition-all duration-300 ease-out scale-95 opacity-0"
         data-batch-properties-target="modalContent">
      
      <!-- Modal Header -->
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div class="flex-1">
            <h3 id="modal-title" 
                class="text-xl font-semibold text-gray-900" 
                data-batch-properties-target="modalTitle">
              Property Actions
            </h3>
            <p id="modal-description" 
               class="text-sm text-gray-600 mt-1" 
               data-batch-properties-target="modalSubtitle">
              Choose an action for this property
            </p>
          </div>
          
          <!-- Close Button with Accessibility -->
          <button type="button" 
                  data-action="click->batch-properties#closeModal"
                  class="ml-4 bg-gray-100 hover:bg-gray-200 rounded-full p-2 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  aria-label="Close modal">
            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
            </svg>
          </button>
        </div>
      </div>

      <!-- Modal Body -->
      <div class="px-6 py-4">
        
        <!-- Property Summary -->
        <div data-batch-properties-target="propertySummary" 
             class="bg-gray-50 rounded-xl p-4 mb-6">
          <div class="flex items-center space-x-3">
            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"/>
              </svg>
            </div>
            <div class="flex-1 min-w-0">
              <h4 class="text-sm font-medium text-gray-900 truncate" data-batch-properties-target="summaryTitle">
                Loading...
              </h4>
              <p class="text-sm text-gray-600" data-batch-properties-target="summaryStatus">
                Status: Loading...
              </p>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="space-y-3">
          
          <!-- View Property Button -->
          <button type="button"
                  data-batch-properties-target="viewPropertyBtn"
                  data-action="click->batch-properties#viewProperty"
                  class="w-full flex items-center px-4 py-3 border border-gray-300 rounded-xl hover:bg-blue-50 hover:border-blue-300 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 group">
            <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-blue-200 transition-colors duration-200">
              <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
              </svg>
            </div>
            <div class="flex-1 text-left">
              <div class="text-sm font-medium text-gray-900">View Property</div>
              <div class="text-xs text-gray-600">Open property details in new tab</div>
            </div>
            <svg class="w-4 h-4 text-gray-400 group-hover:text-blue-600 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
            </svg>
          </button>

          <!-- Edit Property Button -->
          <button type="button"
                  data-batch-properties-target="editPropertyBtn"
                  data-action="click->batch-properties#editProperty"
                  class="w-full flex items-center px-4 py-3 border border-gray-300 rounded-xl hover:bg-green-50 hover:border-green-300 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 group">
            <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-green-200 transition-colors duration-200">
              <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
              </svg>
            </div>
            <div class="flex-1 text-left">
              <div class="text-sm font-medium text-gray-900">Edit Property</div>
              <div class="text-xs text-gray-600">Modify property details</div>
            </div>
            <svg class="w-4 h-4 text-gray-400 group-hover:text-green-600 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
            </svg>
          </button>

          <!-- Retry Processing Button -->
          <button type="button"
                  data-batch-properties-target="retryItemBtn"
                  data-action="click->batch-properties#showRetryConfirmation"
                  class="w-full flex items-center px-4 py-3 border border-gray-300 rounded-xl hover:bg-orange-50 hover:border-orange-300 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 group hidden">
            <div class="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-orange-200 transition-colors duration-200">
              <svg class="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
              </svg>
            </div>
            <div class="flex-1 text-left">
              <div class="text-sm font-medium text-gray-900">Retry Processing</div>
              <div class="text-xs text-gray-600">Attempt to process this item again</div>
            </div>
          </button>

          <!-- View Details Button -->
          <button type="button"
                  data-action="click->batch-properties#toggleDetails"
                  class="w-full flex items-center px-4 py-3 border border-gray-300 rounded-xl hover:bg-purple-50 hover:border-purple-300 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 group">
            <div class="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3 group-hover:bg-purple-200 transition-colors duration-200">
              <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <div class="flex-1 text-left">
              <div class="text-sm font-medium text-gray-900">View Details</div>
              <div class="text-xs text-gray-600">Show property information</div>
            </div>
            <svg class="w-4 h-4 text-gray-400 group-hover:text-purple-600 transition-colors duration-200" 
                 data-batch-properties-target="detailsChevron" 
                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
            </svg>
          </button>

        </div>
      </div>

      <!-- Property Details Section (Expandable) -->
      <div data-batch-properties-target="propertyDetails" 
           class="hidden border-t border-gray-200 bg-gray-50">
        <div class="px-6 py-4">
          <h4 class="text-sm font-semibold text-gray-900 mb-4 flex items-center">
            <svg class="w-4 h-4 mr-2 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
            </svg>
            Property Information
          </h4>
          
          <!-- Loading State -->
          <div data-batch-properties-target="detailsLoading" class="hidden">
            <div class="animate-pulse space-y-3">
              <div class="h-4 bg-gray-300 rounded w-3/4"></div>
              <div class="h-4 bg-gray-300 rounded w-1/2"></div>
              <div class="h-4 bg-gray-300 rounded w-5/6"></div>
            </div>
          </div>
          
          <!-- Details Content -->
          <div data-batch-properties-target="propertyDetailsContent" 
               class="text-sm text-gray-700 space-y-3">
            <!-- Content populated by JavaScript -->
          </div>
          
          <!-- Error State -->
          <div data-batch-properties-target="detailsError" class="hidden">
            <div class="flex items-center text-red-600 text-sm">
              <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
              </svg>
              Failed to load property details
            </div>
          </div>
        </div>
      </div>

      <!-- Retry Confirmation Section -->
      <div data-batch-properties-target="retryConfirmation" 
           class="hidden border-t border-gray-200 bg-red-50">
        <div class="px-6 py-4">
          <div class="flex items-start">
            <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
              <svg class="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
              </svg>
            </div>
            <div class="flex-1">
              <h4 class="text-sm font-semibold text-red-900 mb-2">Confirm Retry</h4>
              <p class="text-sm text-red-800 mb-4">
                Are you sure you want to retry processing this item? This will reset its status and attempt to process it again.
              </p>
              <div class="flex space-x-3">
                <button type="button"
                        data-action="click->batch-properties#confirmRetry"
                        data-batch-properties-target="confirmRetryBtn"
                        class="bg-red-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-red-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2">
                  Yes, Retry
                </button>
                <button type="button"
                        data-action="click->batch-properties#cancelRetry"
                        class="bg-white text-gray-700 px-4 py-2 rounded-lg text-sm font-medium border border-gray-300 hover:bg-gray-50 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Debug Data (Development only) -->
      <% if Rails.env.development? %>
        <div class="border-t border-gray-200">
          <button type="button"
                  data-action="click->batch-properties#toggleDebugData"
                  class="w-full text-left px-6 py-3 text-xs text-gray-500 hover:bg-gray-50 transition-colors duration-200 focus:outline-none focus:bg-gray-50">
            <div class="flex items-center justify-between">
              <span>Toggle Debug Data</span>
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
              </svg>
            </div>
          </button>
          <div data-batch-properties-target="debugData" 
               class="hidden px-6 py-4 bg-gray-900 text-green-400 text-xs font-mono max-h-64 overflow-y-auto">
            <!-- Debug content populated by JavaScript -->
          </div>
        </div>
      <% end %>

    </div>
  </div>
</div>

<!-- Toast Notification Container -->
<div data-batch-properties-target="toastContainer" 
     class="fixed top-4 right-4 z-50 space-y-2">
  <!-- Toast notifications will be injected here -->
</div>
