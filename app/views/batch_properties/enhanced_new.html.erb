<% content_for :title, "Upload Batch Properties" %>

<div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50" data-controller="batch-upload">
  <!-- Header Section -->
  <div class="bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 text-white">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <div class="text-center">
        <h1 class="text-4xl font-bold mb-4">Upload Batch Properties</h1>
        <p class="text-xl text-blue-100">Upload multiple properties at once using a CSV file</p>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    
    <!-- Instructions with Enhanced Visual Design -->
    <div class="bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-white/20 p-8 mb-8">
      <h2 class="text-2xl font-bold text-gray-900 mb-6">How to Upload Properties</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="text-center group">
          <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
            <span class="text-2xl font-bold text-white">1</span>
          </div>
          <h3 class="text-lg font-semibold text-gray-900 mb-2">Download Template</h3>
          <p class="text-gray-600 mb-3">Download our CSV template with all required fields and example data</p>
          <%= link_to "Download Template", template_batch_properties_path(format: :csv),
                      class: "inline-flex items-center text-blue-600 hover:text-blue-800 font-medium transition-colors duration-200" do %>
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
            </svg>
            Download Template
          <% end %>
        </div>
        
        <div class="text-center group">
          <div class="w-16 h-16 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
            <span class="text-2xl font-bold text-white">2</span>
          </div>
          <h3 class="text-lg font-semibold text-gray-900 mb-2">Fill Your Data</h3>
          <p class="text-gray-600">Add your property information to the template. Each row represents one property</p>
        </div>
        
        <div class="text-center group">
          <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300">
            <span class="text-2xl font-bold text-white">3</span>
          </div>
          <h3 class="text-lg font-semibold text-gray-900 mb-2">Upload & Process</h3>
          <p class="text-gray-600">Upload your completed CSV file and we'll validate and create your properties</p>
        </div>
      </div>
    </div>

    <!-- Enhanced Upload Form -->
    <div class="bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-white/20 overflow-hidden">
      <div class="px-8 py-6 border-b border-gray-200">
        <h2 class="text-2xl font-bold text-gray-900">Upload CSV File</h2>
        <p class="text-gray-600 mt-1">Select your CSV file containing property data</p>
      </div>

      <div class="p-8">
        <!-- Enhanced File Upload Area with Better Validation -->
        <div class="border-2 border-dashed border-gray-300 rounded-2xl p-12 text-center hover:border-blue-400 transition-all duration-300 bg-gradient-to-br from-gray-50 to-gray-100 hover:from-blue-50 hover:to-indigo-50"
             data-batch-upload-target="dropZone"
             data-action="dragover->batch-upload#handleDragOver dragenter->batch-upload#handleDragEnter dragleave->batch-upload#handleDragLeave drop->batch-upload#handleDrop">
          
          <input type="file" 
                 accept=".csv"
                 class="hidden"
                 data-batch-upload-target="fileInput"
                 data-action="change->batch-upload#handleFileSelect">
          
          <!-- Upload Prompt State -->
          <div data-batch-upload-target="uploadPrompt">
            <div class="w-20 h-20 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
              <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
              </svg>
            </div>
            
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Drop your CSV file here</h3>
            <p class="text-gray-600 mb-6">or click to browse and select a file</p>
            
            <button type="button" 
                    class="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-8 py-3 rounded-2xl font-medium hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                    data-action="click->batch-upload#openFileDialog">
              Select CSV File
            </button>
            
            <div class="mt-6 flex items-center justify-center space-x-6 text-sm text-gray-500">
              <div class="flex items-center">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                CSV format only
              </div>
              <div class="flex items-center">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                Maximum 10MB
              </div>
            </div>
          </div>
          
          <!-- Enhanced File Selected State -->
          <div data-batch-upload-target="fileSelected" class="hidden">
            <div class="w-20 h-20 bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
              <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            
            <h3 class="text-xl font-semibold text-gray-900 mb-2">File Selected & Validated</h3>
            
            <!-- Enhanced File Information -->
            <div class="bg-white rounded-xl p-4 mb-6 text-left max-w-md mx-auto">
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-gray-700">Filename:</span>
                <span class="text-sm text-gray-900" data-batch-upload-target="fileName"></span>
              </div>
              <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-gray-700">Size:</span>
                <span class="text-sm text-gray-900" data-batch-upload-target="fileSize"></span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm font-medium text-gray-700">Type:</span>
                <span class="text-sm text-gray-900" data-batch-upload-target="fileType"></span>
              </div>
            </div>
            
            <!-- File Preview Section -->
            <div data-batch-upload-target="filePreview">
              <!-- Preview content will be populated by JavaScript -->
            </div>
            
            <div class="flex justify-center space-x-4">
              <button type="button" 
                      class="bg-gradient-to-r from-green-600 to-emerald-600 text-white px-8 py-3 rounded-2xl font-medium hover:from-green-700 hover:to-emerald-700 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                      data-action="click->batch-upload#uploadFile"
                      data-batch-upload-target="uploadButton">
                Upload & Validate
              </button>
              
              <button type="button" 
                      class="bg-gray-200 text-gray-700 px-8 py-3 rounded-2xl font-medium hover:bg-gray-300 transition-all duration-300"
                      data-action="click->batch-upload#clearFile">
                Choose Different File
              </button>
            </div>
          </div>
          
          <!-- Enhanced Upload Progress with Real Progress Tracking -->
          <div data-batch-upload-target="uploadProgress" class="hidden">
            <div class="w-20 h-20 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
              <svg class="w-10 h-10 text-white animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
            </div>
            
            <h3 class="text-xl font-semibold text-gray-900 mb-2">Uploading & Validating</h3>
            <p class="text-gray-600 mb-6">Please wait while we process your file...</p>
            
            <!-- Enhanced Progress Bar -->
            <div class="w-full bg-gray-200 rounded-full h-4 mb-4 overflow-hidden">
              <div class="bg-gradient-to-r from-blue-500 to-indigo-600 h-4 rounded-full transition-all duration-300 ease-out" 
                   style="width: 0%" 
                   data-batch-upload-target="progressBar"></div>
            </div>
            
            <p class="text-sm text-gray-600" data-batch-upload-target="progressText">Initializing...</p>
          </div>
        </div>

        <!-- Validation Results Section -->
        <div data-batch-upload-target="validationResults" class="hidden">
          <!-- Validation messages will be populated by JavaScript -->
        </div>

        <!-- Upload Results Section -->
        <div data-batch-upload-target="uploadResults" class="hidden mt-8">
          <!-- Results will be populated by JavaScript -->
        </div>
      </div>
    </div>

    <!-- Enhanced Tips Section with Better Organization -->
    <div class="bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-white/20 p-8 mt-8">
      <h2 class="text-2xl font-bold text-gray-900 mb-6">Tips for Success</h2>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div>
          <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <svg class="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
            Required Fields
          </h3>
          <div class="space-y-3">
            <div class="bg-green-50 border border-green-200 rounded-lg p-3">
              <h4 class="text-sm font-semibold text-green-800 mb-2">Basic Information</h4>
              <ul class="text-sm text-green-700 space-y-1">
                <li>• Title (property name)</li>
                <li>• Address, City, State</li>
                <li>• Property Type</li>
              </ul>
            </div>
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <h4 class="text-sm font-semibold text-blue-800 mb-2">Property Details</h4>
              <ul class="text-sm text-blue-700 space-y-1">
                <li>• Price (numbers only)</li>
                <li>• Bedrooms & Bathrooms</li>
                <li>• Square Feet (optional)</li>
              </ul>
            </div>
          </div>
        </div>
        
        <div>
          <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <svg class="w-5 h-5 text-blue-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
            </svg>
            Data Format Tips
          </h3>
          <div class="space-y-3">
            <div class="bg-amber-50 border border-amber-200 rounded-lg p-3">
              <h4 class="text-sm font-semibold text-amber-800 mb-2">Boolean Fields</h4>
              <p class="text-sm text-amber-700">Use <code class="bg-amber-100 px-1 rounded">true</code>/<code class="bg-amber-100 px-1 rounded">false</code> for parking, pets, furnished, etc.</p>
            </div>
            <div class="bg-purple-50 border border-purple-200 rounded-lg p-3">
              <h4 class="text-sm font-semibold text-purple-800 mb-2">Numbers & Text</h4>
              <ul class="text-sm text-purple-700 space-y-1">
                <li>• Numbers only for price and dimensions</li>
                <li>• Separate photo filenames with commas</li>
                <li>• No special characters in addresses</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- Common Issues Section -->
      <div class="mt-8 pt-6 border-t border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <svg class="w-5 h-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
          </svg>
          Common Issues to Avoid
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="text-center p-4 bg-red-50 border border-red-200 rounded-lg">
            <svg class="w-8 h-8 text-red-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"/>
            </svg>
            <h4 class="text-sm font-semibold text-red-800">Empty Rows</h4>
            <p class="text-xs text-red-600">Remove any blank rows from your CSV</p>
          </div>
          <div class="text-center p-4 bg-red-50 border border-red-200 rounded-lg">
            <svg class="w-8 h-8 text-red-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h3a1 1 0 011 1v2h4a1 1 0 011 1v1H3V5a1 1 0 011-1h4z"/>
            </svg>
            <h4 class="text-sm font-semibold text-red-800">Wrong Headers</h4>
            <p class="text-xs text-red-600">Use exact column names from template</p>
          </div>
          <div class="text-center p-4 bg-red-50 border border-red-200 rounded-lg">
            <svg class="w-8 h-8 text-red-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
            </svg>
            <h4 class="text-sm font-semibold text-red-800">Invalid Format</h4>
            <p class="text-xs text-red-600">Save as CSV format, not Excel</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
