<%# Property Table Row Component %>
<% property_data = JSON.parse(item.property_data) %>

<tr class="hover:bg-gray-50 transition-colors duration-200">
  <!-- Thumbnail -->
  <td class="px-6 py-4 whitespace-nowrap">
    <div class="flex items-center">
      <div class="flex-shrink-0 h-12 w-12">
        <div class="h-12 w-12 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
          <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"/>
          </svg>
        </div>
      </div>
      <div class="ml-4">
        <div class="text-sm font-medium text-gray-900">Row <%= item.row_number %></div>
        <div class="text-sm text-gray-500">ID: <%= item.id %></div>
      </div>
    </div>
  </td>

  <!-- Title & Description -->
  <td class="px-6 py-4">
    <div class="text-sm font-medium text-gray-900 max-w-xs">
      <%= truncate(property_data['title'], length: 40) %>
    </div>
    <div class="text-sm text-gray-500 max-w-xs">
      <%= truncate(property_data['description'], length: 60) %>
    </div>
  </td>

  <!-- Address -->
  <td class="px-6 py-4">
    <div class="text-sm text-gray-900 max-w-xs">
      <%= truncate(property_data['address'], length: 30) %>
    </div>
    <div class="text-sm text-gray-500">
      <%= property_data['city'] %>
    </div>
  </td>

  <!-- Price & Details -->
  <td class="px-6 py-4 whitespace-nowrap">
    <div class="text-sm font-semibold text-green-600">
      $<%= number_with_delimiter(property_data['price']) %>
    </div>
    <div class="text-sm text-gray-500">
      <%= property_data['bedrooms'] %>bd / <%= property_data['bathrooms'] %>ba
    </div>
  </td>

  <!-- Property Type -->
  <td class="px-6 py-4 whitespace-nowrap">
    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
      <%= property_data['property_type']&.humanize %>
    </span>
  </td>

  <!-- Status -->
  <td class="px-6 py-4 whitespace-nowrap">
    <%= render 'batch_properties/status_badge', item: item %>
  </td>

  <!-- Actions -->
  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
    <div class="flex items-center justify-end space-x-2">
      <!-- Quick Actions for Completed Items -->
      <% if item.property_id %>
        <%= link_to property_path(item.property_id),
            target: "_blank",
            class: "text-blue-600 hover:text-blue-900 transition-colors duration-200",
            title: "View Property" do %>
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
          </svg>
        <% end %>
      <% end %>

      <!-- 3-Dots Menu -->
      <button type="button"
              class="text-gray-400 hover:text-gray-600 transition-colors duration-200 p-1 rounded-full hover:bg-gray-100"
              data-action="click->batch-properties#openModal"
              data-item-id="<%= item.id %>"
              data-property-id="<%= item.property_id %>"
              data-title="<%= j(property_data['title']) %>"
              data-status="<%= item.status %>"
              title="More Actions">
        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
          <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
        </svg>
      </button>
    </div>

    <!-- Error Message (if any) -->
    <% if item.error_message %>
      <div class="mt-2 text-xs text-red-600 max-w-xs">
        <%= truncate(item.error_message, length: 100) %>
      </div>
    <% end %>
  </td>
</tr>
