<%# Upload Row Component for Enhanced Table %>

<tr class="hover:bg-gray-50 transition-colors duration-200" 
    data-upload-id="<%= upload.id %>"
    data-batch-progress-target="uploadRow">
  
  <!-- File Information -->
  <td class="px-6 py-4 whitespace-nowrap">
    <div class="flex items-center">
      <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        </svg>
      </div>
      <div class="ml-4">
        <div class="text-sm font-medium text-gray-900"><%= upload.filename %></div>
        <div class="text-sm text-gray-500"><%= pluralize(upload.total_items, 'property') %></div>
      </div>
    </div>
  </td>
  
  <!-- Enhanced Status with Real-time Updates -->
  <td class="px-6 py-4 whitespace-nowrap" data-batch-progress-target="statusCell">
    <%= render 'batch_properties/status_badge_enhanced', upload: upload %>
  </td>
  
  <!-- Enhanced Progress Bar -->
  <td class="px-6 py-4 whitespace-nowrap">
    <div class="flex items-center">
      <div class="w-full bg-gray-200 rounded-full h-2 mr-3">
        <div class="h-2 rounded-full transition-all duration-500 ease-out
                    <%= upload.status == 'completed' ? 'bg-gradient-to-r from-green-500 to-emerald-600' :
                        upload.status == 'failed' ? 'bg-gradient-to-r from-red-500 to-pink-600' :
                        upload.status == 'processing' ? 'bg-gradient-to-r from-blue-500 to-indigo-600' :
                        'bg-gradient-to-r from-yellow-500 to-amber-600' %>" 
             style="width: <%= upload.progress_percentage %>%"
             data-batch-progress-target="progressBar"></div>
      </div>
      <div class="text-xs text-gray-500 min-w-0 w-12" data-batch-progress-target="progressText">
        <%= upload.progress_percentage %>%
      </div>
    </div>
    
    <!-- Processing indicator for active uploads -->
    <% if upload.status == 'processing' %>
      <div class="mt-1 flex items-center text-xs text-blue-600">
        <div class="w-2 h-2 bg-blue-400 rounded-full animate-pulse mr-1"></div>
        <span>Processing...</span>
      </div>
    <% end %>
  </td>
  
  <!-- Enhanced Results with Animations -->
  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
    <div class="flex space-x-4">
      <div class="flex items-center" data-batch-progress-target="successCount">
        <svg class="w-4 h-4 text-green-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
        </svg>
        <span class="text-green-600 font-medium"><%= upload.successful_items || 0 %></span>
      </div>
      <div class="flex items-center" data-batch-progress-target="failedCount">
        <svg class="w-4 h-4 text-red-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
        </svg>
        <span class="text-red-600 font-medium"><%= upload.failed_items || 0 %></span>
      </div>
    </div>
  </td>
  
  <!-- Date with Relative Time -->
  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
    <div title="<%= upload.created_at.strftime('%B %d, %Y at %I:%M %p') %>">
      <%= time_ago_in_words(upload.created_at) %> ago
    </div>
  </td>
  
  <!-- Enhanced Actions with Better UX -->
  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
    <div class="flex space-x-2">
      <!-- View Action -->
      <%= link_to preview_batch_property_path(upload),
                  class: "inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-sm leading-4 font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200",
                  title: "View batch details" do %>
        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
        </svg>
        View
      <% end %>

      <!-- Process Action (conditional) -->
      <% if upload.status == 'validated' && upload.valid_items && upload.valid_items > 0 %>
        <%= button_to process_batch_property_path(upload),
                      method: :post,
                      class: "inline-flex items-center px-3 py-1 border border-transparent rounded-md text-sm leading-4 font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200",
                      data: { 
                        confirm: "Are you sure you want to process this batch? This will create #{upload.valid_items} properties.",
                        disable_with: "Processing..."
                      },
                      title: "Start processing batch" do %>
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h8m-9-4.5a9 9 0 1118 0A9 9 0 013 5.5z"/>
          </svg>
          Process
        <% end %>
      <% end %>

      <!-- Retry Action (for failed batches) -->
      <% if upload.status == 'failed' %>
        <%= button_to retry_failed_batch_property_path(upload),
                      method: :post,
                      class: "inline-flex items-center px-3 py-1 border border-transparent rounded-md text-sm leading-4 font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-colors duration-200",
                      data: { 
                        confirm: "Retry processing this batch?",
                        disable_with: "Retrying..."
                      },
                      title: "Retry failed processing" do %>
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
          </svg>
          Retry
        <% end %>
      <% end %>

      <!-- Delete Action (conditional) -->
      <% if upload.status != 'processing' %>
        <%= button_to batch_property_path(upload),
                      method: :delete,
                      class: "inline-flex items-center px-3 py-1 border border-red-300 rounded-md text-sm leading-4 font-medium text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200",
                      data: { 
                        confirm: "Are you sure you want to delete this upload? This action cannot be undone.",
                        disable_with: "Deleting..."
                      },
                      title: "Delete this upload" do %>
          <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
          </svg>
          Delete
        <% end %>
      <% end %>
    </div>
  </td>
</tr>
