<% content_for :title, "Batch Upload Preview" %>

<div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 py-8">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold text-gray-900">Batch Upload Preview</h1>
          <p class="mt-2 text-gray-600">Review your property data before processing</p>
        </div>
        
        <%= link_to batch_properties_path, 
            class: "inline-flex items-center px-4 py-2 border border-gray-300 rounded-xl text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200" do %>
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
          </svg>
          Back to Batch Properties
        <% end %>
      </div>
    </div>

    <!-- Upload Summary Card -->
    <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl border border-white/20 p-8 mb-8">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-xl font-semibold text-gray-900">Upload Summary</h2>
        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                     <%= @batch_upload.status == 'validated' ? 'bg-green-100 text-green-800' : 
                         @batch_upload.status == 'processing' ? 'bg-blue-100 text-blue-800' :
                         @batch_upload.status == 'completed' ? 'bg-green-100 text-green-800' :
                         @batch_upload.status == 'failed' ? 'bg-red-100 text-red-800' :
                         'bg-gray-100 text-gray-800' %>">
          <%= @batch_upload.status.humanize %>
        </span>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="text-center">
          <div class="text-3xl font-bold text-blue-600"><%= @batch_upload.total_items %></div>
          <div class="text-sm text-gray-600">Total Properties</div>
        </div>
        <div class="text-center">
          <div class="text-3xl font-bold text-green-600"><%= @batch_upload.valid_items %></div>
          <div class="text-sm text-gray-600">Valid Properties</div>
        </div>
        <div class="text-center">
          <div class="text-3xl font-bold text-red-600"><%= @batch_upload.invalid_items %></div>
          <div class="text-sm text-gray-600">Invalid Properties</div>
        </div>
        <div class="text-center">
          <div class="text-3xl font-bold text-purple-600"><%= @batch_upload.processed_items || 0 %></div>
          <div class="text-sm text-gray-600">Processed</div>
        </div>
      </div>

      <div class="mt-6 pt-6 border-t border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-sm text-gray-600">
              <strong>Filename:</strong> <%= @batch_upload.filename %>
            </p>
            <p class="text-sm text-gray-600">
              <strong>Uploaded:</strong> <%= @batch_upload.created_at.strftime("%B %d, %Y at %I:%M %p") %>
            </p>
          </div>
          
          <div class="flex space-x-3">
            <% if @batch_upload.validated? && @batch_upload.valid_items > 0 %>
              <%= button_to "Process Batch",
                  process_batch_batch_property_path(@batch_upload),
                  method: :post,
                  class: "inline-flex items-center px-6 py-3 border border-transparent rounded-xl text-sm font-medium text-white bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl",
                  data: { confirm: "Are you sure you want to process this batch? This will create #{@batch_upload.valid_items} properties." } %>
            <% elsif @batch_upload.processing? && @batch_upload.valid_items > 0 %>
              <%= button_to "Fix Status",
                  fix_status_batch_property_path(@batch_upload),
                  method: :post,
                  class: "inline-flex items-center px-6 py-3 border border-transparent rounded-xl text-sm font-medium text-white bg-gradient-to-r from-amber-600 to-orange-600 hover:from-amber-700 hover:to-orange-700 transition-all duration-200 shadow-lg hover:shadow-xl",
                  data: { confirm: "This will fix the batch upload status so it can be processed. Continue?" } %>
            <% end %>
          </div>
        </div>
      </div>
    </div>

    <!-- Property Items -->
    <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl border border-white/20 overflow-hidden">
      <div class="px-8 py-6 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Property Details</h3>
        <p class="text-sm text-gray-600">Review each property before processing</p>
      </div>

      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Row</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Address</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <% @batch_items.each do |item| %>
              <% property_data = JSON.parse(item.property_data) %>
              <tr class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  <%= item.row_number %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900"><%= property_data['title'] %></div>
                  <div class="text-sm text-gray-500"><%= truncate(property_data['description'], length: 50) %></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900"><%= property_data['address'] %></div>
                  <div class="text-sm text-gray-500"><%= property_data['city'] %></div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  $<%= number_with_delimiter(property_data['price']) %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    <%= property_data['property_type']&.humanize %>
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                               <%= item.status == 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                   item.status == 'processed' ? 'bg-green-100 text-green-800' :
                                   item.status == 'failed' ? 'bg-red-100 text-red-800' :
                                   'bg-gray-100 text-gray-800' %>">
                    <%= item.status.humanize %>
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <% if item.property_id %>
                    <%= link_to "View Property", property_path(item.property_id), 
                        class: "text-blue-600 hover:text-blue-900", target: "_blank" %>
                  <% elsif item.error_message %>
                    <span class="text-red-600" title="<%= item.error_message %>">Error</span>
                  <% else %>
                    <span class="text-gray-400">Pending</span>
                  <% end %>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <% if @batch_items.respond_to?(:total_pages) && @batch_items.total_pages > 1 %>
        <div class="px-8 py-6 border-t border-gray-200">
          <div class="flex items-center justify-between">
            <div class="text-sm text-gray-700">
              Showing <%= @batch_items.offset_value + 1 %> to <%= [@batch_items.offset_value + @batch_items.limit_value, @batch_items.total_count].min %> of <%= @batch_items.total_count %> results
            </div>

            <div class="flex items-center space-x-2">
              <% if @batch_items.prev_page %>
                <%= link_to preview_batch_property_path(@batch_upload, page: @batch_items.prev_page),
                    class: "relative inline-flex items-center px-3 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 rounded-md" do %>
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                  </svg>
                  Previous
                <% end %>
              <% end %>

              <% (@batch_items.current_page - 2..@batch_items.current_page + 2).each do |page| %>
                <% if page > 0 && page <= @batch_items.total_pages %>
                  <% if page == @batch_items.current_page %>
                    <span class="relative inline-flex items-center px-4 py-2 border border-blue-500 bg-blue-50 text-sm font-medium text-blue-600 rounded-md">
                      <%= page %>
                    </span>
                  <% else %>
                    <%= link_to page, preview_batch_property_path(@batch_upload, page: page),
                        class: "relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 rounded-md" %>
                  <% end %>
                <% end %>
              <% end %>

              <% if @batch_items.next_page %>
                <%= link_to preview_batch_property_path(@batch_upload, page: @batch_items.next_page),
                    class: "relative inline-flex items-center px-3 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 rounded-md" do %>
                  Next
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                  </svg>
                <% end %>
              <% end %>
            </div>
          </div>
        </div>
      <% end %>
    </div>

    <!-- Error Messages (if any) -->
    <% if @batch_upload.error_message.present? %>
      <div class="mt-8 bg-red-50 border border-red-200 rounded-xl p-6">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">Processing Error</h3>
            <div class="mt-2 text-sm text-red-700">
              <%= @batch_upload.error_message %>
            </div>
          </div>
        </div>
      </div>
    <% end %>
  </div>
</div>
