<!-- Enhanced Comments Section -->
<div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-8">
  <div class="flex items-center justify-between mb-8">
    <div class="flex items-center space-x-3">
      <div class="w-10 h-10 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center">
        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
        </svg>
      </div>
      <div>
        <h3 class="text-3xl font-bold text-gray-900">Discussion</h3>
        <p class="text-gray-600 font-medium">
          Ask questions and share thoughts about this property
        </p>
      </div>
    </div>

    <% if user_signed_in? %>
      <button class="group inline-flex items-center px-6 py-3 bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              onclick="toggleCommentForm()">
        <svg class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        Add Comment
      </button>
    <% end %>
  </div>

  <!-- Comment Form -->
  <% if user_signed_in? %>
    <div id="commentForm" class="mb-8 hidden">
      <%= form_with model: [@property, PropertyComment.new],
                    url: property_property_comments_path(@property),
                    local: false,
                    class: "space-y-4",
                    id: "new-comment-form",
                    data: { action: "submit->comments#submitComment" } do |form| %>

        <div class="bg-gradient-to-r from-emerald-50 to-teal-50 rounded-2xl p-6 border border-emerald-200">
          <div class="flex items-start space-x-4">
            <div class="w-12 h-12 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center text-white font-bold text-lg flex-shrink-0">
              <%= (current_user.name || current_user.email).first.upcase %>
            </div>
            <div class="flex-1">
              <%= form.text_area :content,
                                 placeholder: "Ask a question or share your thoughts about this property...",
                                 rows: 4,
                                 class: "w-full px-4 py-3 border border-emerald-300 rounded-2xl focus:ring-2 focus:ring-emerald-500 focus:border-transparent resize-none text-gray-900 placeholder-gray-500",
                                 maxlength: 2000,
                                 oninput: "updateCharacterCount(this)" %>
              <div class="flex items-center justify-between mt-3">
                <div class="text-sm text-gray-500">
                  <span id="characterCount">0</span>/2000 characters
                </div>
                <div class="flex space-x-3">
                  <button type="button"
                          class="px-4 py-2 text-gray-600 hover:text-gray-800 font-medium transition-colors"
                          onclick="toggleCommentForm()">
                    Cancel
                  </button>
                  <%= form.submit "Post Comment",
                                  class: "px-6 py-2 bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" %>
                </div>
              </div>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  <% else %>
    <div class="mb-8 text-center p-6 bg-gradient-to-r from-emerald-50 to-teal-50 rounded-2xl border border-emerald-200">
      <div class="w-16 h-16 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-3xl flex items-center justify-center mx-auto mb-4">
        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
        </svg>
      </div>
      <p class="text-emerald-800 font-semibold mb-4">Sign in to join the discussion</p>
      <%= link_to login_path, class: "inline-block bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white px-8 py-3 rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" do %>
        Sign In to Comment
      <% end %>
    </div>
  <% end %>

  <!-- Comments List -->
  <div class="space-y-6" data-comments-target="commentsList">
    <% if @property.property_comments.not_flagged.any? %>
      <% @property.recent_comments(10).each do |comment| %>
        <%= render 'property_comments/comment', comment: comment %>
      <% end %>
      
      <% if @property.comments_count > 10 %>
        <div class="text-center pt-6">
          <%= link_to property_property_comments_path(@property), 
                      class: "inline-flex items-center px-6 py-3 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" do %>
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
            </svg>
            View All <%= @property.comments_count %> Comments
          <% end %>
        </div>
      <% end %>
    <% else %>
      <div class="text-center py-12">
        <div class="w-20 h-20 bg-gradient-to-r from-gray-200 to-gray-300 rounded-3xl flex items-center justify-center mx-auto mb-6">
          <svg class="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
          </svg>
        </div>
        <h4 class="text-xl font-bold text-gray-600 mb-2">No comments yet</h4>
        <p class="text-gray-500">Be the first to share your thoughts about this property!</p>
      </div>
    <% end %>
  </div>
</div>

<script>
function toggleCommentForm() {
  const form = document.getElementById('commentForm');
  form.classList.toggle('hidden');
  if (!form.classList.contains('hidden')) {
    form.querySelector('textarea').focus();
  }
}

function updateCharacterCount(textarea) {
  const count = textarea.value.length;
  document.getElementById('characterCount').textContent = count;

  // Change color based on character count
  const counter = document.getElementById('characterCount');
  if (count > 1800) {
    counter.className = 'text-red-500 font-bold';
  } else if (count > 1500) {
    counter.className = 'text-amber-500 font-bold';
  } else {
    counter.className = 'text-gray-500';
  }
}

// Handle form submissions with AJAX
document.addEventListener('DOMContentLoaded', function() {
  // Handle main comment form
  const mainForm = document.getElementById('new-comment-form');
  if (mainForm) {
    mainForm.addEventListener('submit', function(e) {
      e.preventDefault();
      submitComment(this, false);
    });
  }

  // Handle reply forms
  document.addEventListener('submit', function(e) {
    if (e.target.classList.contains('reply-form')) {
      e.preventDefault();
      const commentId = e.target.dataset.commentId;
      submitComment(e.target, commentId);
    }
  });
});

function submitComment(form, parentCommentId) {
  const formData = new FormData(form);
  const submitButton = form.querySelector('input[type="submit"]');
  const textarea = form.querySelector('textarea');

  // Disable submit button
  if (submitButton) {
    submitButton.disabled = true;
    submitButton.value = 'Posting...';
  }

  // Get CSRF token
  const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

  fetch(form.action, {
    method: 'POST',
    headers: {
      'X-CSRF-Token': token,
      'Accept': 'application/json'
    },
    body: formData
  })
  .then(response => response.json())
  .then(data => {
    if (data.comment) {
      // Success - add comment to DOM
      addCommentToDOM(data.comment, parentCommentId);

      // Clear form
      textarea.value = '';
      updateCharacterCount(textarea);

      // Hide form if it's a reply form
      if (parentCommentId) {
        toggleReplyForm(parentCommentId);
      } else {
        toggleCommentForm();
      }

      // Show success message
      showNotification('Comment posted successfully!', 'success');
    } else {
      throw new Error(data.error || 'Failed to post comment');
    }
  })
  .catch(error => {
    console.error('Error posting comment:', error);
    showNotification('Failed to post comment. Please try again.', 'error');
  })
  .finally(() => {
    // Re-enable submit button
    if (submitButton) {
      submitButton.disabled = false;
      submitButton.value = parentCommentId ? 'Reply' : 'Post Comment';
    }
  });
}

function addCommentToDOM(commentData, parentCommentId) {
  const commentHTML = createCommentHTML(commentData, parentCommentId);

  if (parentCommentId) {
    // Add as reply
    const parentComment = document.querySelector(`[data-comment-id="${parentCommentId}"]`);
    if (parentComment) {
      let repliesContainer = parentComment.querySelector('.replies-container');
      if (!repliesContainer) {
        repliesContainer = document.createElement('div');
        repliesContainer.className = 'replies-container mt-6 space-y-4';
        parentComment.appendChild(repliesContainer);
      }
      repliesContainer.insertAdjacentHTML('beforeend', commentHTML);
    }
  } else {
    // Add as top-level comment
    const commentsList = document.querySelector('[data-comments-target="commentsList"]') ||
                         document.querySelector('.space-y-6');
    if (commentsList) {
      commentsList.insertAdjacentHTML('afterbegin', commentHTML);
    }
  }
}

function createCommentHTML(commentData, isReply = false) {
  const containerClass = isReply ?
    'ml-6 pl-4 border-l-2 border-gray-200' :
    'bg-gradient-to-r from-gray-50 to-white rounded-2xl p-6 border border-gray-200 hover:shadow-lg transition-all duration-300';

  return `
    <div class="${containerClass}" data-comment-id="${commentData.id}">
      <div class="flex items-start space-x-4">
        <div class="w-${isReply ? '8 h-8' : '12 h-12'} bg-gradient-to-br from-blue-500 to-purple-600 rounded-${isReply ? 'xl' : '2xl'} flex items-center justify-center text-white font-bold text-${isReply ? 'sm' : 'lg'} flex-shrink-0">
          ${commentData.user.name.charAt(0).toUpperCase()}
        </div>
        <div class="flex-1 min-w-0">
          <div class="flex items-center space-x-3 mb-3">
            <h4 class="font-bold text-gray-900 ${isReply ? 'text-sm' : ''}">${commentData.user.name}</h4>
            <span class="text-${isReply ? 'xs' : 'sm'} text-gray-500">just now</span>
          </div>
          <div class="prose prose-gray max-w-none mb-4">
            <p class="text-gray-800 leading-relaxed ${isReply ? 'text-sm' : ''}">${commentData.content}</p>
          </div>
        </div>
      </div>
    </div>
  `;
}

function showNotification(message, type) {
  const notification = document.createElement('div');
  notification.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-2xl font-bold text-white shadow-lg transform transition-all duration-300 ${
    type === 'success' ? 'bg-green-500' : 'bg-red-500'
  }`;
  notification.textContent = message;

  document.body.appendChild(notification);

  // Animate in
  setTimeout(() => {
    notification.style.transform = 'translateX(0)';
  }, 100);

  // Remove after 3 seconds
  setTimeout(() => {
    notification.style.transform = 'translateX(100%)';
    setTimeout(() => {
      document.body.removeChild(notification);
    }, 300);
  }, 3000);
}
</script>
