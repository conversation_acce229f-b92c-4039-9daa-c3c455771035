<!-- Edit Property Comment Page -->
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
  <!-- Header -->
  <div class="bg-white/80 backdrop-blur-xl border-b border-gray-200/50 sticky top-0 z-10">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <%= link_to property_comment_path(@comment), 
                      class: "inline-flex items-center px-4 py-2 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" do %>
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Comment
          <% end %>
          
          <div>
            <h1 class="text-2xl font-bold text-gray-900">Edit Comment</h1>
            <p class="text-gray-600">
              On <%= link_to @comment.property.title, property_path(@comment.property), class: "text-blue-600 hover:text-blue-800 font-medium" %>
            </p>
          </div>
        </div>
        
        <div class="text-sm text-amber-600 bg-amber-50 px-3 py-2 rounded-xl border border-amber-200">
          <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          You can edit comments within 15 minutes of posting
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Edit Form -->
    <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-8">
      <div class="flex items-center space-x-3 mb-8">
        <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center">
          <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
          </svg>
        </div>
        <h2 class="text-3xl font-bold text-gray-900">Edit Your Comment</h2>
      </div>

      <%= form_with model: @comment, 
                    url: property_comment_path(@comment),
                    method: :patch,
                    local: true,
                    class: "space-y-6" do |form| %>
        
        <!-- Display any errors -->
        <% if @comment.errors.any? %>
          <div class="bg-red-50 border border-red-200 rounded-2xl p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">
                  There were some problems with your comment:
                </h3>
                <div class="mt-2 text-sm text-red-700">
                  <ul class="list-disc pl-5 space-y-1">
                    <% @comment.errors.full_messages.each do |message| %>
                      <li><%= message %></li>
                    <% end %>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        <% end %>

        <!-- Comment Form -->
        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-200">
          <div class="flex items-start space-x-4">
            <!-- User Avatar -->
            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center text-white font-bold text-lg flex-shrink-0">
              <%= (current_user.name || current_user.email).first.upcase %>
            </div>
            
            <!-- Form Content -->
            <div class="flex-1">
              <div class="mb-4">
                <label for="property_comment_content" class="block text-sm font-medium text-gray-700 mb-2">
                  Your Comment
                </label>
                <%= form.text_area :content, 
                                   rows: 6,
                                   class: "w-full px-4 py-3 border border-blue-300 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none text-gray-900 placeholder-gray-500",
                                   maxlength: 2000,
                                   oninput: "updateCharacterCount(this)" %>
              </div>
              
              <!-- Character Count -->
              <div class="flex items-center justify-between mb-6">
                <div class="text-sm text-gray-500">
                  <span id="characterCount"><%= @comment.content.length %></span>/2000 characters
                </div>
                <div class="text-xs text-gray-400">
                  <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  This comment will be marked as edited
                </div>
              </div>
              
              <!-- Action Buttons -->
              <div class="flex items-center justify-end space-x-4">
                <%= link_to "Cancel", property_comment_path(@comment), 
                            class: "px-6 py-3 text-gray-600 hover:text-gray-800 font-medium transition-colors" %>
                
                <%= form.submit "Update Comment", 
                                class: "px-8 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" %>
              </div>
            </div>
          </div>
        </div>
      <% end %>
    </div>

    <!-- Original Comment Preview -->
    <div class="bg-white/60 backdrop-blur-xl rounded-3xl shadow-lg border border-white/20 p-8 mt-8">
      <div class="flex items-center space-x-3 mb-6">
        <div class="w-8 h-8 bg-gradient-to-r from-gray-500 to-gray-600 rounded-2xl flex items-center justify-center">
          <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
          </svg>
        </div>
        <h3 class="text-xl font-bold text-gray-700">Original Comment</h3>
        <span class="text-sm text-gray-500">Posted <%= time_ago_in_words(@comment.created_at) %> ago</span>
      </div>
      
      <div class="bg-gradient-to-r from-gray-50 to-white rounded-2xl p-6 border border-gray-200">
        <p class="text-gray-700 leading-relaxed"><%= simple_format(@comment.content) %></p>
      </div>
    </div>
  </div>
</div>

<script>
function updateCharacterCount(textarea) {
  const count = textarea.value.length;
  document.getElementById('characterCount').textContent = count;
  
  // Change color based on character count
  const counter = document.getElementById('characterCount');
  if (count > 1800) {
    counter.className = 'text-red-500 font-bold';
  } else if (count > 1500) {
    counter.className = 'text-amber-500 font-bold';
  } else {
    counter.className = 'text-gray-500';
  }
}

// Initialize character count on page load
document.addEventListener('DOMContentLoaded', function() {
  const textarea = document.querySelector('textarea[name="property_comment[content]"]');
  if (textarea) {
    updateCharacterCount(textarea);
  }
});
</script>
