<!-- Property Comments Index Page -->
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
  <!-- Header -->
  <div class="bg-white/80 backdrop-blur-xl border-b border-gray-200/50 sticky top-0 z-10">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <%= link_to property_path(@property), 
                      class: "inline-flex items-center px-4 py-2 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" do %>
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Property
          <% end %>
          
          <div>
            <h1 class="text-3xl font-bold text-gray-900">All Comments</h1>
            <p class="text-gray-600">
              Discussion about <%= link_to @property.title, property_path(@property), class: "text-blue-600 hover:text-blue-800 font-medium" %>
            </p>
          </div>
        </div>
        
        <div class="flex items-center space-x-4">
          <div class="text-sm text-gray-600 bg-white/60 px-4 py-2 rounded-xl border border-gray-200">
            <%= pluralize(@property.comments_count, 'comment') %> total
          </div>
          
          <% if user_signed_in? %>
            <button class="group inline-flex items-center px-6 py-3 bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                    onclick="toggleCommentForm()">
              <svg class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              Add Comment
            </button>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Comment Form -->
    <% if user_signed_in? %>
      <div id="commentForm" class="mb-8 hidden">
        <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-8">
          <div class="flex items-center space-x-3 mb-6">
            <div class="w-10 h-10 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
            </div>
            <h2 class="text-2xl font-bold text-gray-900">Add Your Comment</h2>
          </div>
          
          <%= form_with model: [@property, @new_comment], 
                        url: property_property_comments_path(@property),
                        local: true, 
                        class: "space-y-4" do |form| %>
            
            <div class="bg-gradient-to-r from-emerald-50 to-teal-50 rounded-2xl p-6 border border-emerald-200">
              <div class="flex items-start space-x-4">
                <div class="w-12 h-12 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center text-white font-bold text-lg flex-shrink-0">
                  <%= (current_user.name || current_user.email).first.upcase %>
                </div>
                <div class="flex-1">
                  <%= form.text_area :content, 
                                     placeholder: "Ask a question or share your thoughts about this property...",
                                     rows: 4,
                                     class: "w-full px-4 py-3 border border-emerald-300 rounded-2xl focus:ring-2 focus:ring-emerald-500 focus:border-transparent resize-none text-gray-900 placeholder-gray-500",
                                     maxlength: 2000,
                                     oninput: "updateCharacterCount(this)" %>
                  <div class="flex items-center justify-between mt-3">
                    <div class="text-sm text-gray-500">
                      <span id="characterCount">0</span>/2000 characters
                    </div>
                    <div class="flex space-x-3">
                      <button type="button" 
                              class="px-4 py-2 text-gray-600 hover:text-gray-800 font-medium transition-colors"
                              onclick="toggleCommentForm()">
                        Cancel
                      </button>
                      <%= form.submit "Post Comment", 
                                      class: "px-6 py-2 bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" %>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      </div>
    <% else %>
      <div class="mb-8">
        <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-8 text-center">
          <div class="w-16 h-16 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-3xl flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
            </svg>
          </div>
          <p class="text-emerald-800 font-semibold mb-4">Sign in to join the discussion</p>
          <%= link_to login_path, class: "inline-block bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white px-8 py-3 rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" do %>
            Sign In to Comment
          <% end %>
        </div>
      </div>
    <% end %>

    <!-- Comments List -->
    <div class="space-y-8">
      <% if @comments.any? %>
        <% @comments.each do |comment| %>
          <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-8">
            <%= render 'property_comments/comment', comment: comment %>
          </div>
        <% end %>
        
        <!-- Load More Button (if there are more comments) -->
        <% if @property.comments_count > @comments.count %>
          <div class="text-center pt-8">
            <div class="bg-white/60 backdrop-blur-xl rounded-2xl p-6 border border-white/20">
              <p class="text-gray-600 mb-4">
                Showing <%= @comments.count %> of <%= @property.comments_count %> comments
              </p>
              <button class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                      onclick="loadMoreComments()">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Load More Comments
              </button>
            </div>
          </div>
        <% end %>
      <% else %>
        <div class="text-center py-16">
          <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-12">
            <div class="w-24 h-24 bg-gradient-to-r from-emerald-200 to-teal-300 rounded-3xl flex items-center justify-center mx-auto mb-8">
              <svg class="w-12 h-12 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
              </svg>
            </div>
            <h3 class="text-2xl font-bold text-gray-600 mb-4">No comments yet</h3>
            <p class="text-gray-500 text-lg mb-8">Be the first to start a conversation about this property!</p>
            
            <% if user_signed_in? %>
              <button class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                      onclick="toggleCommentForm()">
                <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Write the First Comment
              </button>
            <% else %>
              <%= link_to login_path, class: "inline-flex items-center px-8 py-4 bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" do %>
                <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
                Sign In to Comment
              <% end %>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>

<script>
function toggleCommentForm() {
  const form = document.getElementById('commentForm');
  if (form) {
    form.classList.toggle('hidden');
    if (!form.classList.contains('hidden')) {
      const textarea = form.querySelector('textarea');
      if (textarea) {
        textarea.focus();
      }
    }
  }
}

function updateCharacterCount(textarea) {
  const count = textarea.value.length;
  document.getElementById('characterCount').textContent = count;
  
  // Change color based on character count
  const counter = document.getElementById('characterCount');
  if (count > 1800) {
    counter.className = 'text-red-500 font-bold';
  } else if (count > 1500) {
    counter.className = 'text-amber-500 font-bold';
  } else {
    counter.className = 'text-gray-500';
  }
}

function loadMoreComments() {
  // This would implement pagination/infinite scroll
  alert('Load more functionality would be implemented here');
}
</script>
