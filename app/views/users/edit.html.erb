<% content_for :title, "Edit Profile" %>

<div class="min-h-screen bg-gray-50 py-8">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Breadcrumb -->
    <nav class="flex mb-8" aria-label="Breadcrumb">
      <ol class="inline-flex items-center space-x-1 md:space-x-3">
        <li class="inline-flex items-center">
          <%= link_to root_path, class: "inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600" do %>
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
            </svg>
            Home
          <% end %>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
            </svg>
            <%= link_to profile_path, class: "ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2" do %>
              Profile
            <% end %>
          </div>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
            </svg>
            <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">Edit</span>
          </div>
        </li>
      </ol>
    </nav>

    <!-- Header -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900">Edit Profile</h1>
      <p class="mt-2 text-gray-600">Update your personal information and preferences</p>
    </div>

    <!-- Edit Form -->
    <div class="bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden">
      <%= form_with model: @user, url: profile_path, method: :patch, local: true, multipart: true, data: { turbo: false }, class: "space-y-8" do |form| %>
        <!-- Profile Picture Section -->
        <div class="px-6 py-6 border-b border-gray-200">
          <h2 class="text-lg font-medium text-gray-900 mb-4">Profile Picture</h2>
          <div class="flex items-center space-x-6">
            <div class="flex-shrink-0">
              <% if @user.avatar.attached? %>
                <%= image_tag @user.avatar, class: "h-20 w-20 rounded-full object-cover" %>
              <% else %>
                <div class="h-20 w-20 bg-gray-300 rounded-full flex items-center justify-center">
                  <span class="text-xl font-bold text-gray-600"><%= @user.name.first.upcase %></span>
                </div>
              <% end %>
            </div>
            <div class="flex-1">
              <%= form.label :avatar, "Choose new picture", class: "block text-sm font-medium text-gray-700 mb-2" %>
              <%= form.file_field :avatar, accept: "image/*", class: "block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100" %>
              <p class="mt-1 text-sm text-gray-500">PNG, JPG, GIF up to 10MB</p>
            </div>
          </div>
        </div>

        <!-- Personal Information -->
        <div class="px-6 py-6 border-b border-gray-200">
          <h2 class="text-lg font-medium text-gray-900 mb-6">Personal Information</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <%= form.label :name, "Full Name", class: "block text-sm font-medium text-gray-700 mb-2" %>
              <%= form.text_field :name, required: true, 
                  class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200", 
                  placeholder: "Enter your full name" %>
            </div>
            <div>
              <%= form.label :email, "Email Address", class: "block text-sm font-medium text-gray-700 mb-2" %>
              <%= form.email_field :email, required: true, 
                  class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200", 
                  placeholder: "Enter your email" %>

            </div>
            <div>
              <%= form.label :phone, "Phone Number", class: "block text-sm font-medium text-gray-700 mb-2" %>
              <%= form.telephone_field :phone, 
                  class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200", 
                  placeholder: "Enter your phone number" %>
            </div>
            <div>
              <%= form.label :role, "Account Type", class: "block text-sm font-medium text-gray-700 mb-2" %>
              <div class="relative">
                <%= form.select :role, 
                    options_for_select([['Tenant', 'tenant'], ['Landlord', 'landlord']], @user.role), 
                    {}, 
                    { class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200 appearance-none bg-white" } %>
                <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                  <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Bio Section -->
        <div class="px-6 py-6 border-b border-gray-200">
          <h2 class="text-lg font-medium text-gray-900 mb-6">About You</h2>
          <div>
            <%= form.label :bio, "Bio", class: "block text-sm font-medium text-gray-700 mb-2" %>
            <%= form.text_area :bio, rows: 4, 
                class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200 resize-none", 
                placeholder: "Tell us a bit about yourself..." %>
            <p class="mt-1 text-sm text-gray-500">Brief description for your profile. Maximum 500 characters.</p>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="px-6 py-6 bg-gray-50">
          <div class="flex items-center justify-between">
            <div class="flex space-x-3">
              <%= link_to profile_path, class: "px-6 py-3 border border-gray-300 rounded-lg text-gray-700 font-medium hover:bg-gray-50 transition duration-200" do %>
                Cancel
              <% end %>
            </div>
            <div class="flex space-x-3">
              <%= form.submit "Save Changes", class: "px-8 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition duration-200" %>
            </div>
          </div>
        </div>
      <% end %>
    </div>

    <!-- Account Information Notice -->
    <div class="mt-8 bg-blue-50 rounded-lg p-6">
      <div class="flex items-start">
        <svg class="h-5 w-5 text-blue-400 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
        <div class="text-sm text-blue-700">
          <p class="font-medium mb-1">Account Security</p>
          <p>To change your password or update security settings, visit your <%= link_to "Settings", settings_path, class: "font-medium underline hover:no-underline" %> page.</p>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  // Character counter for bio
  document.addEventListener('DOMContentLoaded', function() {
    const bioField = document.querySelector('textarea[name="user[bio]"]');
    if (bioField) {
      const maxLength = 500;
      const counter = document.createElement('div');
      counter.className = 'text-sm text-gray-500 mt-1';
      bioField.parentNode.appendChild(counter);
      
      function updateCounter() {
        const remaining = maxLength - bioField.value.length;
        counter.textContent = `${remaining} characters remaining`;
        
        if (remaining < 0) {
          counter.className = 'text-sm text-red-500 mt-1';
          bioField.classList.add('border-red-300');
        } else {
          counter.className = 'text-sm text-gray-500 mt-1';
          bioField.classList.remove('border-red-300');
        }
      }
      
      bioField.addEventListener('input', updateCounter);
      updateCounter();
    }
  });
</script>