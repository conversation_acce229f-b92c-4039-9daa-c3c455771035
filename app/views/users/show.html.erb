<% content_for :title, "My Profile" %>

<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 py-6 sm:py-12">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Enhanced Breadcrumb -->
    <nav class="flex mb-8" aria-label="Breadcrumb">
      <ol class="inline-flex items-center space-x-1 md:space-x-3 bg-white/70 backdrop-blur-sm rounded-full px-4 py-2 shadow-sm border border-white/20">
        <li class="inline-flex items-center">
          <%= link_to root_path, class: "inline-flex items-center text-sm font-medium text-slate-600 hover:text-indigo-600 transition-colors duration-200" do %>
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
            </svg>
            Home
          <% end %>
        </li>
        <li>
          <div class="flex items-center">
            <svg class="w-5 h-5 text-slate-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
            </svg>
            <span class="ml-1 text-sm font-medium text-slate-500 md:ml-2">Profile</span>
          </div>
        </li>
      </ol>
    </nav>

    <div class="grid grid-cols-1 lg:grid-cols-12 gap-6 lg:gap-8">
      <!-- Enhanced Profile Card -->
      <div class="lg:col-span-4">
        <div class="bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-white/20 overflow-hidden hover:shadow-2xl transition-all duration-300">
          <!-- Sophisticated Profile Header -->
          <div class="relative bg-gradient-to-br from-indigo-600 via-purple-600 to-blue-700 px-8 py-10">
            <!-- Background Pattern -->
            <div class="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent"></div>
            <div class="absolute inset-0 bg-[url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.05"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')]"></div>
            
            <div class="relative text-center">
              <!-- Enhanced Avatar -->
              <div class="mx-auto h-28 w-28 relative mb-6">
                <div class="absolute inset-0 bg-white rounded-full shadow-2xl"></div>
                <div class="relative h-full w-full bg-white rounded-full flex items-center justify-center overflow-hidden ring-4 ring-white/30">
                  <% if @user.avatar.attached? %>
                    <%= image_tag @user.avatar, class: "h-full w-full object-cover" %>
                  <% else %>
                    <span class="text-3xl font-bold text-indigo-600"><%= @user.name.first.upcase %></span>
                  <% end %>
                </div>
                <!-- Online Status Indicator -->
                <div class="absolute bottom-1 right-1 h-6 w-6 bg-emerald-400 rounded-full border-4 border-white shadow-lg"></div>
              </div>
              
              <h1 class="text-2xl font-bold text-white mb-1"><%= @user.name %></h1>
              <div class="inline-flex items-center px-3 py-1 rounded-full bg-white/20 backdrop-blur-sm mb-2">
                <span class="text-white/90 text-sm font-medium capitalize"><%= @user.role %></span>
              </div>
              <p class="text-white/70 text-sm">Member since <%= @user.created_at.strftime("%B %Y") %></p>
            </div>
          </div>

          <!-- Enhanced Stats Section -->
          <div class="px-8 py-8">
            <div class="grid grid-cols-2 gap-6">
              <% if @user.landlord? %>
                <div class="text-center group">
                  <div class="text-3xl font-bold text-slate-800 mb-1 group-hover:text-indigo-600 transition-colors duration-200"><%= @properties_count %></div>
                  <div class="text-xs font-medium text-slate-500 uppercase tracking-wider">Properties</div>
                </div>
              <% end %>
              <div class="text-center group">
                <div class="text-3xl font-bold text-slate-800 mb-1 group-hover:text-indigo-600 transition-colors duration-200"><%= @reviews_count %></div>
                <div class="text-xs font-medium text-slate-500 uppercase tracking-wider">Reviews</div>
              </div>
              <div class="text-center group">
                <div class="text-3xl font-bold text-slate-800 mb-1 group-hover:text-indigo-600 transition-colors duration-200"><%= @favorites_count %></div>
                <div class="text-xs font-medium text-slate-500 uppercase tracking-wider">Favorites</div>
              </div>
              <div class="text-center group">
                <div class="text-lg font-bold text-slate-800 mb-1 group-hover:text-indigo-600 transition-colors duration-200"><%= time_ago_in_words(@user.updated_at) %></div>
                <div class="text-xs font-medium text-slate-500 uppercase tracking-wider">Last Active</div>
              </div>
            </div>
          </div>

          <!-- Enhanced Action Buttons -->
          <div class="px-8 pb-8 space-y-3">
            <%= link_to edit_profile_path, class: "group w-full bg-gradient-to-r from-indigo-600 to-purple-600 text-white py-4 px-6 rounded-2xl font-semibold hover:from-indigo-700 hover:to-purple-700 transition-all duration-200 text-center block shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" do %>
              <svg class="w-5 h-5 inline mr-2 group-hover:rotate-12 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
              </svg>
              Edit Profile
            <% end %>
            <%= link_to settings_path, class: "group w-full bg-slate-100 hover:bg-slate-200 text-slate-700 py-4 px-6 rounded-2xl font-semibold transition-all duration-200 text-center block hover:shadow-lg transform hover:-translate-y-0.5" do %>
              <svg class="w-5 h-5 inline mr-2 group-hover:rotate-90 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
              Settings
            <% end %>
          </div>
        </div>
      </div>

      <!-- Enhanced Main Content -->
      <div class="lg:col-span-8 space-y-6">
        <!-- Sophisticated Profile Information -->
        <div class="bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-white/20 p-8 hover:shadow-2xl transition-all duration-300">
          <div class="flex items-center justify-between mb-8">
            <h2 class="text-2xl font-bold text-slate-800">Profile Information</h2>
            <div class="h-1 w-16 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full"></div>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div class="space-y-2">
              <label class="block text-sm font-semibold text-slate-600 uppercase tracking-wider">Full Name</label>
              <p class="text-lg font-medium text-slate-800 bg-slate-50 rounded-xl px-4 py-3"><%= @user.name %></p>
            </div>
            <div class="space-y-2">
              <label class="block text-sm font-semibold text-slate-600 uppercase tracking-wider">Email</label>
              <p class="text-lg font-medium text-slate-800 bg-slate-50 rounded-xl px-4 py-3 break-all"><%= @user.email %></p>
            </div>
            <div class="space-y-2">
              <label class="block text-sm font-semibold text-slate-600 uppercase tracking-wider">Phone</label>
              <p class="text-lg font-medium text-slate-800 bg-slate-50 rounded-xl px-4 py-3">
                <%= @user.phone.present? ? @user.phone : "Not provided" %>
              </p>
            </div>
            <div class="space-y-2">
              <label class="block text-sm font-semibold text-slate-600 uppercase tracking-wider">Account Type</label>
              <div class="inline-flex items-center px-4 py-3 rounded-xl bg-gradient-to-r from-indigo-500 to-purple-500 text-white font-semibold shadow-lg">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                  <path d="M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z"></path>
                </svg>
                <span class="capitalize"><%= @user.role %></span>
              </div>
            </div>
          </div>
          
          <% if @user.bio.present? %>
            <div class="mt-8 space-y-2">
              <label class="block text-sm font-semibold text-slate-600 uppercase tracking-wider">About Me</label>
              <div class="bg-slate-50 rounded-xl px-6 py-4">
                <p class="text-lg text-slate-700 leading-relaxed"><%= @user.bio %></p>
              </div>
            </div>
          <% end %>
        </div>

        <!-- Enhanced Recent Activity -->
        <div class="bg-white/80 backdrop-blur-sm rounded-3xl shadow-xl border border-white/20 p-8 hover:shadow-2xl transition-all duration-300">
          <div class="flex items-center justify-between mb-8">
            <h2 class="text-2xl font-bold text-slate-800">Recent Activity</h2>
            <div class="h-1 w-16 bg-gradient-to-r from-emerald-500 to-blue-500 rounded-full"></div>
          </div>
          
          <% if @recent_activities.any? %>
            <div class="space-y-4">
              <% @recent_activities.each do |activity| %>
                <div class="group flex items-start space-x-4 p-6 bg-slate-50/50 hover:bg-white rounded-2xl transition-all duration-200 hover:shadow-lg border border-transparent hover:border-white/50">
                  <div class="flex-shrink-0">
                    <% case activity[:type] %>
                    <% when 'review' %>
                      <div class="w-12 h-12 bg-gradient-to-br from-amber-400 to-orange-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-200">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                      </div>
                    <% when 'favorite' %>
                      <div class="w-12 h-12 bg-gradient-to-br from-rose-400 to-pink-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-200">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
                        </svg>
                      </div>
                    <% when 'viewing' %>
                      <div class="w-12 h-12 bg-gradient-to-br from-blue-400 to-indigo-500 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-200">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                        </svg>
                      </div>
                    <% end %>
                  </div>
                  <div class="flex-1 min-w-0">
                    <p class="text-base font-semibold text-slate-800 mb-1"><%= activity[:description] %></p>
                    <p class="text-sm text-slate-500 font-medium"><%= time_ago_in_words(activity[:date]) %> ago</p>
                  </div>
                  <div class="flex-shrink-0">
                    <%= link_to activity[:link], class: "inline-flex items-center px-4 py-2 text-sm font-semibold text-indigo-600 hover:text-white bg-indigo-50 hover:bg-indigo-600 rounded-xl transition-all duration-200 hover:shadow-lg transform hover:-translate-y-0.5" do %>
                      View
                      <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                      </svg>
                    <% end %>
                  </div>
                </div>
              <% end %>
            </div>
          <% else %>
            <div class="text-center py-16">
              <div class="mx-auto h-24 w-24 bg-gradient-to-br from-slate-100 to-slate-200 rounded-3xl flex items-center justify-center mb-6">
                <svg class="h-12 w-12 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
              </div>
              <h3 class="text-xl font-bold text-slate-800 mb-2">No recent activity</h3>
              <p class="text-base text-slate-500 mb-6">Start exploring properties to see your activity here.</p>
              <%= link_to root_path, class: "inline-flex items-center px-6 py-3 text-sm font-semibold text-white bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl hover:from-indigo-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" do %>
                Explore Properties
                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                </svg>
              <% end %>
            </div>
          <% end %>
        </div>
      </div>
    </div>
  </div>
</div>