<% content_for :title, "Analytics Dashboard - Ofie" %>
<% content_for :description, "Comprehensive insights into your property portfolio performance with advanced analytics and reporting." %>

<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 relative overflow-hidden">
  <!-- Background Pattern -->
  <div class="absolute inset-0 bg-grid-slate-100/50 [mask-image:linear-gradient(0deg,transparent,black)] pointer-events-none"></div>
  <div class="absolute top-0 right-1/3 w-96 h-96 bg-gradient-to-l from-emerald-300/20 to-teal-300/20 rounded-full blur-3xl"></div>
  <div class="absolute bottom-0 left-1/3 w-96 h-96 bg-gradient-to-r from-purple-300/20 to-pink-300/20 rounded-full blur-3xl"></div>
  
  <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Enhanced Header -->
    <div class="mb-12">
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div class="mb-6 lg:mb-0">
          <div class="flex items-center mb-4">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-2xl mr-4 shadow-lg shadow-emerald-500/25">
              <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
            </div>
            <div>
              <h1 class="text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-900 via-gray-800 to-gray-700 bg-clip-text text-transparent">
                Analytics Dashboard
              </h1>
              <p class="text-xl text-gray-600 font-medium mt-1">
                Comprehensive insights into your property portfolio performance
              </p>
            </div>
          </div>
          <div class="flex items-center space-x-4 text-sm text-gray-500">
            <div class="flex items-center">
              <svg class="w-4 h-4 mr-1 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              Data updated: <%= Time.current.strftime('%B %d, %Y at %I:%M %p') %>
            </div>
            <span class="h-4 w-px bg-gray-300"></span>
            <div class="flex items-center">
              <div class="w-2 h-2 bg-green-500 rounded-full mr-1 animate-pulse"></div>
              Real-time analytics
            </div>
          </div>
        </div>
        
        <div class="flex flex-col sm:flex-row items-stretch sm:items-center space-y-3 sm:space-y-0 sm:space-x-4">
          <%= link_to dashboard_path, class: "group inline-flex items-center justify-center px-6 py-3 border-2 border-gray-200 text-gray-700 bg-white/80 backdrop-blur-sm hover:bg-white hover:border-gray-300 rounded-2xl font-semibold transition-all duration-300 transform hover:-translate-y-0.5 hover:shadow-lg" do %>
            <svg class="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m0 7h18"></path>
            </svg>
            Back to Dashboard
          <% end %>
          <button class="group inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-emerald-600 to-teal-600 text-white rounded-2xl font-semibold hover:from-emerald-700 hover:to-teal-700 transition-all duration-300 transform hover:-translate-y-0.5 shadow-lg shadow-emerald-500/25 hover:shadow-xl hover:shadow-emerald-500/30">
            <svg class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Export Report
          </button>
        </div>
      </div>
    </div>

    <!-- Enhanced Key Metrics Overview -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
      <!-- Total Revenue -->
      <div class="group bg-white/80 backdrop-blur-xl overflow-hidden shadow-xl shadow-gray-200/50 rounded-3xl border border-white/20 hover:shadow-2xl hover:shadow-emerald-500/10 transition-all duration-300 transform hover:-translate-y-1">
        <div class="p-8">
          <div class="flex items-center justify-between mb-4">
            <div class="w-14 h-14 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center shadow-lg shadow-emerald-500/25 group-hover:scale-110 transition-transform duration-300">
              <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
              </svg>
            </div>
            <div class="text-emerald-500 text-sm font-semibold flex items-center">
              <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12"></path>
              </svg>
              +12%
            </div>
          </div>
          <div class="space-y-2">
            <h3 class="text-sm font-semibold text-gray-500 uppercase tracking-wider">Total Revenue</h3>
            <p class="text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
              $<%= number_with_delimiter(@analytics[:monthly_revenue].sum { |m| m[:revenue] }) %>
            </p>
            <p class="text-sm text-emerald-600 font-medium">+12% from last year</p>
          </div>
          <div class="mt-6">
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class="bg-gradient-to-r from-emerald-500 to-teal-600 h-2 rounded-full transition-all duration-1000" style="width: 78%"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Occupancy Rate -->
      <div class="group bg-white/80 backdrop-blur-xl overflow-hidden shadow-xl shadow-gray-200/50 rounded-3xl border border-white/20 hover:shadow-2xl hover:shadow-blue-500/10 transition-all duration-300 transform hover:-translate-y-1">
        <div class="p-8">
          <div class="flex items-center justify-between mb-4">
            <div class="w-14 h-14 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg shadow-blue-500/25 group-hover:scale-110 transition-transform duration-300">
              <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
              </svg>
            </div>
            <div class="text-blue-500 text-sm font-semibold">
              Above Average
            </div>
          </div>
          <div class="space-y-2">
            <h3 class="text-sm font-semibold text-gray-500 uppercase tracking-wider">Occupancy Rate</h3>
            <p class="text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
              <%= @analytics[:occupancy_rate] %>%
            </p>
            <p class="text-sm text-blue-600 font-medium">Industry avg: 85%</p>
          </div>
          <div class="mt-6">
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class="bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full transition-all duration-1000" style="width: <%= @analytics[:occupancy_rate] %>%"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Total Applications -->
      <div class="group bg-white/80 backdrop-blur-xl overflow-hidden shadow-xl shadow-gray-200/50 rounded-3xl border border-white/20 hover:shadow-2xl hover:shadow-amber-500/10 transition-all duration-300 transform hover:-translate-y-1">
        <div class="p-8">
          <div class="flex items-center justify-between mb-4">
            <div class="w-14 h-14 bg-gradient-to-r from-amber-500 to-orange-600 rounded-2xl flex items-center justify-center shadow-lg shadow-amber-500/25 group-hover:scale-110 transition-transform duration-300">
              <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
              </svg>
            </div>
            <div class="text-amber-500 text-sm font-semibold">
              Last 6M
            </div>
          </div>
          <div class="space-y-2">
            <h3 class="text-sm font-semibold text-gray-500 uppercase tracking-wider">Total Applications</h3>
            <p class="text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
              <%= @analytics[:application_trends].sum { |m| m[:applications] } %>
            </p>
            <p class="text-sm text-amber-600 font-medium">Last 6 months</p>
          </div>
          <div class="mt-6">
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class="bg-gradient-to-r from-amber-500 to-orange-600 h-2 rounded-full transition-all duration-1000" style="width: 65%"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Average Revenue per Property -->
      <div class="group bg-white/80 backdrop-blur-xl overflow-hidden shadow-xl shadow-gray-200/50 rounded-3xl border border-white/20 hover:shadow-2xl hover:shadow-purple-500/10 transition-all duration-300 transform hover:-translate-y-1">
        <div class="p-8">
          <div class="flex items-center justify-between mb-4">
            <div class="w-14 h-14 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg shadow-purple-500/25 group-hover:scale-110 transition-transform duration-300">
              <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
            </div>
            <div class="text-purple-500 text-sm font-semibold">
              Per Property
            </div>
          </div>
          <div class="space-y-2">
            <h3 class="text-sm font-semibold text-gray-500 uppercase tracking-wider">Avg Revenue/Property</h3>
            <p class="text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
              $<%= number_with_delimiter(@analytics[:property_performance].any? ? (@analytics[:property_performance].sum { |p| p[:total_revenue] } / @analytics[:property_performance].count).round : 0) %>
            </p>
            <p class="text-sm text-purple-600 font-medium">Per property</p>
          </div>
          <div class="mt-6">
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class="bg-gradient-to-r from-purple-500 to-indigo-600 h-2 rounded-full transition-all duration-1000" style="width: 82%"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Enhanced Charts Section -->
    <div class="grid grid-cols-1 xl:grid-cols-2 gap-8 mb-12">
      <!-- Monthly Revenue Chart -->
      <div class="bg-white/80 backdrop-blur-xl shadow-xl shadow-gray-200/50 rounded-3xl border border-white/20">
        <div class="px-8 py-6 border-b border-gray-200/50">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-2xl font-bold text-gray-900">Monthly Revenue Trend</h3>
              <p class="text-gray-600 mt-1">Revenue over the last 12 months</p>
            </div>
            <div class="flex items-center space-x-2">
              <div class="w-3 h-3 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-full"></div>
              <span class="text-sm font-medium text-gray-600">Revenue</span>
            </div>
          </div>
        </div>
        <div class="p-8">
          <div class="h-80 flex items-end justify-between space-x-3">
            <% @analytics[:monthly_revenue].each_with_index do |month_data, index| %>
              <% max_revenue = @analytics[:monthly_revenue].map { |m| m[:revenue] }.max %>
              <% height_percentage = max_revenue > 0 ? (month_data[:revenue].to_f / max_revenue * 100) : 0 %>
              <div class="group flex flex-col items-center space-y-3 flex-1">
                <div class="relative w-full">
                  <div class="bg-gradient-to-t from-blue-500 to-indigo-600 rounded-t-xl group-hover:from-blue-600 group-hover:to-indigo-700 transition-all duration-300 shadow-lg group-hover:shadow-xl" 
                       style="height: <%= height_percentage %>%; min-height: 8px;"
                       data-tooltip="$<%= number_with_delimiter(month_data[:revenue]) %>">
                  </div>
                </div>
                <div class="text-xs text-gray-600 font-medium transform -rotate-45 origin-center whitespace-nowrap">
                  <%= month_data[:month].split(' ').first %>
                </div>
                <div class="text-xs font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-200">
                  $<%= number_with_delimiter(month_data[:revenue], delimiter: ',', precision: 0) %>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>

      <!-- Application Trends Chart -->
      <div class="bg-white/80 backdrop-blur-xl shadow-xl shadow-gray-200/50 rounded-3xl border border-white/20">
        <div class="px-8 py-6 border-b border-gray-200/50">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-2xl font-bold text-gray-900">Application Trends</h3>
              <p class="text-gray-600 mt-1">Applications received over the last 6 months</p>
            </div>
            <div class="flex items-center space-x-2">
              <div class="w-3 h-3 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-full"></div>
              <span class="text-sm font-medium text-gray-600">Applications</span>
            </div>
          </div>
        </div>
        <div class="p-8">
          <div class="h-80 flex items-end justify-between space-x-4">
            <% @analytics[:application_trends].each_with_index do |month_data, index| %>
              <% max_applications = @analytics[:application_trends].map { |m| m[:applications] }.max %>
              <% height_percentage = max_applications > 0 ? (month_data[:applications].to_f / max_applications * 100) : 0 %>
              <div class="group flex flex-col items-center space-y-3 flex-1">
                <div class="relative w-full">
                  <div class="bg-gradient-to-t from-emerald-500 to-teal-600 rounded-t-xl group-hover:from-emerald-600 group-hover:to-teal-700 transition-all duration-300 shadow-lg group-hover:shadow-xl" 
                       style="height: <%= height_percentage %>%; min-height: 8px;"
                       data-tooltip="<%= month_data[:applications] %> applications">
                  </div>
                </div>
                <div class="text-xs text-gray-600 font-medium transform -rotate-45 origin-center whitespace-nowrap">
                  <%= month_data[:month].split(' ').first %>
                </div>
                <div class="text-xs font-bold text-gray-900 group-hover:text-emerald-600 transition-colors duration-200">
                  <%= month_data[:applications] %>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </div>

    <!-- Enhanced Property Performance Table -->
    <div class="bg-white/80 backdrop-blur-xl shadow-xl shadow-gray-200/50 rounded-3xl border border-white/20">
      <div class="px-8 py-6 border-b border-gray-200/50">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-2xl font-bold text-gray-900">Property Performance</h3>
            <p class="text-gray-600 mt-1">Detailed performance metrics for each property</p>
          </div>
          <div class="flex items-center space-x-3">
            <select class="px-4 py-2 bg-white border-2 border-gray-200 rounded-xl text-sm font-medium focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200">
              <option>All Properties</option>
              <option>Top Performers</option>
              <option>Needs Attention</option>
            </select>
          </div>
        </div>
      </div>
      <div class="overflow-x-auto">
        <table class="min-w-full">
          <thead>
            <tr class="border-b border-gray-200/50">
              <th scope="col" class="px-8 py-6 text-left text-sm font-bold text-gray-700 uppercase tracking-wider">
                Property
              </th>
              <th scope="col" class="px-8 py-6 text-left text-sm font-bold text-gray-700 uppercase tracking-wider">
                Applications
              </th>
              <th scope="col" class="px-8 py-6 text-left text-sm font-bold text-gray-700 uppercase tracking-wider">
                Active Leases
              </th>
              <th scope="col" class="px-8 py-6 text-left text-sm font-bold text-gray-700 uppercase tracking-wider">
                Total Revenue
              </th>
              <th scope="col" class="px-8 py-6 text-left text-sm font-bold text-gray-700 uppercase tracking-wider">
                Performance
              </th>
              <th scope="col" class="px-8 py-6 text-left text-sm font-bold text-gray-700 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200/50">
            <% @analytics[:property_performance].each_with_index do |property, index| %>
              <tr class="group hover:bg-blue-50/50 transition-all duration-200">
                <td class="px-8 py-6">
                  <div class="flex items-center space-x-4">
                    <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center text-white font-bold shadow-lg">
                      <%= (index + 1).to_s.rjust(2, '0') %>
                    </div>
                    <div>
                      <div class="text-lg font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-200">
                        <%= property[:title] %>
                      </div>
                      <div class="text-sm text-gray-600">Property #<%= property[:id] || index + 1 %></div>
                    </div>
                  </div>
                </td>
                <td class="px-8 py-6">
                  <div class="flex items-center">
                    <div class="text-2xl font-bold text-gray-900"><%= property[:applications] %></div>
                    <div class="ml-2 text-sm text-gray-500">apps</div>
                  </div>
                </td>
                <td class="px-8 py-6">
                  <div class="flex items-center">
                    <div class="text-2xl font-bold text-gray-900"><%= property[:active_leases] %></div>
                    <div class="ml-2 text-sm text-gray-500">active</div>
                  </div>
                </td>
                <td class="px-8 py-6">
                  <div class="text-2xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                    $<%= number_with_delimiter(property[:total_revenue]) %>
                  </div>
                </td>
                <td class="px-8 py-6">
                  <% performance_score = (property[:applications] * 10 + property[:active_leases] * 50 + property[:total_revenue] / 100).round %>
                  <% if performance_score >= 80 %>
                    <span class="inline-flex items-center px-4 py-2 rounded-xl text-sm font-bold bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 shadow-sm">
                      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                      Excellent
                    </span>
                  <% elsif performance_score >= 60 %>
                    <span class="inline-flex items-center px-4 py-2 rounded-xl text-sm font-bold bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 shadow-sm">
                      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                      </svg>
                      Good
                    </span>
                  <% elsif performance_score >= 40 %>
                    <span class="inline-flex items-center px-4 py-2 rounded-xl text-sm font-bold bg-gradient-to-r from-amber-100 to-orange-100 text-amber-800 shadow-sm">
                      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                      </svg>
                      Average
                    </span>
                  <% else %>
                    <span class="inline-flex items-center px-4 py-2 rounded-xl text-sm font-bold bg-gradient-to-r from-red-100 to-pink-100 text-red-800 shadow-sm">
                      <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                      Needs Attention
                    </span>
                  <% end %>
                </td>
                <td class="px-8 py-6">
                  <div class="flex items-center space-x-2">
                    <button class="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200">
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                      </svg>
                    </button>
                    <button class="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-lg transition-all duration-200">
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                      </svg>
                    </button>
                    <button class="p-2 text-gray-400 hover:text-purple-600 hover:bg-purple-50 rounded-lg transition-all duration-200">
                      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                      </svg>
                    </button>
                  </div>
                </td>
              </tr>
            <% end %>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<!-- Enhanced JavaScript -->
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Animate metrics counters
    function animateCounters() {
      const counters = document.querySelectorAll('[data-stat]');
      counters.forEach(counter => {
        const target = parseInt(counter.textContent.replace(/[,$]/g, ''));
        if (target > 0) {
          let count = 0;
          const increment = target / 100;
          const timer = setInterval(() => {
            count += increment;
            if (count >= target) {
              counter.textContent = target.toLocaleString();
              clearInterval(timer);
            } else {
              counter.textContent = Math.ceil(count).toLocaleString();
            }
          }, 20);
        }
      });
    }

    // Chart hover effects
    const chartBars = document.querySelectorAll('[data-tooltip]');
    chartBars.forEach(bar => {
      bar.addEventListener('mouseenter', function() {
        // Create tooltip
        const tooltip = document.createElement('div');
        tooltip.className = 'absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-gray-900 text-white text-xs rounded-lg z-50 shadow-lg';
        tooltip.textContent = this.getAttribute('data-tooltip');
        this.parentNode.appendChild(tooltip);
      });

      bar.addEventListener('mouseleave', function() {
        const tooltip = this.parentNode.querySelector('.absolute');
        if (tooltip) tooltip.remove();
      });
    });

    // Auto-refresh analytics data every 10 minutes
    setInterval(function() {
      if (document.visibilityState === 'visible') {
        fetch('<%= dashboard_analytics_path %>.json')
          .then(response => response.json())
          .then(data => {
            console.log('Analytics data refreshed:', data);
            // Update charts and metrics here if needed
          })
          .catch(error => console.error('Error refreshing analytics:', error));
      }
    }, 600000); // 10 minutes

    // Run counter animation after page load
    setTimeout(animateCounters, 500);
  });
</script>

<style>
  .bg-grid-slate-100\/50 {
    background-image: url("data:image/svg+xml,%3csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3e%3cg fill='%23f1f5f9' fill-opacity='0.3' fill-rule='evenodd'%3e%3cpath d='m0 40 40-40h-40v40zm40 0v-40h-40z'/%3e%3c/g%3e%3c/svg%3e");
  }
</style>
