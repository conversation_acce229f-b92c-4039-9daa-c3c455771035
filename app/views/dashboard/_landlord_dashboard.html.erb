<!-- Enhanced Landlord Dashboard -->
<div class="space-y-8">
  <!-- Enhanced Stats Overview -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 animate-on-scroll">
    <!-- Total Properties -->
    <div class="group bg-white/80 backdrop-blur-xl overflow-hidden shadow-xl shadow-gray-200/50 rounded-3xl border border-white/20 hover:shadow-2xl hover:shadow-blue-500/10 transition-all duration-300 transform hover:-translate-y-1">
      <div class="p-8">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg shadow-blue-500/25 group-hover:scale-110 transition-transform duration-300">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
              </svg>
            </div>
            <div>
              <p class="text-sm font-semibold text-gray-500 uppercase tracking-wider">Total Properties</p>
              <p class="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent" data-stat="total_properties"><%= @stats[:total_properties] %></p>
            </div>
          </div>
          <div class="text-green-500 text-sm font-medium">
            +12%
          </div>
        </div>
        <div class="mt-4 flex items-center">
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div class="bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full" style="width: 75%"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Available Properties -->
    <div class="group bg-white/80 backdrop-blur-xl overflow-hidden shadow-xl shadow-gray-200/50 rounded-3xl border border-white/20 hover:shadow-2xl hover:shadow-green-500/10 transition-all duration-300 transform hover:-translate-y-1">
      <div class="p-8">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="w-12 h-12 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center shadow-lg shadow-emerald-500/25 group-hover:scale-110 transition-transform duration-300">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div>
              <p class="text-sm font-semibold text-gray-500 uppercase tracking-wider">Available</p>
              <p class="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent" data-stat="available_properties"><%= @stats[:available_properties] %></p>
            </div>
          </div>
          <div class="text-green-500 text-sm font-medium">
            +8%
          </div>
        </div>
        <div class="mt-4 flex items-center">
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div class="bg-gradient-to-r from-emerald-500 to-teal-600 h-2 rounded-full" style="width: 60%"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Total Revenue -->
    <div class="group bg-white/80 backdrop-blur-xl overflow-hidden shadow-xl shadow-gray-200/50 rounded-3xl border border-white/20 hover:shadow-2xl hover:shadow-amber-500/10 transition-all duration-300 transform hover:-translate-y-1">
      <div class="p-8">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="w-12 h-12 bg-gradient-to-r from-amber-500 to-orange-600 rounded-2xl flex items-center justify-center shadow-lg shadow-amber-500/25 group-hover:scale-110 transition-transform duration-300">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
              </svg>
            </div>
            <div>
              <p class="text-sm font-semibold text-gray-500 uppercase tracking-wider">Total Revenue</p>
              <p class="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent" data-stat="total_revenue">$<%= number_with_delimiter(@stats[:total_revenue]) %></p>
            </div>
          </div>
          <div class="text-green-500 text-sm font-medium">
            +24%
          </div>
        </div>
        <div class="mt-4 flex items-center">
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div class="bg-gradient-to-r from-amber-500 to-orange-600 h-2 rounded-full" style="width: 85%"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Pending Applications -->
    <div class="group bg-white/80 backdrop-blur-xl overflow-hidden shadow-xl shadow-gray-200/50 rounded-3xl border border-white/20 hover:shadow-2xl hover:shadow-purple-500/10 transition-all duration-300 transform hover:-translate-y-1">
      <div class="p-8">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg shadow-purple-500/25 group-hover:scale-110 transition-transform duration-300">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
              </svg>
            </div>
            <div>
              <p class="text-sm font-semibold text-gray-500 uppercase tracking-wider">Pending Apps</p>
              <p class="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent" data-stat="pending_applications"><%= @stats[:pending_applications] %></p>
            </div>
          </div>
          <div class="text-orange-500 text-sm font-medium">
            <%= @stats[:pending_applications] > 0 ? 'Review' : 'All clear' %>
          </div>
        </div>
        <div class="mt-4 flex items-center">
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div class="bg-gradient-to-r from-purple-500 to-indigo-600 h-2 rounded-full" style="width: 40%"></div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Enhanced Quick Actions -->
  <div class="bg-white/80 backdrop-blur-xl shadow-xl shadow-gray-200/50 rounded-3xl border border-white/20 animate-on-scroll">
    <div class="px-8 py-6 border-b border-gray-200/50">
      <div class="flex items-center">
        <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl mr-3 flex items-center justify-center">
          <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
          </svg>
        </div>
        <h3 class="text-2xl font-bold text-gray-900">Quick Actions</h3>
      </div>
    </div>
    <div class="p-8">
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        <%= link_to new_property_path, class: "group flex items-center p-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl hover:from-blue-100 hover:to-indigo-100 transition-all duration-300 transform hover:-translate-y-1 hover:shadow-lg border border-blue-100 hover:border-blue-200" do %>
          <div class="flex-shrink-0 mr-4">
            <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg shadow-blue-500/25 group-hover:scale-110 transition-transform duration-300">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
            </div>
          </div>
          <div>
            <p class="text-lg font-bold text-blue-900 group-hover:text-blue-800">Add Property</p>
            <p class="text-sm text-blue-700 group-hover:text-blue-600">List a new property</p>
          </div>
        <% end %>

        <%= link_to properties_path, class: "group flex items-center p-6 bg-gradient-to-br from-emerald-50 to-teal-50 rounded-2xl hover:from-emerald-100 hover:to-teal-100 transition-all duration-300 transform hover:-translate-y-1 hover:shadow-lg border border-emerald-100 hover:border-emerald-200" do %>
          <div class="flex-shrink-0 mr-4">
            <div class="w-12 h-12 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center shadow-lg shadow-emerald-500/25 group-hover:scale-110 transition-transform duration-300">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
              </svg>
            </div>
          </div>
          <div>
            <p class="text-lg font-bold text-emerald-900 group-hover:text-emerald-800">Manage Properties</p>
            <p class="text-sm text-emerald-700 group-hover:text-emerald-600">View all properties</p>
          </div>
        <% end %>

        <%= link_to "#", class: "group flex items-center p-6 bg-gradient-to-br from-amber-50 to-orange-50 rounded-2xl hover:from-amber-100 hover:to-orange-100 transition-all duration-300 transform hover:-translate-y-1 hover:shadow-lg border border-amber-100 hover:border-amber-200" do %>
          <div class="flex-shrink-0 mr-4">
            <div class="w-12 h-12 bg-gradient-to-r from-amber-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg shadow-amber-500/25 group-hover:scale-110 transition-transform duration-300">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
              </svg>
            </div>
          </div>
          <div>
            <p class="text-lg font-bold text-amber-900 group-hover:text-amber-800">Applications</p>
            <p class="text-sm text-amber-700 group-hover:text-amber-600">Review applications</p>
          </div>
        <% end %>

        <%= link_to dashboard_analytics_path, class: "group flex items-center p-6 bg-gradient-to-br from-purple-50 to-indigo-50 rounded-2xl hover:from-purple-100 hover:to-indigo-100 transition-all duration-300 transform hover:-translate-y-1 hover:shadow-lg border border-purple-100 hover:border-purple-200" do %>
          <div class="flex-shrink-0 mr-4">
            <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg shadow-purple-500/25 group-hover:scale-110 transition-transform duration-300">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
            </div>
          </div>
          <div>
            <p class="text-lg font-bold text-purple-900 group-hover:text-purple-800">Analytics</p>
            <p class="text-sm text-purple-700 group-hover:text-purple-600">View reports</p>
          </div>
        <% end %>
      </div>
    </div>
  </div>

  <!-- Enhanced Recent Activity & Properties Grid -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 animate-on-scroll">
    <!-- Recent Applications -->
    <div class="bg-white/80 backdrop-blur-xl shadow-xl shadow-gray-200/50 rounded-3xl border border-white/20">
      <div class="px-8 py-6 border-b border-gray-200/50">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <div class="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl mr-3 flex items-center justify-center">
              <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
              </svg>
            </div>
            <h3 class="text-xl font-bold text-gray-900">Recent Applications</h3>
          </div>
          <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <%= @recent_applications.count %> New
          </span>
        </div>
      </div>
      <div class="divide-y divide-gray-100 max-h-96 overflow-y-auto">
        <% if @recent_applications.any? %>
          <% @recent_applications.each do |application| %>
            <div class="p-6 hover:bg-gray-50/50 transition-colors duration-200">
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                  <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg shadow-blue-500/25">
                    <span class="text-lg font-bold text-white">
                      <%= application.user.name&.first&.upcase || application.user.email.first.upcase %>
                    </span>
                  </div>
                  <div>
                    <p class="text-lg font-semibold text-gray-900"><%= application.user.name || application.user.email %></p>
                    <p class="text-sm text-gray-600"><%= application.property.title %></p>
                  </div>
                </div>
                <div class="text-right space-y-2">
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold
                    <%= case application.status
                        when 'pending' then 'bg-gradient-to-r from-yellow-100 to-amber-100 text-yellow-800'
                        when 'approved' then 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800'
                        when 'rejected' then 'bg-gradient-to-r from-red-100 to-pink-100 text-red-800'
                        else 'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800'
                        end %>">
                    <%= application.status.capitalize %>
                  </span>
                  <p class="text-xs text-gray-500"><%= time_ago_in_words(application.created_at) %> ago</p>
                </div>
              </div>
            </div>
          <% end %>
        <% else %>
          <div class="p-12 text-center">
            <div class="w-16 h-16 bg-gradient-to-r from-gray-100 to-gray-200 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
              </svg>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">No applications yet</h3>
            <p class="text-gray-600">Applications will appear here when tenants apply to your properties.</p>
          </div>
        <% end %>
      </div>
    </div>

    <!-- Upcoming Payments -->
    <div class="bg-white/80 backdrop-blur-xl shadow-xl shadow-gray-200/50 rounded-3xl border border-white/20">
      <div class="px-8 py-6 border-b border-gray-200/50">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <div class="w-8 h-8 bg-gradient-to-r from-amber-500 to-orange-600 rounded-xl mr-3 flex items-center justify-center">
              <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2"></path>
              </svg>
            </div>
            <h3 class="text-xl font-bold text-gray-900">Upcoming Payments</h3>
          </div>
          <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
            This Month
          </span>
        </div>
      </div>
      <div class="divide-y divide-gray-100 max-h-96 overflow-y-auto">
        <% if @upcoming_payments.any? %>
          <% @upcoming_payments.each do |payment| %>
            <div class="p-6 hover:bg-gray-50/50 transition-colors duration-200">
              <div class="flex items-center justify-between">
                <div class="space-y-1">
                  <p class="text-lg font-semibold text-gray-900"><%= payment.lease_agreement.property.title %></p>
                  <p class="text-sm text-gray-600"><%= payment.payment_type.humanize %></p>
                </div>
                <div class="text-right space-y-1">
                  <p class="text-xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">$<%= number_with_delimiter(payment.amount) %></p>
                  <p class="text-sm text-gray-500">Due <%= payment.due_date.strftime('%b %d') %></p>
                </div>
              </div>
            </div>
          <% end %>
        <% else %>
          <div class="p-12 text-center">
            <div class="w-16 h-16 bg-gradient-to-r from-green-100 to-emerald-200 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">All caught up!</h3>
            <p class="text-gray-600">No upcoming payments scheduled.</p>
          </div>
        <% end %>
      </div>
    </div>
  </div>

  <!-- Enhanced Properties Grid -->
  <div class="bg-white/80 backdrop-blur-xl shadow-xl shadow-gray-200/50 rounded-3xl border border-white/20 animate-on-scroll">
    <div class="px-8 py-6 border-b border-gray-200/50">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div class="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-xl mr-3 flex items-center justify-center">
            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-gray-900">Your Properties</h3>
        </div>
        <%= link_to "View All", properties_path, class: "inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-500 to-indigo-600 text-white text-sm font-semibold rounded-xl hover:from-blue-600 hover:to-indigo-700 transition-all duration-300 transform hover:-translate-y-0.5 shadow-lg hover:shadow-xl" %>
      </div>
    </div>
    <div class="p-8">
      <% if @properties.any? %>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <% @properties.limit(6).each do |property| %>
            <div class="group bg-white rounded-2xl border border-gray-200 overflow-hidden hover:shadow-xl hover:shadow-gray-200/50 transition-all duration-300 transform hover:-translate-y-2">
              <div class="relative overflow-hidden">
                <% if property.photos.attached? %>
                  <%= image_tag property.photos.first, class: "w-full h-56 object-cover group-hover:scale-110 transition-transform duration-500" %>
                <% else %>
                  <div class="w-full h-56 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center group-hover:from-gray-200 group-hover:to-gray-300 transition-all duration-300">
                    <div class="text-center">
                      <svg class="w-16 h-16 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                      </svg>
                      <p class="text-sm text-gray-500">No image available</p>
                    </div>
                  </div>
                <% end %>
                <div class="absolute top-4 right-4">
                  <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold backdrop-blur-sm border border-white/20
                    <%= property.availability_status == 'available' ? 'bg-green-500/80 text-white' : 'bg-red-500/80 text-white' %>">
                    <%= property.availability_status.capitalize %>
                  </span>
                </div>
              </div>
              <div class="p-6">
                <div class="flex items-start justify-between mb-3">
                  <h4 class="text-xl font-bold text-gray-900 truncate group-hover:text-blue-600 transition-colors duration-200"><%= property.title %></h4>
                  <div class="flex space-x-2 ml-4">
                    <%= link_to property_path(property), class: "p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-all duration-200" do %>
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                      </svg>
                    <% end %>
                    <%= link_to edit_property_path(property), class: "p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-lg transition-all duration-200" do %>
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                      </svg>
                    <% end %>
                  </div>
                </div>
                <div class="flex items-center text-gray-600 mb-4">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  </svg>
                  <span class="text-sm"><%= property.address %>, <%= property.city %></span>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">$<%= number_with_delimiter(property.price) %><span class="text-sm font-medium text-gray-500">/mo</span></span>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      <% else %>
        <div class="text-center py-16">
          <div class="w-24 h-24 bg-gradient-to-r from-blue-100 to-indigo-200 rounded-3xl flex items-center justify-center mx-auto mb-6">
            <svg class="w-12 h-12 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-gray-900 mb-3">No properties yet</h3>
          <p class="text-lg text-gray-600 mb-8">Get started by adding your first property to start earning rental income.</p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <%= link_to new_property_path, class: "inline-flex items-center px-8 py-4 border border-transparent shadow-lg text-lg font-semibold rounded-2xl text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-300 transform hover:-translate-y-1" do %>
              <svg class="-ml-1 mr-3 h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              Add Your First Property
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>
