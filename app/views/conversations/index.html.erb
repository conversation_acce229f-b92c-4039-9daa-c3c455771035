<% content_for :title, "Messages - Ofie" %>
<% content_for :description, "Manage your conversations and messages with property owners and tenants." %>

<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 relative overflow-hidden">
  <!-- Background Pattern -->
  <div class="absolute inset-0 bg-grid-slate-100/50 [mask-image:linear-gradient(0deg,transparent,black)] pointer-events-none"></div>
  <div class="absolute top-0 right-1/4 w-96 h-96 bg-gradient-to-l from-purple-300/20 to-pink-300/20 rounded-full blur-3xl"></div>
  <div class="absolute bottom-0 left-1/4 w-96 h-96 bg-gradient-to-r from-blue-300/20 to-cyan-300/20 rounded-full blur-3xl"></div>
  
  <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Enhanced Header -->
    <div class="mb-8">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-2xl shadow-lg shadow-purple-500/25">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
            </svg>
          </div>
          <div>
            <h1 class="text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-900 via-gray-800 to-gray-700 bg-clip-text text-transparent">
              Messages
            </h1>
            <p class="text-xl text-gray-600 font-medium mt-1">
              Stay connected with property owners and tenants
            </p>
          </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="flex items-center space-x-4">
          <%= link_to properties_path, class: "group inline-flex items-center px-6 py-3 border-2 border-gray-200 text-gray-700 bg-white/80 backdrop-blur-sm hover:bg-white hover:border-gray-300 rounded-2xl font-semibold transition-all duration-300 transform hover:-translate-y-0.5 hover:shadow-lg" do %>
            <svg class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
            Browse Properties
          <% end %>
        </div>
      </div>
    </div>

    <% if @conversations.any? %>
      <!-- Conversations Grid -->
      <div class="space-y-6">
        <% @conversations.each do |conversation| %>
          <div class="group bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 hover:shadow-2xl hover:shadow-gray-200/60 transition-all duration-300 transform hover:-translate-y-1">
            <div class="p-8">
              <div class="flex items-start justify-between">
                <!-- Conversation Main Content -->
                <div class="flex items-start space-x-6 flex-1">
                  <!-- Property Image -->
                  <div class="relative flex-shrink-0">
                    <% if conversation.property.photos.attached? && conversation.property.photos.any? %>
                      <div class="relative overflow-hidden rounded-2xl">
                        <%= image_tag conversation.property.photos.first, 
                            class: "w-20 h-20 object-cover group-hover:scale-110 transition-transform duration-300",
                            alt: conversation.property.title %>
                        <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-2xl"></div>
                      </div>
                    <% else %>
                      <div class="w-20 h-20 bg-gradient-to-br from-blue-100 to-indigo-200 rounded-2xl flex items-center justify-center shadow-inner">
                        <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                      </div>
                    <% end %>
                    
                    <!-- Status Indicator -->
                    <div class="absolute -top-2 -right-2">
                      <span class="inline-flex items-center justify-center w-6 h-6 bg-gradient-to-r from-green-400 to-emerald-500 text-white text-xs font-bold rounded-full border-2 border-white shadow-lg">
                        <%= conversation.unread_count_for(current_user) > 0 ? conversation.unread_count_for(current_user) : '✓' %>
                      </span>
                    </div>
                  </div>
                  
                  <!-- Conversation Details -->
                  <div class="flex-1 min-w-0">
                    <div class="flex items-start justify-between mb-3">
                      <div>
                        <h3 class="text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-200 mb-1">
                          <%= conversation.subject %>
                        </h3>
                        <div class="flex items-center space-x-4 text-sm text-gray-600">
                          <div class="flex items-center">
                            <svg class="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                            <%= conversation.property.title %>
                          </div>
                          <span class="text-gray-300">•</span>
                          <div class="flex items-center">
                            <svg class="w-4 h-4 mr-1 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            <%= conversation.property.address %>
                          </div>
                        </div>
                      </div>
                      
                      <!-- Status Badge -->
                      <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold
                        <%= case conversation.status
                            when 'active' then 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800'
                            when 'closed' then 'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800'
                            else 'bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800'
                            end %>">
                        <%= conversation.status.capitalize %>
                      </span>
                    </div>
                    
                    <!-- Other Participant Info -->
                    <div class="flex items-center space-x-3 mb-4">
                      <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center text-white font-semibold shadow-lg">
                        <%= conversation.other_participant(current_user).name.first.upcase %>
                      </div>
                      <div>
                        <p class="font-semibold text-gray-900">
                          <%= conversation.other_participant(current_user).name %>
                        </p>
                        <p class="text-sm text-gray-600">
                          <%= conversation.other_participant(current_user) == conversation.landlord ? 'Property Owner' : 'Tenant' %>
                        </p>
                      </div>
                    </div>
                    
                    <!-- Last Message Preview -->
                    <% if conversation.messages.any? %>
                      <div class="bg-gray-50 rounded-2xl p-4 mb-4">
                        <div class="flex items-start justify-between">
                          <div class="flex-1">
                            <p class="text-sm text-gray-700 line-clamp-2">
                              "<%= conversation.messages.last.content.truncate(100) %>"
                            </p>
                          </div>
                          <div class="ml-4 text-right flex-shrink-0">
                            <p class="text-xs text-gray-500">
                              <%= time_ago_in_words(conversation.last_message_at) %> ago
                            </p>
                          </div>
                        </div>
                      </div>
                    <% else %>
                      <div class="bg-blue-50 rounded-2xl p-4 mb-4 border border-blue-100">
                        <p class="text-sm text-blue-700 flex items-center">
                          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                          </svg>
                          No messages yet - start the conversation!
                        </p>
                      </div>
                    <% end %>
                    
                    <!-- Action Buttons -->
                    <div class="flex items-center justify-between">
                      <div class="flex items-center space-x-3">
                        <%= link_to conversation_path(conversation), class: "group inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-2xl font-semibold hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 transform hover:-translate-y-0.5 shadow-lg hover:shadow-xl" do %>
                          <svg class="w-4 h-4 mr-2 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                          </svg>
                          <% if conversation.unread_count_for(current_user) > 0 %>
                            Reply (<%= conversation.unread_count_for(current_user) %> new)
                          <% else %>
                            View Conversation
                          <% end %>
                        <% end %>
                        
                        <%= link_to property_path(conversation.property), class: "group inline-flex items-center px-4 py-3 border-2 border-gray-200 text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-300 rounded-2xl font-semibold transition-all duration-300 transform hover:-translate-y-0.5" do %>
                          <svg class="w-4 h-4 mr-2 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                          </svg>
                          View Property
                        <% end %>
                      </div>
                      
                      <!-- Property Price -->
                      <div class="text-right">
                        <p class="text-2xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                          $<%= number_with_delimiter(conversation.property.price) %>
                        </p>
                        <p class="text-sm text-gray-500">per month</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        <% end %>
      </div>
      
      <!-- Pagination or Load More (if needed) -->
      <% if @conversations.count >= 50 %>
        <div class="mt-12 text-center">
          <button class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-gray-100 to-gray-200 text-gray-700 rounded-2xl font-semibold hover:from-gray-200 hover:to-gray-300 transition-all duration-300 transform hover:-translate-y-0.5 shadow-lg hover:shadow-xl">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            Load More Conversations
          </button>
        </div>
      <% end %>
      
    <% else %>
      <!-- Enhanced Empty State -->
      <div class="text-center py-20">
        <div class="max-w-md mx-auto">
          <!-- Empty State Icon -->
          <div class="w-32 h-32 bg-gradient-to-br from-blue-100 via-purple-100 to-indigo-200 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-lg">
            <svg class="w-16 h-16 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
            </svg>
          </div>
          
          <h3 class="text-3xl font-bold text-gray-900 mb-4">No conversations yet</h3>
          <p class="text-lg text-gray-600 mb-8 leading-relaxed">
            Start connecting with property owners and tenants.<br>
            Browse properties and send your first message!
          </p>
          
          <!-- Action Buttons -->
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <%= link_to properties_path, class: "inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-2xl font-semibold hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 transform hover:-translate-y-1 shadow-lg hover:shadow-xl" do %>
              <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
              Browse Properties
            <% end %>
            
            <% if current_user.landlord? %>
              <%= link_to new_property_path, class: "inline-flex items-center px-8 py-4 border-2 border-gray-200 text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-300 rounded-2xl font-semibold transition-all duration-300 transform hover:-translate-y-1 shadow-lg hover:shadow-xl" do %>
                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Add Your Property
              <% end %>
            <% end %>
          </div>
        </div>
      </div>
    <% end %>
  </div>
</div>

<style>
  .bg-grid-slate-100\/50 {
    background-image: url("data:image/svg+xml,%3csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3e%3cg fill='%23f1f5f9' fill-opacity='0.3' fill-rule='evenodd'%3e%3cpath d='m0 40 40-40h-40v40zm40 0v-40h-40z'/%3e%3c/g%3e%3c/svg%3e");
  }
  
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
</style>
