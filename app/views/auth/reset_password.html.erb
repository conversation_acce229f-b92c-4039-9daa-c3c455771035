<% content_for :title, "Set New Password" %>

<div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full">
    <!-- <PERSON><PERSON> and <PERSON>er -->
    <div class="text-center mb-8">
      <div class="mx-auto h-16 w-16 bg-blue-600 rounded-full flex items-center justify-center mb-4">
        <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
        </svg>
      </div>
      <h2 class="text-3xl font-bold text-gray-900 mb-2">Set New Password</h2>
      <p class="text-gray-600">Enter your new password below</p>
    </div>

    <!-- Reset Password Form -->
    <div class="bg-white rounded-2xl shadow-xl p-8">
      <%= form_with url: reset_password_path, method: :post, local: true, data: { turbo: false }, class: "space-y-6" do |form| %>
        <%= form.hidden_field :token, value: params[:token] %>
        
        <div>
          <%= form.label :password, "New Password", class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= form.password_field :password, autocomplete: "new-password", required: true, 
              class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200", 
              placeholder: "Enter new password",
              minlength: 6 %>
          <p class="mt-1 text-sm text-gray-500">Must be at least 6 characters long</p>
        </div>

        <div>
          <%= form.label :password_confirmation, "Confirm New Password", class: "block text-sm font-medium text-gray-700 mb-2" %>
          <%= form.password_field :password_confirmation, autocomplete: "new-password", required: true, 
              class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200", 
              placeholder: "Confirm new password",
              minlength: 6 %>
        </div>

        <div>
          <%= form.submit "Update Password", class: "w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition duration-200" %>
        </div>

        <div class="text-center">
          <%= link_to "Back to Sign In", login_path, class: "font-medium text-blue-600 hover:text-blue-500" %>
        </div>
      <% end %>
    </div>

    <!-- Security Notice -->
    <div class="mt-8 text-center">
      <div class="bg-green-50 rounded-lg p-4">
        <div class="flex items-start">
          <svg class="h-5 w-5 text-green-400 mt-0.5 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
          <div class="text-sm text-green-700">
            <p class="font-medium mb-1">Secure Password Reset</p>
            <p>Your password will be encrypted and stored securely. This reset link will expire after use.</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  // Password confirmation validation
  document.addEventListener('DOMContentLoaded', function() {
    const passwordField = document.querySelector('input[name="password"]');
    const confirmField = document.querySelector('input[name="password_confirmation"]');
    const submitButton = document.querySelector('input[type="submit"]');
    
    function validatePasswords() {
      const password = passwordField.value;
      const confirm = confirmField.value;
      
      if (password.length >= 6 && password === confirm) {
        confirmField.classList.remove('border-red-300');
        confirmField.classList.add('border-green-300');
        submitButton.disabled = false;
        submitButton.classList.remove('opacity-50', 'cursor-not-allowed');
      } else if (confirm.length > 0) {
        confirmField.classList.remove('border-green-300');
        confirmField.classList.add('border-red-300');
        submitButton.disabled = true;
        submitButton.classList.add('opacity-50', 'cursor-not-allowed');
      }
    }
    
    passwordField.addEventListener('input', validatePasswords);
    confirmField.addEventListener('input', validatePasswords);
  });
</script>