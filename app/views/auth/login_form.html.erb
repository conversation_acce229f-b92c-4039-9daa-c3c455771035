<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 relative overflow-hidden flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
  <!-- Background Pattern -->
  <div class="absolute inset-0 bg-grid-slate-100/50 [mask-image:linear-gradient(0deg,transparent,black)] pointer-events-none"></div>
  <div class="absolute top-0 right-0 w-96 h-96 bg-gradient-to-l from-purple-300/20 to-pink-300/20 rounded-full blur-3xl"></div>
  <div class="absolute bottom-0 left-0 w-96 h-96 bg-gradient-to-r from-blue-300/20 to-cyan-300/20 rounded-full blur-3xl"></div>

  <div class="relative z-10 max-w-md w-full">
    <!-- Logo and Header -->
    <div class="text-center mb-8">
      <div class="mx-auto h-20 w-20 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-3xl flex items-center justify-center mb-6 shadow-2xl shadow-blue-500/25">
        <svg class="h-10 w-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
        </svg>
      </div>
      <h2 class="text-4xl font-bold bg-gradient-to-r from-gray-900 via-gray-800 to-gray-700 bg-clip-text text-transparent mb-3">Welcome to Ofie</h2>
      <p class="text-lg text-gray-600 font-medium">Sign in to access your property dashboard</p>
    </div>

    <!-- Login Form -->
    <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-8">
      <%= form_with url: login_path, method: :post, local: true, data: { turbo: false }, class: "space-y-6" do |form| %>
        <div class="space-y-6">
          <div class="space-y-2">
            <%= form.label :email, class: "block text-sm font-bold text-gray-700" do %>
              <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"></path>
              </svg>
              Email Address
            <% end %>
            <%= form.email_field :email, autocomplete: "email", required: true,
                class: "w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 font-medium placeholder-gray-400 hover:border-gray-300",
                placeholder: "Enter your email address" %>
          </div>

          <div class="space-y-2">
            <%= form.label :password, class: "block text-sm font-bold text-gray-700" do %>
              <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
              </svg>
              Password
            <% end %>
            <%= form.password_field :password, autocomplete: "current-password", required: true,
                class: "w-full px-4 py-3 bg-white border-2 border-gray-200 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300 font-medium placeholder-gray-400 hover:border-gray-300",
                placeholder: "Enter your password" %>
          </div>
        </div>

        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <input type="checkbox" id="remember_me" name="remember" class="h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-300 rounded-lg">
            <label for="remember_me" class="ml-3 block text-sm font-medium text-gray-700">Remember me</label>
          </div>
          <div class="text-sm">
            <%= link_to forgot_password_path, class: "font-bold text-blue-600 hover:text-blue-700 transition-colors duration-200" do %>
              Forgot your password?
            <% end %>
          </div>
        </div>

        <div>
          <%= form.submit "Sign In", class: "group w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white py-4 px-6 rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" %>
        </div>

        <div class="text-center">
          <span class="text-gray-600 font-medium">Don't have an account?</span>
          <%= link_to "Sign up here", register_path, class: "font-bold text-blue-600 hover:text-blue-700 ml-1 transition-colors duration-200" %>
        </div>
      <% end %>
    </div>

    <!-- Additional Info -->
    <div class="text-center mt-8">
      <p class="text-sm text-gray-500">
        By signing in, you agree to our
        <a href="#" class="text-blue-600 hover:text-blue-700 font-medium transition-colors duration-200">Terms of Service</a> and
        <a href="#" class="text-blue-600 hover:text-blue-700 font-medium transition-colors duration-200">Privacy Policy</a>
      </p>
    </div>
  </div>
</div>

<style>
  .bg-grid-slate-100\/50 {
    background-image: url("data:image/svg+xml,%3csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3e%3cg fill='%23f1f5f9' fill-opacity='0.3' fill-rule='evenodd'%3e%3cpath d='m0 40 40-40h-40v40zm40 0v-40h-40z'/%3e%3c/g%3e%3c/svg%3e");
  }
</style>