# AI-generated code: Test fixtures for Bot model

primary_bot:
  id: <%= SecureRandom.uuid %>
  name: "Ofie Assistant"
  email: "<EMAIL>"
  password_digest: <%= BCrypt::Password.create('password123') %>
  role: "bot"
  email_verified: true
  active: true
  created_at: <%= 1.week.ago %>
  updated_at: <%= 1.day.ago %>

secondary_bot:
  id: <%= SecureRandom.uuid %>
  name: "Support Bot"
  email: "<EMAIL>"
  password_digest: <%= BCrypt::Password.create('password123') %>
  role: "bot"
  email_verified: true
  active: true
  created_at: <%= 1.week.ago %>
  updated_at: <%= 1.day.ago %>

inactive_bot:
  id: <%= SecureRandom.uuid %>
  name: "Inactive Bot"
  email: "<EMAIL>"
  password_digest: <%= BCrypt::Password.create('password123') %>
  role: "bot"
  email_verified: true
  active: false
  created_at: <%= 2.weeks.ago %>
  updated_at: <%= 1.week.ago %>