# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

notification_one:
  user: tenant
  title: "New Message"
  message: "You have a new message from <PERSON>"
  notification_type: message
  read: false
  created_at: <%= 1.hour.ago %>
  updated_at: <%= 1.hour.ago %>

notification_two:
  user: landlord
  title: "Property Inquiry"
  message: "<PERSON> is interested in your property"
  notification_type: property_inquiry
  read: true
  created_at: <%= 2.hours.ago %>
  updated_at: <%= 1.hour.ago %>