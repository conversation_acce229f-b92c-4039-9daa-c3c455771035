# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

tenant:
  email: <EMAIL>
  password_digest: <%= BCrypt::Password.create('password123') %>
  role: tenant
  name: <PERSON>
  email_verified: true
  created_at: <%= 1.week.ago %>
  updated_at: <%= 1.day.ago %>

landlord:
  email: <EMAIL>
  password_digest: <%= BCrypt::Password.create('password123') %>
  role: landlord
  name: <PERSON>
  email_verified: true
  created_at: <%= 2.weeks.ago %>
  updated_at: <%= 1.day.ago %>

another_tenant:
  email: <EMAIL>
  password_digest: <%= BCrypt::Password.create('password123') %>
  role: tenant
  name: <PERSON>
  email_verified: true
  created_at: <%= 1.week.ago %>
  updated_at: <%= 1.day.ago %>

another_landlord:
  email: <EMAIL>
  password_digest: <%= BCrypt::Password.create('password123') %>
  role: landlord
  name: <PERSON>
  email_verified: true
  created_at: <%= 2.weeks.ago %>
  updated_at: <%= 1.day.ago %>

contractor:
  email: <EMAIL>
  password_digest: <%= BCrypt::Password.create('password123') %>
  role: landlord
  name: <PERSON> Contractor
  email_verified: true
  created_at: <%= 2.weeks.ago %>
  updated_at: <%= 1.day.ago %>
