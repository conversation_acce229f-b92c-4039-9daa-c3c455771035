# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

viewing_one:
  user: tenant
  property: property_one
  scheduled_at: <%= 1.day.from_now %>
  status: 1
  viewing_type: 0
  notes: "Looking forward to seeing the apartment"
  contact_phone: "************"
  contact_email: "<EMAIL>"
  created_at: <%= 1.day.ago %>
  updated_at: <%= 1.day.ago %>

viewing_two:
  user: another_tenant
  property: property_two
  scheduled_at: <%= 2.days.from_now %>
  status: 1
  viewing_type: 1
  notes: "Interested in the garden area"
  contact_phone: "************"
  contact_email: "<EMAIL>"
  created_at: <%= 2.days.ago %>
  updated_at: <%= 2.days.ago %>