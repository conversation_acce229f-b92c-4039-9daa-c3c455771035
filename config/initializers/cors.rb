# Be sure to restart your server when you modify this file.

# Avoid CORS issues when API is called from the frontend app.
# Handle Cross-Origin Resource Sharing (CORS) in order to accept cross-origin Ajax requests.

# Read more: https://github.com/cyu/rack-cors

Rails.application.config.middleware.insert_before 0, Rack::Cors do
  allow do
    # Allow requests from localhost for development
    origins "localhost:3000", "localhost:3001", "127.0.0.1:3000", "127.0.0.1:3001"

    resource "*",
      headers: :any,
      methods: [ :get, :post, :put, :patch, :delete, :options, :head ],
      credentials: true
  end

  # Allow API requests from any origin in development
  allow do
    origins "*" if Rails.env.development?

    resource "/api/*",
      headers: :any,
      methods: [ :get, :post, :put, :patch, :delete, :options, :head ],
      credentials: false
  end

  # Specific configuration for OAuth callbacks
  allow do
    origins "accounts.google.com", "www.facebook.com", "facebook.com"

    resource "/api/v1/auth/*",
      headers: :any,
      methods: [ :get, :post, :options ],
      credentials: true
  end
end
