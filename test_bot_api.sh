#!/bin/bash

# Test script for bot API endpoint
echo "Testing Bot API Endpoint..."
echo "=========================="

# Test 1: Simple greeting
echo -e "\n1. Testing greeting:"
curl -X POST http://localhost:3000/api/v1/bot/chat \
  -H "Content-Type: application/json" \
  -d '{"query": "hi"}' \
  2>/dev/null | jq '.' || echo "Failed to connect or parse JSON"

# Test 2: Property search
echo -e "\n2. Testing property search:"
curl -X POST http://localhost:3000/api/v1/bot/chat \
  -H "Content-Type: application/json" \
  -d '{"query": "find properties"}' \
  2>/dev/null | jq '.' || echo "Failed to connect or parse JSON"

# Test 3: How it works
echo -e "\n3. Testing how it works:"
curl -X POST http://localhost:3000/api/v1/bot/chat \
  -H "Content-Type: application/json" \
  -d '{"query": "how does this work?"}' \
  2>/dev/null | jq '.' || echo "Failed to connect or parse JSON"

echo -e "\nTest completed!"
