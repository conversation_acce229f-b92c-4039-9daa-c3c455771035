GEM
  remote: https://rubygems.org/
  specs:
    actioncable (8.0.2)
      actionpack (= 8.0.2)
      activesupport (= 8.0.2)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (8.0.2)
      actionpack (= 8.0.2)
      activejob (= 8.0.2)
      activerecord (= 8.0.2)
      activestorage (= 8.0.2)
      activesupport (= 8.0.2)
      mail (>= 2.8.0)
    actionmailer (8.0.2)
      actionpack (= 8.0.2)
      actionview (= 8.0.2)
      activejob (= 8.0.2)
      activesupport (= 8.0.2)
      mail (>= 2.8.0)
      rails-dom-testing (~> 2.2)
    actionpack (8.0.2)
      actionview (= 8.0.2)
      activesupport (= 8.0.2)
      nokogiri (>= 1.8.5)
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
      useragent (~> 0.16)
    actiontext (8.0.2)
      actionpack (= 8.0.2)
      activerecord (= 8.0.2)
      activestorage (= 8.0.2)
      activesupport (= 8.0.2)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (8.0.2)
      activesupport (= 8.0.2)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activejob (8.0.2)
      activesupport (= 8.0.2)
      globalid (>= 0.3.6)
    activemodel (8.0.2)
      activesupport (= 8.0.2)
    activerecord (8.0.2)
      activemodel (= 8.0.2)
      activesupport (= 8.0.2)
      timeout (>= 0.4.0)
    activestorage (8.0.2)
      actionpack (= 8.0.2)
      activejob (= 8.0.2)
      activerecord (= 8.0.2)
      activesupport (= 8.0.2)
      marcel (~> 1.0)
    activesupport (8.0.2)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
      uri (>= 0.13.1)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    ast (2.4.3)
    base64 (0.2.0)
    bcrypt (3.1.20)
    bcrypt_pbkdf (1.1.1)
    benchmark (0.4.0)
    bigdecimal (3.1.9)
    bootsnap (1.18.6)
      msgpack (~> 1.2)
    brakeman (7.0.2)
      racc
    builder (3.3.0)
    childprocess (5.1.0)
      logger (~> 1.5)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.3)
    crass (1.0.6)
    date (3.4.1)
    debug (1.10.0)
      irb (~> 1.10)
      reline (>= 0.3.8)
    dotenv (3.1.8)
    drb (2.2.3)
    ed25519 (1.4.0)
    erb (5.0.1)
    erubi (1.13.1)
    et-orbi (1.2.11)
      tzinfo
    faraday (2.13.1)
      faraday-net_http (>= 2.0, < 3.5)
      json
      logger
    faraday-net_http (3.4.0)
      net-http (>= 0.5.0)
    ffi (1.17.2-aarch64-linux-gnu)
    ffi (1.17.2-aarch64-linux-musl)
    ffi (1.17.2-arm-linux-gnu)
    ffi (1.17.2-arm-linux-musl)
    ffi (1.17.2-arm64-darwin)
    ffi (1.17.2-x86_64-linux-gnu)
    ffi (1.17.2-x86_64-linux-musl)
    fugit (1.11.1)
      et-orbi (~> 1, >= 1.2.11)
      raabro (~> 1.4)
    globalid (1.2.1)
      activesupport (>= 6.1)
    hashie (5.0.0)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    image_processing (1.14.0)
      mini_magick (>= 4.9.5, < 6)
      ruby-vips (>= 2.0.17, < 3)
    importmap-rails (2.1.0)
      actionpack (>= 6.0.0)
      activesupport (>= 6.0.0)
      railties (>= 6.0.0)
    io-console (0.8.0)
    irb (1.15.2)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jbuilder (2.13.0)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    json (2.12.2)
    jwt (2.10.1)
      base64
    kamal (2.6.1)
      activesupport (>= 7.0)
      base64 (~> 0.2)
      bcrypt_pbkdf (~> 1.0)
      concurrent-ruby (~> 1.2)
      dotenv (~> 3.1)
      ed25519 (~> 1.4)
      net-ssh (~> 7.3)
      sshkit (>= 1.23.0, < 2.0)
      thor (~> 1.3)
      zeitwerk (>= 2.6.18, < 3.0)
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    language_server-protocol (********)
    launchy (3.1.1)
      addressable (~> 2.8)
      childprocess (~> 5.0)
      logger (~> 1.6)
    letter_opener (1.10.0)
      launchy (>= 2.2, < 4)
    lint_roller (1.1.0)
    logger (1.7.0)
    loofah (2.24.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    mini_magick (5.2.0)
      benchmark
      logger
    mini_mime (1.1.5)
    minitest (5.25.5)
    msgpack (1.8.0)
    multi_xml (0.7.2)
      bigdecimal (~> 3.1)
    net-http (0.6.0)
      uri
    net-imap (0.5.8)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-scp (4.1.0)
      net-ssh (>= 2.6.5, < 8.0.0)
    net-sftp (4.0.0)
      net-ssh (>= 5.0.0, < 8.0.0)
    net-smtp (0.5.1)
      net-protocol
    net-ssh (7.3.0)
    nio4r (2.7.4)
    nokogiri (1.18.8-aarch64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.8-aarch64-linux-musl)
      racc (~> 1.4)
    nokogiri (1.18.8-arm-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.8-arm-linux-musl)
      racc (~> 1.4)
    nokogiri (1.18.8-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-linux-gnu)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-linux-musl)
      racc (~> 1.4)
    oauth2 (2.0.11)
      faraday (>= 0.17.3, < 4.0)
      jwt (>= 1.0, < 4.0)
      logger (~> 1.2)
      multi_xml (~> 0.5)
      rack (>= 1.2, < 4)
      snaky_hash (~> 2.0, >= 2.0.3)
      version_gem (>= 1.1.8, < 3)
    omniauth (2.1.3)
      hashie (>= 3.4.6)
      rack (>= 2.2.3)
      rack-protection
    omniauth-facebook (10.0.0)
      bigdecimal
      omniauth-oauth2 (>= 1.2, < 3)
    omniauth-google-oauth2 (1.2.1)
      jwt (>= 2.9.2)
      oauth2 (~> 2.0)
      omniauth (~> 2.0)
      omniauth-oauth2 (~> 1.8)
    omniauth-oauth2 (1.8.0)
      oauth2 (>= 1.4, < 3)
      omniauth (~> 2.0)
    omniauth-rails_csrf_protection (1.0.2)
      actionpack (>= 4.2)
      omniauth (~> 2.0)
    ostruct (0.6.1)
    parallel (1.27.0)
    parser (3.3.8.0)
      ast (~> 2.4.1)
      racc
    pg (1.5.9)
    pp (0.6.2)
      prettyprint
    prettyprint (0.2.0)
    prism (1.4.0)
    psych (5.2.6)
      date
      stringio
    public_suffix (6.0.2)
    puma (6.6.0)
      nio4r (~> 2.0)
    raabro (1.4.0)
    racc (1.8.1)
    rack (3.1.15)
    rack-cors (3.0.0)
      logger
      rack (>= 3.0.14)
    rack-protection (4.1.1)
      base64 (>= 0.1.0)
      logger (>= 1.6.0)
      rack (>= 3.0.0, < 4)
    rack-session (2.1.1)
      base64 (>= 0.1.0)
      rack (>= 3.0.0)
    rack-test (2.2.0)
      rack (>= 1.3)
    rackup (2.2.1)
      rack (>= 3)
    rails (8.0.2)
      actioncable (= 8.0.2)
      actionmailbox (= 8.0.2)
      actionmailer (= 8.0.2)
      actionpack (= 8.0.2)
      actiontext (= 8.0.2)
      actionview (= 8.0.2)
      activejob (= 8.0.2)
      activemodel (= 8.0.2)
      activerecord (= 8.0.2)
      activestorage (= 8.0.2)
      activesupport (= 8.0.2)
      bundler (>= 1.15.0)
      railties (= 8.0.2)
    rails-dom-testing (2.3.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    railties (8.0.2)
      actionpack (= 8.0.2)
      activesupport (= 8.0.2)
      irb (~> 1.13)
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.2.1)
    rdoc (6.14.0)
      erb
      psych (>= 4.0.0)
    regexp_parser (2.10.0)
    reline (0.6.1)
      io-console (~> 0.5)
    rubocop (1.75.8)
      json (~> 2.3)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.44.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.44.1)
      parser (>= *******)
      prism (~> 1.4)
    rubocop-performance (1.25.0)
      lint_roller (~> 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.38.0, < 2.0)
    rubocop-rails (2.32.0)
      activesupport (>= 4.2.0)
      lint_roller (~> 1.1)
      rack (>= 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.44.0, < 2.0)
    rubocop-rails-omakase (1.1.0)
      rubocop (>= 1.72)
      rubocop-performance (>= 1.24)
      rubocop-rails (>= 2.30)
    ruby-progressbar (1.13.0)
    ruby-vips (2.2.3)
      ffi (~> 1.12)
      logger
    securerandom (0.4.1)
    snaky_hash (2.0.3)
      hashie (>= 0.1.0, < 6)
      version_gem (>= 1.1.8, < 3)
    solid_cable (3.0.8)
      actioncable (>= 7.2)
      activejob (>= 7.2)
      activerecord (>= 7.2)
      railties (>= 7.2)
    solid_cache (1.0.7)
      activejob (>= 7.2)
      activerecord (>= 7.2)
      railties (>= 7.2)
    solid_queue (1.1.5)
      activejob (>= 7.1)
      activerecord (>= 7.1)
      concurrent-ruby (>= 1.3.1)
      fugit (~> 1.11.0)
      railties (>= 7.1)
      thor (~> 1.3.1)
    sprockets (4.2.2)
      concurrent-ruby (~> 1.0)
      logger
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.5.2)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      sprockets (>= 3.0.0)
    sshkit (1.24.0)
      base64
      logger
      net-scp (>= 1.1.2)
      net-sftp (>= 2.1.2)
      net-ssh (>= 2.8.0)
      ostruct
    stimulus-rails (1.3.4)
      railties (>= 6.0.0)
    stringio (3.1.7)
    stripe (15.2.0)
    tailwindcss-rails (4.2.3)
      railties (>= 7.0.0)
      tailwindcss-ruby (~> 4.0)
    tailwindcss-ruby (4.1.7)
    tailwindcss-ruby (4.1.7-aarch64-linux-gnu)
    tailwindcss-ruby (4.1.7-aarch64-linux-musl)
    tailwindcss-ruby (4.1.7-arm64-darwin)
    tailwindcss-ruby (4.1.7-x86_64-linux-gnu)
    tailwindcss-ruby (4.1.7-x86_64-linux-musl)
    thor (1.3.2)
    thruster (0.1.13)
    thruster (0.1.13-aarch64-linux)
    thruster (0.1.13-arm64-darwin)
    thruster (0.1.13-x86_64-linux)
    timeout (0.4.3)
    turbo-rails (2.0.13)
      actionpack (>= 7.1.0)
      railties (>= 7.1.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode-display_width (3.1.4)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    uri (1.0.3)
    useragent (0.16.11)
    version_gem (1.1.8)
    websocket-driver (0.8.0)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    zeitwerk (2.7.3)

PLATFORMS
  aarch64-linux
  aarch64-linux-gnu
  aarch64-linux-musl
  arm-linux-gnu
  arm-linux-musl
  arm64-darwin-24
  x86_64-linux
  x86_64-linux-gnu
  x86_64-linux-musl

DEPENDENCIES
  bcrypt (~> 3.1.7)
  bootsnap
  brakeman
  debug
  image_processing (~> 1.2)
  importmap-rails
  jbuilder
  jwt
  kamal
  kaminari (~> 1.2)
  letter_opener
  mail
  omniauth
  omniauth-facebook
  omniauth-google-oauth2
  omniauth-rails_csrf_protection
  pg (~> 1.1)
  puma (>= 5.0)
  rack-cors
  rails (~> 8.0.2)
  rubocop-rails-omakase
  securerandom
  solid_cable
  solid_cache
  solid_queue
  sprockets-rails
  stimulus-rails
  stripe
  tailwindcss-rails
  thruster
  turbo-rails
  tzinfo-data

BUNDLED WITH
   2.6.9
