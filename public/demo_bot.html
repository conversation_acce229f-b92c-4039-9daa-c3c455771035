<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ofie - Beautiful AI Rental Assistant</title>
    
    <!-- Tailwind CSS for the demo page -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- React and React DOM -->
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    
    <!-- Framer Motion for animations -->
    <script src="https://unpkg.com/framer-motion@10/dist/framer-motion.js"></script>
    
    <!-- Bot Styles -->
    <link rel="stylesheet" href="/assets/bot.css">
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON>o', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .hero-pattern {
            background-image: radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                            radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
        }
        
        .property-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .property-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="hero-pattern">
    <!-- Header -->
    <header class="bg-white/10 backdrop-blur-sm border-b border-white/20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div class="flex items-center space-x-2">
                    <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                        </svg>
                    </div>
                    <h1 class="text-2xl font-bold text-white">Ofie</h1>
                </div>
                
                <nav class="hidden md:flex items-center space-x-8">
                    <a href="#" class="text-white/90 hover:text-white transition-colors">Properties</a>
                    <a href="#" class="text-white/90 hover:text-white transition-colors">Search</a>
                    <a href="#" class="text-white/90 hover:text-white transition-colors">About</a>
                    <a href="#" class="bg-white/20 backdrop-blur-sm text-white px-4 py-2 rounded-lg hover:bg-white/30 transition-all">
                        Sign In
                    </a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-5xl md:text-6xl font-bold text-white mb-6">
                Find Your Perfect
                <span class="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                    Rental Home
                </span>
            </h1>
            <p class="text-xl text-white/90 mb-8 max-w-3xl mx-auto">
                Discover amazing properties with our AI-powered assistant. Get personalized recommendations, 
                instant answers, and seamless application support.
            </p>
            
            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <button class="bg-white text-gray-900 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-100 transition-colors shadow-xl">
                    Start Searching
                </button>
                <button class="bg-white/20 backdrop-blur-sm text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-white/30 transition-all border border-white/30">
                    Learn More
                </button>
            </div>
            
            <!-- AI Assistant Preview -->
            <div class="mt-12 text-center">
                <p class="text-white/80 mb-4">👋 Meet your AI rental assistant</p>
                <div class="inline-flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-6 py-3 border border-white/20">
                    <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.847a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456z"></path>
                        </svg>
                    </div>
                    <span class="text-white font-medium">Click the bot in the bottom right to start chatting!</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Properties -->
    <section class="py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold text-white text-center mb-12">Featured Properties</h2>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Property Card 1 -->
                <div class="property-card rounded-2xl overflow-hidden shadow-xl">
                    <div class="h-48 bg-gradient-to-br from-blue-400 to-purple-500 relative">
                        <div class="absolute inset-0 bg-black/20"></div>
                        <div class="absolute top-4 left-4 bg-white/90 backdrop-blur-sm text-gray-900 px-3 py-1 rounded-full text-sm font-medium">
                            $2,400/month
                        </div>
                        <div class="absolute bottom-4 left-4 text-white">
                            <h3 class="text-xl font-bold">Modern Downtown Loft</h3>
                            <p class="text-white/90">Downtown Seattle</p>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="flex items-center space-x-4 text-gray-600 mb-4">
                            <span class="flex items-center space-x-1">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2V7"></path>
                                </svg>
                                <span>2 bed</span>
                            </span>
                            <span class="flex items-center space-x-1">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10v11M20 10v11"></path>
                                </svg>
                                <span>2 bath</span>
                            </span>
                            <span class="flex items-center space-x-1">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l4 4m8-8h4v4m0-4l-4 4m-8 8l-4 4h4v-4m8 4l4-4v4h-4"></path>
                                </svg>
                                <span>1,200 sq ft</span>
                            </span>
                        </div>
                        <p class="text-gray-600 mb-4">
                            Beautiful modern loft with city views, hardwood floors, and high-end appliances.
                        </p>
                        <button class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 rounded-xl font-semibold hover:shadow-lg transition-all">
                            View Details
                        </button>
                    </div>
                </div>

                <!-- Property Card 2 -->
                <div class="property-card rounded-2xl overflow-hidden shadow-xl">
                    <div class="h-48 bg-gradient-to-br from-green-400 to-blue-500 relative">
                        <div class="absolute inset-0 bg-black/20"></div>
                        <div class="absolute top-4 left-4 bg-white/90 backdrop-blur-sm text-gray-900 px-3 py-1 rounded-full text-sm font-medium">
                            $1,800/month
                        </div>
                        <div class="absolute bottom-4 left-4 text-white">
                            <h3 class="text-xl font-bold">Cozy Garden Apartment</h3>
                            <p class="text-white/90">Capitol Hill</p>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="flex items-center space-x-4 text-gray-600 mb-4">
                            <span class="flex items-center space-x-1">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2V7"></path>
                                </svg>
                                <span>1 bed</span>
                            </span>
                            <span class="flex items-center space-x-1">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10v11M20 10v11"></path>
                                </svg>
                                <span>1 bath</span>
                            </span>
                            <span class="flex items-center space-x-1">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l4 4m8-8h4v4m0-4l-4 4m-8 8l-4 4h4v-4m8 4l4-4v4h-4"></path>
                                </svg>
                                <span>850 sq ft</span>
                            </span>
                        </div>
                        <p class="text-gray-600 mb-4">
                            Charming apartment with private garden access, perfect for plant lovers and pets.
                        </p>
                        <button class="w-full bg-gradient-to-r from-green-500 to-blue-600 text-white py-3 rounded-xl font-semibold hover:shadow-lg transition-all">
                            View Details
                        </button>
                    </div>
                </div>

                <!-- Property Card 3 -->
                <div class="property-card rounded-2xl overflow-hidden shadow-xl">
                    <div class="h-48 bg-gradient-to-br from-purple-400 to-pink-500 relative">
                        <div class="absolute inset-0 bg-black/20"></div>
                        <div class="absolute top-4 left-4 bg-white/90 backdrop-blur-sm text-gray-900 px-3 py-1 rounded-full text-sm font-medium">
                            $3,200/month
                        </div>
                        <div class="absolute bottom-4 left-4 text-white">
                            <h3 class="text-xl font-bold">Luxury Waterfront Condo</h3>
                            <p class="text-white/90">Belltown</p>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="flex items-center space-x-4 text-gray-600 mb-4">
                            <span class="flex items-center space-x-1">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2V7"></path>
                                </svg>
                                <span>3 bed</span>
                            </span>
                            <span class="flex items-center space-x-1">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10v11M20 10v11"></path>
                                </svg>
                                <span>2 bath</span>
                            </span>
                            <span class="flex items-center space-x-1">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l4 4m8-8h4v4m0-4l-4 4m-8 8l-4 4h4v-4m8 4l4-4v4h-4"></path>
                                </svg>
                                <span>1,800 sq ft</span>
                            </span>
                        </div>
                        <p class="text-gray-600 mb-4">
                            Stunning waterfront views, premium finishes, gym and pool access included.
                        </p>
                        <button class="w-full bg-gradient-to-r from-purple-500 to-pink-600 text-white py-3 rounded-xl font-semibold hover:shadow-lg transition-all">
                            View Details
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-white mb-4">Why Choose Ofie?</h2>
                <p class="text-white/90 text-lg">Experience the future of rental property search with our AI-powered platform</p>
            </div>
            
            <div class="grid md:grid-cols-3 gap-8">
                <div class="property-card rounded-2xl p-8 text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.847a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">AI-Powered Assistant</h3>
                    <p class="text-gray-600">Get instant answers, personalized recommendations, and 24/7 support from our intelligent bot.</p>
                </div>
                
                <div class="property-card rounded-2xl p-8 text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Smart Search</h3>
                    <p class="text-gray-600">Advanced filters and AI recommendations to find properties that match your exact needs and preferences.</p>
                </div>
                
                <div class="property-card rounded-2xl p-8 text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Verified Properties</h3>
                    <p class="text-gray-600">All listings are verified and up-to-date. Apply with confidence knowing the information is accurate.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Bot Integration Container -->
    <div id="ofie-bot-root"></div>

    <!-- Bot Integration Script -->
    <script type="module">
        // Sample user data (in a real app, this would come from your authentication system)
        const currentUser = {
            id: 1,
            name: "John Doe",
            email: "<EMAIL>",
            role: "tenant",
            avatar: null,
            email_verified: true
        };

        // Initialize the Ofie Bot
        function initializeBot() {
            // Check if BotIntegration component is loaded
            if (typeof window.BotIntegration !== 'undefined') {
                window.initializeOfieBot({
                    containerId: 'ofie-bot-root',
                    user: currentUser,
                    apiBaseUrl: '/api/v1',
                    theme: 'default',
                    enabled: true
                });
            } else {
                // Retry in 500ms if components aren't loaded yet
                setTimeout(initializeBot, 500);
            }
        }

        // Initialize when page loads
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeBot);
        } else {
            initializeBot();
        }
    </script>

    <!-- Load Bot Components (In a real Rails app, these would be compiled by the asset pipeline) -->
    <script type="module">
        // In a real Rails application, you would load these as compiled modules
        // For demo purposes, we'll simulate the bot being ready
        
        // Simulate bot loading
        setTimeout(() => {
            // Create a simplified bot button for demo
            const botContainer = document.getElementById('ofie-bot-root');
            
            const botHTML = `
                <div class="fixed bottom-6 right-6 z-50">
                    <div id="bot-chat" class="absolute bottom-20 right-0 w-96 h-96 bg-white rounded-2xl shadow-2xl border border-gray-200 hidden">
                        <div class="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-700 text-white px-4 py-3 rounded-t-2xl relative overflow-hidden">
                            <div class="relative flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <div class="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center border border-white/30">
                                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.847a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h3 class="font-semibold text-lg">Ofie Assistant</h3>
                                        <div class="flex items-center space-x-2 text-sm text-white/90">
                                            <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                                            <span>Online</span>
                                        </div>
                                    </div>
                                </div>
                                <button onclick="toggleBot()" class="p-2 rounded-full hover:bg-white/20 transition-colors">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        <div class="p-4 h-64 overflow-y-auto bg-gradient-to-b from-gray-50 to-white">
                            <div class="space-y-4">
                                <div class="flex items-start space-x-3">
                                    <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.847a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456z"></path>
                                        </svg>
                                    </div>
                                    <div class="bg-white border border-gray-200 rounded-2xl rounded-tl-md px-4 py-3 shadow-sm max-w-xs">
                                        <p class="text-sm text-gray-800">👋 Hi John! I'm your AI rental assistant. I can help you find properties, understand the application process, or answer any questions about rentals!</p>
                                    </div>
                                </div>
                                <div class="space-y-2">
                                    <div class="text-xs text-gray-500 text-center">Quick suggestions:</div>
                                    <button onclick="sendMessage('Find me a 2-bedroom apartment under $2500')" class="w-full text-left px-4 py-2 bg-blue-50 hover:bg-blue-100 border border-blue-200 rounded-lg text-sm text-gray-700 transition-colors">
                                        🏠 Find me a 2-bedroom apartment under $2500
                                    </button>
                                    <button onclick="sendMessage('What documents do I need for an application?')" class="w-full text-left px-4 py-2 bg-purple-50 hover:bg-purple-100 border border-purple-200 rounded-lg text-sm text-gray-700 transition-colors">
                                        📋 What documents do I need for an application?
                                    </button>
                                    <button onclick="sendMessage('Tell me about the rental process')" class="w-full text-left px-4 py-2 bg-green-50 hover:bg-green-100 border border-green-200 rounded-lg text-sm text-gray-700 transition-colors">
                                        ✅ Tell me about the rental process
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="border-t border-gray-200 p-4">
                            <div class="flex space-x-2">
                                <input type="text" placeholder="Ask me anything..." class="flex-1 border border-gray-300 rounded-xl px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <button class="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-2 rounded-xl hover:shadow-lg transition-all">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <button id="bot-button" onclick="toggleBot()" class="w-16 h-16 bg-gradient-to-br from-blue-500 via-purple-600 to-blue-700 rounded-full shadow-2xl hover:shadow-blue-500/25 transition-all duration-300 flex items-center justify-center hover:scale-110 hover:-translate-y-1 relative group">
                        <svg class="w-8 h-8 text-white group-hover:animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.847a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456z"></path>
                        </svg>
                        <div class="absolute -top-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-2 border-white">
                            <div class="w-full h-full bg-green-400 rounded-full animate-ping"></div>
                        </div>
                    </button>
                </div>
            `;
            
            botContainer.innerHTML = botHTML;
            
            // Show a welcome bubble after 2 seconds
            setTimeout(() => {
                const welcomeHTML = `
                    <div id="welcome-bubble" class="absolute bottom-full right-0 mb-4 max-w-xs">
                        <div class="bg-white rounded-2xl rounded-br-md shadow-xl border border-gray-200 p-4 relative animate-bounce">
                            <div class="absolute bottom-0 right-4 w-0 h-0 border-l-[12px] border-l-transparent border-r-[12px] border-r-transparent border-t-[12px] border-t-white transform translate-y-full"></div>
                            <div class="flex items-start space-x-3">
                                <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.847a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456z"></path>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <p class="text-sm text-gray-800 mb-2">👋 Hi! I'm here to help you find the perfect rental. Click to start chatting!</p>
                                    <button onclick="toggleBot(); document.getElementById('welcome-bubble').remove();" class="text-xs text-blue-600 hover:text-blue-800 font-medium">
                                        Let's chat →
                                    </button>
                                </div>
                            </div>
                            <button onclick="document.getElementById('welcome-bubble').remove();" class="absolute top-2 right-2 w-6 h-6 flex items-center justify-center text-gray-400 hover:text-gray-600">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                `;
                
                document.querySelector('#ofie-bot-root > div').insertAdjacentHTML('beforeend', welcomeHTML);
            }, 2000);
        }, 1000);
        
        // Bot functionality
        window.toggleBot = function() {
            const chatElement = document.getElementById('bot-chat');
            chatElement.classList.toggle('hidden');
            
            // Remove welcome bubble when opening chat
            const welcomeBubble = document.getElementById('welcome-bubble');
            if (welcomeBubble) {
                welcomeBubble.remove();
            }
        };
        
        window.sendMessage = function(message) {
            console.log('Sending message:', message);
            // In a real app, this would send the message to your bot API
            alert('Demo message: ' + message + '\n\nIn the real app, this would start an intelligent conversation with our AI assistant!');
        };
    </script>
</body>
</html>