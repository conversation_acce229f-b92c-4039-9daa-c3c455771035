#!/usr/bin/env ruby

# Simple test script to verify bot response logic
class BotTester
  def handle_guest_query(query)
    query_lower = query.downcase.strip

    # Greeting responses
    return "Hi! Welcome to Ofie! 👋\n\nI'm here to help you find your perfect rental property. You can browse our listings, learn about our services, or create an account to get started.\n\nWhat would you like to know?" if query_lower.match?(/\b(hi|hello|hey|greetings)\b/)

    # Property search related
    return "You can browse our available properties right here on the site! Use our search filters to find properties by location, price range, number of bedrooms, and amenities. Would you like me to guide you to the properties page?" if query_lower.match?(/\b(property|properties|apartment|house|rental|search|find)\b/)

    # How it works
    return "Ofie makes renting simple! Here's how it works:\n\n• Browse verified property listings\n• Apply online with digital documents\n• Schedule virtual or in-person viewings\n• Communicate directly with landlords\n• Manage payments and maintenance requests\n\nWant to get started? Create a free account!" if query_lower.match?(/\b(how.*work|what.*do|services|platform)\b/)

    # Application process
    return "Our rental application process is streamlined:\n\n1. Find a property you love\n2. Submit your application online\n3. Upload required documents\n4. Wait for landlord approval\n5. Sign your lease digitally\n\nTo apply for properties, you'll need to create an account first. Would you like me to help you get started?" if query_lower.match?(/\b(apply|application|documents|requirements)\b/)

    # Pricing/costs
    return "Ofie is free for renters! You can browse properties, apply for rentals, and use our platform features at no cost. Landlords pay a small fee to list their properties.\n\nReady to start your search?" if query_lower.match?(/\b(cost|price|fee|free|money|pay)\b/)

    # Account/signup
    return "Creating an account is quick and easy! With an account you can:\n\n• Save favorite properties\n• Submit rental applications\n• Message landlords directly\n• Track application status\n• Manage your rental journey\n\nClick the 'Sign Up' button to get started!" if query_lower.match?(/\b(account|sign.*up|register|join|create)\b/)

    # Contact/support
    return "I'm here to help! For additional support, you can:\n\n• Use this chat for quick questions\n• Visit our Help Center\n• Contact our support team\n• Email us directly\n\nWhat specific question can I help you with?" if query_lower.match?(/\b(help|support|contact|question)\b/)

    # Default response
    "I'd be happy to help you with information about Ofie! I can tell you about:\n\n• How our platform works\n• Browsing and searching properties\n• The rental application process\n• Creating an account\n• Getting support\n\nWhat would you like to know more about?"
  end

  def test_queries
    test_cases = [
      "hi",
      "hello",
      "how does this work?",
      "find properties",
      "how do I apply?",
      "is it free?",
      "create account",
      "help",
      "random question"
    ]

    puts "Testing Bot Responses:"
    puts "=" * 50

    test_cases.each do |query|
      puts "\nQuery: '#{query}'"
      puts "-" * 30
      response = handle_guest_query(query)
      puts response
      puts "\n" + "=" * 50
    end
  end
end

# Run the test
if __FILE__ == $0
  tester = BotTester.new
  tester.test_queries
end
