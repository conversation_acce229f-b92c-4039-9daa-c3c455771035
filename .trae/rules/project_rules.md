## Example `project_rules.md` for a Ruby on Rails Rental Application

This document outlines the coding, architectural, and collaboration rules for developing a rental application using Ruby on Rails, Hotwire, TailwindCSS, and PostgreSQL. It is intended for both human developers and AI agents contributing to the project.

---

### **1. Project Stack and Setup**

- **Framework:** Ruby on Rails 8+
- **Frontend:** Hotwire (Turbo + Stimulus)
- **Styling:** TailwindCSS (via tailwindcss-rails gem)
- **Database:** PostgreSQL
- **Other:** Use Rails generators with `--css tailwind --database=postgresql` when scaffolding new apps or features[1][3][4].

---

### **2. Coding Conventions**

- **Follow Rails "Convention over Configuration"**: Use standard Rails naming and directory structures for models, controllers, and views[2].
- **Controllers:** Keep controllers thin. Business logic belongs in models or service objects.
- **Models:** Use "fat models"—encapsulate validations, associations, and business logic here[2].
- **Views:** Use partials and helpers to keep views DRY. Leverage Turbo Frames for dynamic updates.
- **Routes:** Use resourceful routing (e.g., `resources :listings`). Avoid deeply nested routes[2].
- **Database:** Use migrations for all schema changes. Tables should be snake_case and plural (e.g., `rental_units`). Models are singular (e.g., `RentalUnit`)[2].
- **Validations:** Always validate user input at the model level (e.g., `validates :name, presence: true`)[2].
- **Strong Parameters:** Use strong parameter filtering in all create/update actions to prevent mass assignment vulnerabilities[2].

---

### **3. Hotwire & TailwindCSS Usage**

- **Hotwire:** Use Turbo Frames and Streams for real-time UI updates. Prefer Turbo over custom JS unless necessary.
- **TailwindCSS:** Use utility classes for styling. Do not write custom CSS unless a design cannot be achieved with Tailwind[4][5].
- **Componentization:** Break complex UI into reusable partials or view components.
- **Modals:** Implement modals using Turbo Frames and Tailwind for styling[5].

---

### **4. RESTful API and CRUD Practices**

- **Actions:** Use standard RESTful actions: index, show, new, create, edit, update, destroy[2].
- **HTTP Status Codes:** Return appropriate codes (200 for OK, 201 for Created, 204 for No Content, 400 for Bad Request, 404 for Not Found, 500 for errors)[2].
- **Pagination:** Use gems like Kaminari or Pagy for paginating large collections[2].
- **Caching:** Implement caching for read-heavy endpoints.

---

### **5. Database and Data Integrity**

- **Migrations:** All schema changes must go through Rails migrations and be reviewed.
- **Timestamps:** Use `created_at` and `updated_at` columns for all tables[2].
- **Indexes:** Add indexes for foreign keys and frequently queried columns.

---

### **6. Collaboration and Workflow**

- **Version Control:** All code must be committed to Git. Use feature branches and submit pull requests for review.
- **Commits:** Write clear, descriptive commit messages.
- **Code Reviews:** All changes must be peer-reviewed before merging.
- **Testing:** Write model, controller, and system tests for all features and bug fixes.
- **Documentation:** Update README and relevant docs for any new features or architectural changes.

---

### **7. Security and Performance**

- **Authentication:** Use Devise or similar for user authentication.
- **Authorization:** Use Pundit or similar for access control.
- **Background Jobs:** Use Solid Job for long-running or background tasks.
- **Query Optimization:** Avoid N+1 queries by eager loading associations as needed[2].
- **Sensitive Data:** Never commit secrets or credentials to version control.

---

### **8. AI Agent-Specific Rules**

- **Adhere to all above conventions and best practices.**
- **Code Generation:** When generating code, follow Rails and Tailwind conventions strictly.
- **Comments:** All AI-generated code must include a comment indicating it was generated by an AI agent.
- **Testing:** AI agents must generate tests for any new code.
- **Pull Requests:** AI-generated contributions must pass all CI checks before review.

---

### **9. Miscellaneous**

- **Asset Management:** Use the Rails asset pipeline for all static assets.
- **Environment Management:** Use Rails build in credentials for environment variables.
- **Accessibility:** Strive for accessible UI components (ARIA labels, keyboard navigation).

---

> “Let the framework do the heavy lifting. Focus on features, not configurations.”[2]

---

This `project_rules.md` should be reviewed and updated as the project evolves. All contributors (human or AI) are expected to comply.
