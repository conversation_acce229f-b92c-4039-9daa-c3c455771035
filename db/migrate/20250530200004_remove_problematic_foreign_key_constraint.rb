class RemoveProblematicForeignKeyConstraint < ActiveRecord::Migration[8.0]
  def up
    # The error mentions constraint "fk_rails_7e3f5c0824"
    # Let's try to remove this specific constraint if it exists
    
    begin
      # Try to find and remove the problematic constraint
      execute "ALTER TABLE IF EXISTS notifications DROP CONSTRAINT IF EXISTS fk_rails_7e3f5c0824;"
    rescue => e
      puts "Could not remove constraint fk_rails_7e3f5c0824 from notifications: #{e.message}"
    end
    
    # Check other tables that might have this constraint
    %w[property_favorites property_viewings property_reviews payment_methods payments 
       rental_applications lease_agreements maintenance_requests property_comments comment_likes].each do |table_name|
      begin
        execute "ALTER TABLE IF EXISTS #{table_name} DROP CONSTRAINT IF EXISTS fk_rails_7e3f5c0824;"
      rescue => e
        puts "Could not remove constraint fk_rails_7e3f5c0824 from #{table_name}: #{e.message}"
      end
    end
    
    # Now let's ensure all user_id columns are properly typed and have correct foreign keys
    
    # Fix notifications table specifically since it's a common culprit
    if table_exists?(:notifications) && column_exists?(:notifications, :user_id)
      # Remove any existing foreign key
      begin
        remove_foreign_key :notifications, :users if foreign_key_exists?(:notifications, :users)
      rescue => e
        puts "No foreign key to remove from notifications: #{e.message}"
      end
      
      # Ensure the column is UUID type
      change_column :notifications, :user_id, :uuid, null: false
      
      # Add the foreign key back with proper type
      add_foreign_key :notifications, :users, column: :user_id
    end
  end
  
  def down
    # This is a fix migration, not easily reversible
    puts "This migration removes problematic constraints and is not reversible"
  end
end
