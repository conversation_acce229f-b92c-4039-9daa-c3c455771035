# Migration to create bot feedback table
class CreateBotFeedback < ActiveRecord::Migration[7.0]
  def change
    create_table :bot_feedbacks do |t|
      t.references :user, null: false, foreign_key: true, index: true
      t.references :message, null: false, foreign_key: true, index: true
      t.string :feedback_type, null: false, index: true
      t.text :details
      t.json :context
      t.timestamps

      t.index :created_at
      t.index [:feedback_type, :created_at]
    end
  end
end