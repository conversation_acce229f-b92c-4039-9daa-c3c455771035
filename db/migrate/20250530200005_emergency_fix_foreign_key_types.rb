class EmergencyFixForeignKeyTypes < ActiveRecord::Migration[8.0]
  def up
    # Emergency fix for foreign key type mismatch
    # We need to fix this before Rails tries to add any foreign key constraints
    
    # First, let's disable foreign key checks temporarily
    execute "SET session_replication_role = replica;"
    
    begin
      # Check and fix each table that might have the issue
      
      # 1. Check notifications table
      if table_exists?(:notifications)
        # Get current column info
        user_id_column = connection.columns(:notifications).find { |col| col.name == 'user_id' }
        if user_id_column && user_id_column.sql_type.downcase.include?('bigint')
          puts "Fixing notifications.user_id: #{user_id_column.sql_type} -> uuid"
          
          # Drop foreign key if exists
          execute "ALTER TABLE notifications DROP CONSTRAINT IF EXISTS fk_rails_7e3f5c0824;"
          execute "ALTER TABLE notifications DROP CONSTRAINT IF EXISTS fk_rails_notifications_users;"
          
          # Change column type
          execute "ALTER TABLE notifications ALTER COLUMN user_id TYPE uuid USING user_id::text::uuid;"
          
          # Add foreign key back
          execute "ALTER TABLE notifications ADD CONSTRAINT fk_rails_notifications_users FOREIGN KEY (user_id) REFERENCES users(id);"
        end
      end
      
      # 2. Check property_favorites table
      if table_exists?(:property_favorites)
        user_id_column = connection.columns(:property_favorites).find { |col| col.name == 'user_id' }
        if user_id_column && user_id_column.sql_type.downcase.include?('bigint')
          puts "Fixing property_favorites.user_id: #{user_id_column.sql_type} -> uuid"
          
          execute "ALTER TABLE property_favorites DROP CONSTRAINT IF EXISTS fk_rails_property_favorites_users;"
          execute "ALTER TABLE property_favorites ALTER COLUMN user_id TYPE uuid USING user_id::text::uuid;"
          execute "ALTER TABLE property_favorites ADD CONSTRAINT fk_rails_property_favorites_users FOREIGN KEY (user_id) REFERENCES users(id);"
        end
      end
      
      # 3. Check property_viewings table
      if table_exists?(:property_viewings)
        user_id_column = connection.columns(:property_viewings).find { |col| col.name == 'user_id' }
        if user_id_column && user_id_column.sql_type.downcase.include?('bigint')
          puts "Fixing property_viewings.user_id: #{user_id_column.sql_type} -> uuid"
          
          execute "ALTER TABLE property_viewings DROP CONSTRAINT IF EXISTS fk_rails_property_viewings_users;"
          execute "ALTER TABLE property_viewings ALTER COLUMN user_id TYPE uuid USING user_id::text::uuid;"
          execute "ALTER TABLE property_viewings ADD CONSTRAINT fk_rails_property_viewings_users FOREIGN KEY (user_id) REFERENCES users(id);"
        end
      end
      
      # 4. Check property_reviews table
      if table_exists?(:property_reviews)
        user_id_column = connection.columns(:property_reviews).find { |col| col.name == 'user_id' }
        if user_id_column && user_id_column.sql_type.downcase.include?('bigint')
          puts "Fixing property_reviews.user_id: #{user_id_column.sql_type} -> uuid"
          
          execute "ALTER TABLE property_reviews DROP CONSTRAINT IF EXISTS fk_rails_property_reviews_users;"
          execute "ALTER TABLE property_reviews ALTER COLUMN user_id TYPE uuid USING user_id::text::uuid;"
          execute "ALTER TABLE property_reviews ADD CONSTRAINT fk_rails_property_reviews_users FOREIGN KEY (user_id) REFERENCES users(id);"
        end
      end
      
      # 5. Check any other tables that might have been created with wrong types
      %w[payment_methods payments rental_applications lease_agreements maintenance_requests].each do |table_name|
        if table_exists?(table_name)
          user_id_column = connection.columns(table_name).find { |col| col.name == 'user_id' }
          if user_id_column && user_id_column.sql_type.downcase.include?('bigint')
            puts "Fixing #{table_name}.user_id: #{user_id_column.sql_type} -> uuid"
            
            execute "ALTER TABLE #{table_name} DROP CONSTRAINT IF EXISTS fk_rails_#{table_name}_users;"
            execute "ALTER TABLE #{table_name} ALTER COLUMN user_id TYPE uuid USING user_id::text::uuid;"
            execute "ALTER TABLE #{table_name} ADD CONSTRAINT fk_rails_#{table_name}_users FOREIGN KEY (user_id) REFERENCES users(id);"
          end
        end
      end
      
    ensure
      # Re-enable foreign key checks
      execute "SET session_replication_role = DEFAULT;"
    end
    
    puts "Foreign key type fixes completed successfully!"
  end
  
  def down
    # This is an emergency fix migration - not reversible
    raise ActiveRecord::IrreversibleMigration, "Emergency fix migration cannot be reversed"
  end
end
