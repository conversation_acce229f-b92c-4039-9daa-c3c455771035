class FixUserIdForeignKeyTypes < ActiveRecord::Migration[8.0]
  def up
    # Fix the specific foreign key constraint issue
    # The error suggests there's a user_id column that's bigint trying to reference users (uuid)

    # Let's check and fix the most likely culprits based on the schema

    # Check notifications table - it might have been created with wrong type
    if column_exists?(:notifications, :user_id)
      current_type = connection.columns(:notifications).find { |col| col.name == 'user_id' }&.sql_type
      if current_type&.include?('bigint')
        puts "Fixing notifications.user_id from bigint to uuid"
        remove_foreign_key :notifications, :users if foreign_key_exists?(:notifications, :users)
        change_column :notifications, :user_id, :uuid
        add_foreign_key :notifications, :users
      end
    end

    # Check any other tables that might have the issue
    tables_with_user_refs = %w[
      property_favorites property_viewings property_reviews
      payment_methods payments rental_applications
      lease_agreements maintenance_requests property_comments comment_likes
    ]

    tables_with_user_refs.each do |table_name|
      next unless table_exists?(table_name)

      # Check user_id column
      if column_exists?(table_name, :user_id)
        current_type = connection.columns(table_name).find { |col| col.name == 'user_id' }&.sql_type
        if current_type&.include?('bigint')
          puts "Fixing #{table_name}.user_id from bigint to uuid"
          remove_foreign_key table_name, :users if foreign_key_exists?(table_name, :users)
          change_column table_name, :user_id, :uuid
          add_foreign_key table_name, :users
        end
      end

      # Check other user reference columns
      %w[landlord_id tenant_id sender_id reviewed_by_id assigned_to_id].each do |col_name|
        if column_exists?(table_name, col_name)
          current_type = connection.columns(table_name).find { |col| col.name == col_name }&.sql_type
          if current_type&.include?('bigint')
            puts "Fixing #{table_name}.#{col_name} from bigint to uuid"
            remove_foreign_key table_name, :users, column: col_name if foreign_key_exists?(table_name, :users, column: col_name)
            change_column table_name, col_name, :uuid
            add_foreign_key table_name, :users, column: col_name
          end
        end
      end
    end
  end

  def down
    # This migration fixes data type mismatches and is not easily reversible
    raise ActiveRecord::IrreversibleMigration, "This migration cannot be reversed automatically"
  end
end
