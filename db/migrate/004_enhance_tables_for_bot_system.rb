# Migration to enhance existing tables for bot functionality
class EnhanceTablesForBotSystem < ActiveRecord::Migration[7.0]
  def change
    # Add preferences column to users table
    add_column :users, :preferences, :json
    add_column :users, :last_seen_at, :datetime
    add_index :users, :last_seen_at
    
    # Add metadata column to messages table
    add_column :messages, :metadata, :json
    add_index :messages, :created_at
    
    # Add metadata column to conversations table
    add_column :conversations, :metadata, :json
    add_index :conversations, :last_message_at
    
    # Add bot-related fields to properties table for recommendations
    add_column :properties, :score, :decimal, precision: 8, scale: 2, default: 0.0
    add_column :properties, :views_count, :integer, default: 0
    add_column :properties, :applications_count, :integer, default: 0
    add_column :properties, :favorites_count, :integer, default: 0
    
    add_index :properties, :score
    add_index :properties, [:score, :created_at]
    add_index :properties, [:city, :property_type]
    add_index :properties, [:price, :bedrooms, :bathrooms]
  end
end