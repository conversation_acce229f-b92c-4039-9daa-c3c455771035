-- Emergency SQL script to fix foreign key type mismatches
-- Run this directly in PostgreSQL before running Rails migrations

-- First, let's see what tables have bigint user_id columns
DO $$
DECLARE
    table_name text;
    column_info record;
BEGIN
    -- Check all tables for user_id columns with wrong types
    FOR table_name IN 
        SELECT tablename FROM pg_tables 
        WHERE schemaname = 'public' 
        AND tablename NOT IN ('schema_migrations', 'ar_internal_metadata')
    LOOP
        -- Check if table has user_id column and what type it is
        FOR column_info IN
            SELECT column_name, data_type, udt_name
            FROM information_schema.columns 
            WHERE table_name = table_name 
            AND column_name = 'user_id'
            AND data_type = 'bigint'
        LOOP
            RAISE NOTICE 'Found bigint user_id in table: %', table_name;
        END LOOP;
    END LOOP;
END $$;

-- Fix notifications table if it has bigint user_id
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'notifications' 
        AND column_name = 'user_id' 
        AND data_type = 'bigint'
    ) THEN
        RAISE NOTICE 'Fixing notifications.user_id from bigint to uuid';
        
        -- Drop the problematic constraint
        ALTER TABLE notifications DROP CONSTRAINT IF EXISTS fk_rails_7e3f5c0824;
        
        -- Change column type (this will fail if there's data that can't be converted)
        ALTER TABLE notifications ALTER COLUMN user_id TYPE uuid USING user_id::text::uuid;
        
        -- Add foreign key back
        ALTER TABLE notifications ADD CONSTRAINT fk_rails_notifications_users 
            FOREIGN KEY (user_id) REFERENCES users(id);
    END IF;
END $$;

-- Fix property_favorites table if needed
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'property_favorites' 
        AND column_name = 'user_id' 
        AND data_type = 'bigint'
    ) THEN
        RAISE NOTICE 'Fixing property_favorites.user_id from bigint to uuid';
        
        ALTER TABLE property_favorites DROP CONSTRAINT IF EXISTS fk_rails_property_favorites_users;
        ALTER TABLE property_favorites ALTER COLUMN user_id TYPE uuid USING user_id::text::uuid;
        ALTER TABLE property_favorites ADD CONSTRAINT fk_rails_property_favorites_users 
            FOREIGN KEY (user_id) REFERENCES users(id);
    END IF;
END $$;

-- Fix property_viewings table if needed
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'property_viewings' 
        AND column_name = 'user_id' 
        AND data_type = 'bigint'
    ) THEN
        RAISE NOTICE 'Fixing property_viewings.user_id from bigint to uuid';
        
        ALTER TABLE property_viewings DROP CONSTRAINT IF EXISTS fk_rails_property_viewings_users;
        ALTER TABLE property_viewings ALTER COLUMN user_id TYPE uuid USING user_id::text::uuid;
        ALTER TABLE property_viewings ADD CONSTRAINT fk_rails_property_viewings_users 
            FOREIGN KEY (user_id) REFERENCES users(id);
    END IF;
END $$;

-- Fix property_reviews table if needed
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'property_reviews' 
        AND column_name = 'user_id' 
        AND data_type = 'bigint'
    ) THEN
        RAISE NOTICE 'Fixing property_reviews.user_id from bigint to uuid';
        
        ALTER TABLE property_reviews DROP CONSTRAINT IF EXISTS fk_rails_property_reviews_users;
        ALTER TABLE property_reviews ALTER COLUMN user_id TYPE uuid USING user_id::text::uuid;
        ALTER TABLE property_reviews ADD CONSTRAINT fk_rails_property_reviews_users 
            FOREIGN KEY (user_id) REFERENCES users(id);
    END IF;
END $$;

-- Remove any remaining problematic constraints
ALTER TABLE notifications DROP CONSTRAINT IF EXISTS fk_rails_7e3f5c0824;
ALTER TABLE property_favorites DROP CONSTRAINT IF EXISTS fk_rails_7e3f5c0824;
ALTER TABLE property_viewings DROP CONSTRAINT IF EXISTS fk_rails_7e3f5c0824;
ALTER TABLE property_reviews DROP CONSTRAINT IF EXISTS fk_rails_7e3f5c0824;

-- Show final status
SELECT 
    table_name, 
    column_name, 
    data_type,
    udt_name
FROM information_schema.columns 
WHERE column_name = 'user_id' 
AND table_schema = 'public'
ORDER BY table_name;
